import { ACLResource } from '@hetu/acl';
import type { BaseModel, HttpApi } from '@hetu/http';

export interface Message extends BaseModel {
  /** 消息标题 */
  title: string;

  /** 消息内容 */
  content: string;

  avatar: string;
  sender: string;
  senderName: string;
  sendTime: string;

  link?: string;

  /**
   * - `0`: 未读
   * - `1`: 已读 */
  status: 0 | 1;
}

const API_HOST = `/hetu/system/message/notice` as const;

export class MessageApi implements HttpApi {
  /** GET, 未读消息数 */
  unread = `${API_HOST}/unread/count` as const;

  /** POST, 未读消息 */
  page = `${API_HOST}/page` as const;

  /** DELETE, 删除消息 */
  delete = `${API_HOST}/user/delete` as const;

  /** DELETE, 删除全部 */
  deleteAll = `${API_HOST}/user/delete/all` as const;

  /** GET, 设为已读 */
  read = `${API_HOST}/read` as const;

  /** GET, 全部已读 */
  readAll = `${API_HOST}/read/all` as const;
}

export enum MessageACLResourceCode {
  /** 标记为已读 */
  Read = 'read',

  /** 全部标记为已读 */
  ReadAll = 'readAll',

  /** 删除 */
  Delete = 'delete',

  /** 删除全部 */
  DeleteAll = 'deleteAll'
}

export class MessageACLResource extends ACLResource<typeof MessageACLResourceCode> {
  readonly group: string = 'messageNotice';

  constructor() {
    super();
    this.resourceCode = MessageACLResourceCode;
  }
}
