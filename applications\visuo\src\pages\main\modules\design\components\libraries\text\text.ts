import { TextAdapt, useUiStyle, type TextBox } from '@vis/document-core';
import { useDesignStore } from '../../../stores/design.store';
import { defineComponent, ref, nextTick, type PropType, onMounted, computed, watch } from 'vue';
import { useGraph } from '../../../hooks';

/**
 * 文本组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-textbox',
  components: {},
  props: {
    graph: {
      type: Object as PropType<TextBox>
    }
  },
  setup(props) {
    const designStore = useDesignStore();

    const { delGraphs } = useGraph();

    const textbox = computed(() => {
      return props.graph as TextBox;
    });

    const editor = ref(textbox.value.content);
    const editorRef = ref();
    const isEditable = ref(true);
    const selection = window.getSelection();
    const range = document.createRange();

    onMounted(() => {
      if (!editorRef.value) return;

      nextTick(() => {
        if (!editor.value || editor.value === '<br>') {
          editorRef.value.focus();
        }
      });
    });

    const onSelectText = () => {
      isEditable.value = true;
      nextTick(() => {
        editorRef.value.focus();
      });
    };
    const handleEditor = (val: string) => {
      textbox.value.content = val;
    };

    const onFocus = (event: FocusEvent) => {
      const target = event.target as HTMLElement;
      range.selectNodeContents(target);
      range.setStart(target, 0);
      range.setEnd(target, target.childNodes.length);
      selection?.removeAllRanges();
      selection?.addRange(range);
    };

    const onBlur = () => {
      isEditable.value = false;
      selection?.removeAllRanges();
      // 删除空文本框
      if (!editor.value || editor.value === '<br>') {
        delGraphs([textbox.value]);
      }
    };

    const isCaret = computed(() => {
      const graphIds = designStore.active.value.graphIds;
      return (!editor.value || editor.value === '<br>') && graphIds.length === 1 && graphIds[0] === textbox.value.id;
    });

    const { getTextStyle } = useUiStyle();
    const textStyle = computed(() => {
      const style = textbox.value.text ? getTextStyle(textbox.value.text) : {};

      return style;
    });

    /**
     * 计算超出样式
     */
    const adaptStyle = computed(() => {
      if (textbox.value.text.adapt === TextAdapt.Ellipsis) {
        let lineNumber = 1;
        if (textbox.value.height) {
          const { lineHeight = 0, fontSize = 0 } = textbox.value.text;
          const baseHeight = lineHeight || fontSize * 1.5;
          const [top, right, bottom, left] = textbox.value.stroke?.position || [0, 0, 0, 0];

          lineNumber = Math.floor((textbox.value.height - top - bottom) / baseHeight);

          lineNumber = Math.max(lineNumber, 1);
        }
        return isEditable.value
          ? {}
          : {
              display: '-webkit-box',
              webkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              webkitLineClamp: `${lineNumber}`
            };
      } else if (textbox.value.text.adapt === TextAdapt.Single) {
        return {
          whiteSpace: 'nowrap'
        };
      }
      return {};
    });

    /**
     * 切换展示模式后，修改graph的尺寸
     * 字号、行高、字距、缩进等均有可能引起高度变化
     */
    watch(
      [() => textbox.value.text, () => editor.value],
      () => {
        const ele = document.querySelector(`#${textbox.value.id} .q-editor__content`) as HTMLElement;
        if (!ele) return;

        const adapt = textbox.value.text.adapt;
        if (adapt === TextAdapt.Single) {
          editor.value = ele.innerText.replace(/\r?\n|\r/g, ' ');
          handleEditor(editor.value);
        }
        nextTick(() => {
          switch (adapt) {
            case TextAdapt.Single:
            case TextAdapt.Auto: {
              const width = ele.scrollWidth;
              const height = ele.scrollHeight;
              const [top, right, bottom, left] = textbox.value.stroke?.position || [0, 0, 0, 0];

              textbox.value.width = width + left + right;
              textbox.value.height = height + top + bottom;
              break;
            }
            default:
              break;
          }
        });
      },
      { deep: true }
    );

    /**
     * 当文本宽高发生变化时，按照中心点位重新计算偏移
     */
    watch([() => textbox.value?.width, () => textbox.value?.height], (newVal, oldVal) => {
      if (!textbox.value?.transform.translate) return;
      if (!Array.isArray(newVal) || !Array.isArray(oldVal)) return;
      const [newWidth, newHeight] = newVal;
      const [oldWidth, oldHeight] = oldVal;
      if (!newWidth || !newHeight || !oldWidth || !oldHeight || (newWidth === oldWidth && newHeight === oldHeight))
        return;

      let [x, y] = textbox.value.transform.translate;
      x -= (newWidth - oldWidth) / 2;
      y -= (newHeight - oldHeight) / 2;
      textbox.value.transform.translate = [parseInt(`${x}`), parseInt(`${y}`)];
    });

    return {
      editor,
      editorRef,
      isEditable,

      isCaret,

      handleEditor,
      onSelectText,

      onBlur,
      onFocus,

      textStyle,
      adaptStyle
    };
  }
});
