import type { Records } from '@hetu/util';
import type { AxisField } from './data/axis-field';

/**
 * 组件块
 * <AUTHOR>
 */
export class WidgetBlock {
  /** 唯一标识 */
  id = '';

  /** 组件名称 */
  name = '';

  /** 组件类型 */
  type = '';

  /** 数据集id */
  datasetId = '';

  /** 数据集类型 */
  datasetType = '';

  /** 数据映射 */
  dataMapping: Records<AxisField> = {};

  /** 数据刷新方式 auto:自动刷新 control:受控模式 none:不刷新 */
  refreshType: 'auto' | 'control' | 'none' = 'none';

  /** 刷新频率 */
  refreshSecond = 1;

  /** 组件属性 */
  options: any = {};
}
