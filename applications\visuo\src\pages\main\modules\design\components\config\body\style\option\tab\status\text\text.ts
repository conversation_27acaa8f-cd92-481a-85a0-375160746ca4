import { FontConfig, TabText, useDocumentStore, type Font } from '@vis/document-core';
import { computed, defineComponent, type PropType } from 'vue';

/**
 * 文本状态组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-status-text',
  props: {
    statusOption: {
      type: Object as PropType<TabText>,
      required: true
    }
  },
  setup(props) {
    const fontStyle = computed(() => props.statusOption);

    const { fontWeights, fontSizes, fontStyles } = new FontConfig();

    const fontStyleKeys: ('italic' | 'underlined' | 'through')[] = ['italic', 'underlined', 'through'];

    const docStore = useDocumentStore();

    const fontFamilys = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });

    /**
     * 设置文字修饰--斜体、下划线和删除线
     * @param val
     * @returns
     */
    const setFontStyle = (val: 'italic' | 'underlined' | 'through') => {
      if (fontStyle.value[val] === undefined) {
        return;
      }
      fontStyle.value[val] = !fontStyle.value[val];

      // 下划线和删除线不能同时选中
      if (fontStyle.value[val]) {
        if (val === 'underlined') {
          fontStyle.value.through = false;
        } else if (val === 'through') {
          fontStyle.value.underlined = false;
        }
      }
    };

    const isIconFont = (name: string) => {
      return name.startsWith('vis');
    };
    return {
      fontStyle,
      fontFamilys,
      fontWeights,
      fontSizes,
      fontStyles,
      fontStyleKeys,
      setFontStyle,
      isIconFont
    };
  }
});
