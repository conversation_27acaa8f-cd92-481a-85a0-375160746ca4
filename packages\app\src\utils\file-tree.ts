import { isFunction } from 'lodash-es';
import type { FileData, FileInfo } from '../models';
import { OrderType, type QueryOrder } from '@hetu/http';
import { reactive } from 'vue';

/**
 * 文件树抽象类
 * <AUTHOR>
 */
export abstract class AbstractFileTree<T extends K, K extends FileInfo = FileInfo> {
  /**
   * 前置条件
   * @param node 节点对象
   */
  abstract get preconditions(): ((node: FileTreeNode<T, K>) => boolean) | undefined;

  private map = reactive(new Map<string, FileTreeNode<T, K>>()) as Map<string, FileTreeNode<T, K>>;

  private unknows: Map<string, number[]>;

  /** 根节点 */
  private root: FileTreeNode<T, K>;

  /** map 中额外添加的 key 值属性名 */
  private extraKey?: keyof T;

  constructor(data: FileData<T, K>, extraKey?: keyof T) {
    const map = this.map;
    const unReadyParent = new Map<string, number[]>();
    let directorys = [] as FileTreeNode<T>[];
    data.directorys?.forEach((item) => {
      const { id, parentId } = item;
      const node = this.createNode(item);

      if (map.get(id)) {
        // 由下级添加的不完整父级数据
        map.set(id, Object.assign(node, map.get(id)));

        if (unReadyParent.has(id)) {
          const indexs = unReadyParent.get(id) as number[];
          indexs.forEach((i) => {
            directorys[i] = undefined as unknown as FileTreeNode<T>;
          });
          unReadyParent.delete(id);
        }
      } else {
        map.set(id, node);
      }

      extraKey && node[extraKey] && map.set(node[extraKey] as unknown as string, node);

      if (parentId) {
        const unReady = unReadyParent.has(parentId) || !map.get(parentId);
        if (unReady) {
          const index = directorys.push(node);
          const indexs = unReadyParent.get(parentId) || [];
          indexs.push(index - 1);
          unReadyParent.set(parentId, indexs);
        }

        const parent = map.get(parentId) || ({} as FileTreeNode<T, K>);
        parent.directorys = parent.directorys || [];
        parent.directorys.push(node);
        map.set(parentId, parent);
      } else {
        directorys.push(node);
      }
    });

    directorys = directorys.filter((item) => !!item) as FileTreeNode<T>[];
    this.unknows = unReadyParent;

    const files = [] as FileTreeNode<T>[];
    data.files?.forEach((item) => {
      const { id, parentId } = item;
      const node = this.createNode(item);
      map.set(id, node);
      extraKey && node[extraKey] && map.set(node[extraKey] as unknown as string, node);

      if (parentId && !unReadyParent.get(parentId)) {
        const parent = map.get(parentId);
        if (parent) {
          parent.files.push(node);
          return;
        }
      }
      files.push(node);
    });

    this.extraKey = extraKey;
    this.root = reactive(this.createNode({ directorys, files } as unknown as K)) as FileTreeNode<T, K>;
  }

  /**
   * 获取数据中存在的节点
   * @param key id | extraKey 的值
   */
  getFile(key?: string) {
    if (!key) {
      return this.root;
    }
    if (!this.unknows.get(key)) {
      return this.map.get(key);
    }
  }

  /**
   * 根据 key 值获取节点: key 为空时返回根节点
   * @param key id | extraKey 的值
   */
  getNode(key?: string) {
    let node = this.getFile(key);

    if (node) {
      // 处理前置条件
      node = node.filter();
    }

    return node;
  }

  /**
   * 添加节点
   * @param item 节点对象
   */
  addNode(item: K) {
    const { parentId, dataType } = item;
    const node = this.createNode(item);
    const parent = parentId ? this.map.get(parentId) : this.root;
    if (parent) {
      const datas = dataType ? parent.directorys : parent.files;
      datas.push(node);
    }

    this.map.set(node.id, node);
    this.extraKey && node[this.extraKey] && this.map.set(node[this.extraKey] as unknown as string, node);

    return this.map.get(node.id) as typeof node;
  }

  /**
   * 替换节点
   * @param id 替换前节点 ID
   * @param item 节点对象
   */
  replaceNode(id: string, node: FileTreeNode<T, K>) {
    this.map.delete(id);

    this.map.set(node.id, node);
    this.extraKey && node[this.extraKey] && this.map.set(node[this.extraKey] as unknown as string, node);
  }

  /**
   * 移除节点
   * @param id ID
   */
  removeNode(id: string | string[]) {
    const ids = Array.isArray(id) ? id : [id];
    ids.forEach((val) => {
      const node = this.map.get(val) as FileTreeNode<T, K>;
      const { parentId, dataType } = node;
      const parent = parentId && !this.unknows.get(parentId) ? this.map.get(parentId) : this.root;
      if (parent) {
        const tree = dataType ? parent.directorys : parent.files;
        tree.splice(
          tree.findIndex((item) => item.id === val),
          1
        );
      }

      this.map.delete(val);
      this.extraKey && node[this.extraKey] && this.map.delete(node[this.extraKey] as unknown as string);
    });
  }

  private createNode(item: K) {
    const node = item as unknown as FileTreeNode<T, K>;

    if (node.dataType) {
      node.directorys = node.directorys || [];
      node.files = node.files || [];
    }

    const findParents = (file: FileTreeNode<T, K>, mode = 1) => {
      const result = [];
      while (file && file.parentId) {
        const parentId = file.parentId;
        file = this.getFile(parentId) as FileTreeNode<T, K>;
        if (file && this.checkPreconditions(file)) {
          result.push(mode ? file : parentId);
        } else {
          file = undefined as unknown as FileTreeNode<T, K>;
        }
      }
      return result;
    };

    node.getParent = (filter = true) => {
      const { parentId } = node;
      if (parentId) {
        const file = filter ? this.getNode(parentId) : this.getFile(parentId);
        if (file && this.checkPreconditions(file)) {
          return file;
        }
      }
    };

    node.getParents = () => findParents(node) as FileTreeNode<T, K>[];

    node.getParentIds = () => findParents(node, 0) as string[];

    const filter = (current: FileTreeNode<T, K>, condition?: (item: FileTreeNode<T, K>) => boolean) => {
      const exec = this.preconditions || condition;

      if (exec) {
        const copy = Object.assign({}, current);
        const directorys = [] as FileTreeNode<T, K>[];
        const files = [] as FileTreeNode<T, K>[];

        const check = (value: FileTreeNode<T, K>) => {
          if (condition) {
            return condition(value);
          }
          const result = this.checkPreconditions(value);
          if (!result && value.dataType) {
            // 不满足前置条件的文件夹查找其下级节点
            fn(value);
          }
          return result;
        };

        const fn = (target: FileTreeNode<T, K> = current) => {
          const dir = target.directorys?.filter(check);
          const file = target.files?.filter(check);
          dir && directorys.push(...dir);
          file && files.push(...file);
        };

        fn();

        copy.directorys = directorys;
        copy.files = files;

        // 更改范围
        if (this.preconditions && !condition) {
          copy.filter = filter.bind(this, copy);
          copy.search = search.bind(this, copy);
        }
        copy.sort = sort.bind(this, copy);

        return copy;
      }

      return current;
    };
    node.filter = filter.bind(this, node);

    const search = (current: FileTreeNode<T, K>, str: string): FileTreeNode<T, K> & { length?: number } => {
      if (!str) {
        return current;
      }

      const copy = Object.assign({}, current) as typeof current & { length: number };
      const directorys = [] as FileTreeNode<T, K>[];
      const files = [] as FileTreeNode<T, K>[];

      const fn = (target: FileTreeNode<T, K> = current) => {
        target.directorys?.forEach((dir) => {
          this.checkPreconditions(dir) && dir.title.includes(str) && directorys.push(dir);
          fn(dir.filter());
        });
        target.files?.forEach((file) => {
          this.checkPreconditions(file) && file.title.includes(str) && files.push(file);
        });
      };

      fn();

      copy.directorys = directorys;
      copy.files = files;
      copy.length = directorys.length + files.length;

      // 更改范围
      copy.filter = filter.bind(this, copy);
      copy.sort = sort.bind(this, copy);
      return copy;
    };
    node.search = search.bind(this, node);

    const sort = (
      current: FileTreeNode<T, K>,
      compareFn: QueryOrder<keyof T> | ((a: FileTreeNode<T, K>, b: FileTreeNode<T, K>) => number)
    ) => {
      const isFn = isFunction(compareFn);

      let fn: (a: FileTreeNode<T, K>, b: FileTreeNode<T, K>) => number;
      if (!isFn) {
        const { column, sorting } = compareFn as QueryOrder<keyof T>;
        fn = (a, b) => {
          let sortA = a[column] as unknown;
          let sortB = b[column] as unknown;
          if (sortA === sortB) {
            return 0;
          }
          if (['createdTime', 'updateTime'].includes(column as string)) {
            sortA =
              (a as any)['_' + (column as string)] ||
              ((a as any)['_' + (column as string)] = new Date(sortA as string).getTime());
            sortB =
              (b as any)['_' + (column as string)] ||
              ((b as any)['_' + (column as string)] = new Date(sortB as string).getTime());
          }
          sortA = parseInt(sortA as string, 10);
          sortB = parseInt(sortB as string, 10);
          const isAsc = sorting === OrderType.Asc;
          return (sortA as number) > (sortB as number) ? (isAsc ? 1 : -1) : isAsc ? -1 : 1;
        };
      } else {
        fn = compareFn as (a: FileTreeNode<T, K>, b: FileTreeNode<T, K>) => number;
      }

      current.directorys = [...(current.directorys?.sort(fn) ?? [])];
      current.files = [...(current.files?.sort(fn) ?? [])];
      return { ...current };
    };
    node.sort = sort.bind(this, node);

    return node;
  }

  private checkPreconditions(node: FileTreeNode<T, K>) {
    let result = true;
    if (this.preconditions) {
      result = this.preconditions(node);
    }
    return result;
  }
}

/**
 * 文件树
 * <AUTHOR>
 */
export class FileTree<T extends K, K extends FileInfo = FileInfo> extends AbstractFileTree<T, K> {
  preconditions: undefined;
}

export type FileTreeNode<T extends K, K extends FileInfo = FileInfo> = FileTreeNodeExtra<T, K> & T;

export interface FileTreeNodeExtra<T extends K, K extends FileInfo = FileInfo>
  extends FileData<FileTreeNode<T, K>, FileTreeNode<T, K>> {
  /**
   * 获取存在的上级节点
   * @param filter 是否过滤数据
   */
  getParent(filter?: boolean): FileTreeNode<K> | undefined;

  /** 获取所有存在的上级节点 */
  getParents(): FileTreeNode<K>[];

  /** 获取所有存在的上级节点 ID */
  getParentIds(): string[];

  /**
   * 在当前层级中过滤: filter 不能作为 search 的前置条件
   * @param condition 条件
   */
  filter(condition?: (item: FileTreeNode<T, K>) => boolean): FileTreeNode<T, K>;

  /**
   * 在当前层级及所有下级节点中搜索标题
   * @param str 搜索文本
   */
  search(str: string): FileTreeNode<T, K> & { length?: number };

  /**
   * 在当前层级中排序
   * @param compareFn 排序比较函数
   */
  sort(compareFn: QueryOrder<keyof T> | ((a: FileTreeNode<T, K>, b: FileTreeNode<T, K>) => number)): FileTreeNode<T, K>;
}
