/**
 * 数据过滤基础接口
 * <AUTHOR>
 */
// `NOTE`: 请务必保持此为接口，勿妄图重用
export interface DataFilter {
  /** 列名称 */
  columnName: string;

  /** 筛选条件名称 */
  filterName: string;

  /** 唯一值, 仅前端用
   * 1. 当从后端来的数据有 id 值时，需要将 `id` 交给 `key`
   * 2. 当从前端生成的过滤数据时，需要前端生成临时的 `key`
   *
   * *要保证这个属性名称没有被后台处理* */
  key: string;

  /** 唯一值, 仅保存过的数据才拥有, 拿到从后台返回的数据后, 需要将其交给 `key`
   *
   * **NOTE: 对于新生成的数据, 前端请勿给 `id` 赋值** */
  id?: string;
}

/**
 * 数据过滤备选项
 * <AUTHOR>
 */
// `NOTE`: 请务必保持此为接口，勿妄图重用
export interface DataFilterOption {
  /** 值 */
  columnValue: any;

  /** 值个数 */
  columnCount: number;

  /** 值占比 */
  columnRate: number;
}
