<template>
  <div class="ht-app-page__main ht-material-geo-info col column relative q-gutter-y-md">
    <div class="ht-app-page__main-header row items-center">
      <label class="text-h5 col">GeoJson 详情</label>
      <label class="ht-link" @click="$emit('onBack')">
        <q-icon name="arrow_back" size="xs" />
        <span class="q-ml-xs">返回</span>
      </label>
    </div>
    <q-scroll-area class="col">
      <div class="row flex-center">
        <q-form ref="formRef" class="col-xl-6 col-lg-6 col-md-8 col-sm-8 col-xs-10 q-ml-md">
          <div class="q-mb-sm">区域名称</div>
          <q-input
            v-model="formData.name"
            :rules="rules.name"
            lazy-rules
            debounce="1000"
            placeholder="请输入名称"
            no-error-icon
            outlined
            dense
          ></q-input>

          <div class="q-mb-sm">区域编码</div>
          <q-input
            v-model="formData.cityCode"
            :rules="rules.cityCode"
            lazy-rules
            debounce="1000"
            placeholder="请输入编码"
            no-error-icon
            outlined
            dense
          ></q-input>

          <div class="q-mb-sm">文件</div>
          <q-file
            v-model="fileData"
            :display-value="fileName"
            :rules="rules.attachmentId"
            lazy-rules
            debounce="1000"
            accept=".json"
            no-error-icon
            label=""
            outlined
            clearable
            bottom-slots
            @update:model-value="upload"
            @clear="removeFile"
          >
            <template v-slot:prepend>
              <q-icon class="text-primary" name="cloud_upload" />
            </template>
            <template v-slot:label>
              <div class="text-body2">
                将文件拖到此处或<span class="text-primary">点击上传</span>, 只能上传 .json 文件
              </div>
            </template>
          </q-file>

          <div class="q-mb-sm">上级区域</div>
          <q-select
            v-model="parentName"
            class="q-field--with-bottom"
            placeholder="请选择"
            clearable
            outlined
            dense
            @clear="updateParent()"
          >
            <template #no-option>
              <q-scroll-area class="q-pa-sm" style="height: 240px">
                <q-tree
                  v-model:selected="formData.parentId"
                  :nodes="tree"
                  node-key="id"
                  label-key="name"
                  selected-color="primary"
                  :filter="formData.id"
                  :filter-method="filterParent"
                  default-expand-all
                >
                </q-tree>
              </q-scroll-area>
            </template>
          </q-select>

          <div class="q-mb-sm">启用状态</div>
          <q-field class="q-field--with-bottom" borderless dense>
            <q-btn-toggle
              v-model="formData.active"
              class="col ht-btn-group--outline"
              :options="[
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ]"
              toggle-color="primary"
              size="md"
              unelevated
              spread
            />
          </q-field>

          <div class="q-mb-sm">序号</div>
          <q-input
            v-model.number="formData.orderNo"
            class="col q-field--with-bottom"
            type="number"
            :min="0"
            outlined
            dense
          >
          </q-input>
        </q-form>
      </div>
    </q-scroll-area>

    <div v-acl="acl.save" class="ht-app-page__main-footer text-right">
      <q-btn class="ht-btn--outline q-mr-sm" flat @click="reset">重置</q-btn>
      <q-btn color="primary" unelevated :loading="saveing" @click="save">保存</q-btn>
    </div>

    <q-inner-loading :showing="loading">
      <q-spinner color="primary" size="2rem" :thickness="2" />
    </q-inner-loading>
  </div>
</template>

<script src="./info.ts" lang="ts"></script>
