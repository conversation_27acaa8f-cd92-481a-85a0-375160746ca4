import { Graph, GraphType, Frame, useLayout, useGraphStyle, GridItem, CrossAlignment } from '@vis/document-core';
import { useDesignStore } from '../../../stores/design.store';
import { defineComponent, computed, type PropType, watch, nextTick } from 'vue';
import type { Records } from '@hetu/util';
import { debounce } from 'lodash-es';
import { useConstraints } from '../../../hooks';

export default defineComponent({
  name: 'vis-graph-entrance',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Graph>,
      required: true
    },
    parent: {
      type: Object as PropType<Frame>,
      required: false
    }
  },
  setup(props) {
    const designStore = useDesignStore();

    const { isFlex, isFreeform, isGrid, flexItemStyle, freeformStyle, gridItemStyle } = useLayout();
    const { graphClassName, graphComponentName, graphBaseStyle } = useGraphStyle();
    const { setChildrenPosition } = useConstraints();

    const activeGraphIds = computed(() => designStore.active.value.graphIds);
    const activeFrame = computed(() => designStore.active.value.frame);
    const canvasState = computed(() => designStore.canvasState.value);

    // 当前图形
    const graph = computed(() => props.graph);
    // 父级容器
    const parent = computed(() => props.parent);

    const className = computed(() => {
      const name = graphClassName(graph.value);
      if (activeGraphIds.value.includes(graph.value.id)) {
        name.push('active');
      }
      return name.join(' ');
    });

    const componentName = computed(() => graphComponentName(graph.value));

    const style = computed(() => {
      const getStyle = (graph: Graph, parent?: Frame) => {
        let positionStyle: Records<string | number> = {};

        // 说明frame是容器下的子元素
        if (parent) {
          // 父级是自由布局
          if (isFreeform(parent)) {
            // 处理设置约束时的位置
            if (graph.constraints) {
              positionStyle = freeformStyle(graph);
            }
          }
          // 父级是flex布局
          else if (isFlex(parent)) {
            if (graph.ignoreAutoLayout) {
              positionStyle = freeformStyle(graph);
            } else {
              positionStyle = flexItemStyle(graph, parent);
            }
          } else if (isGrid(parent)) {
            if (parent.ignoreAutoLayout) {
              positionStyle = freeformStyle(graph);
            } else {
              // girdItem是右对齐或者上对齐
              const gridItemAlignRB =
                (graph.gridItem as GridItem).justifySelf === CrossAlignment.End ||
                (graph.gridItem as GridItem).alignSelf === CrossAlignment.End;
              if (
                // girdItem移动过程中用相对位置
                canvasState.value.dragging.includes(graph.id) ||
                // girdItem改变尺寸过程中，并且对齐方式是上或右对齐时用相对位置(因为上或右对齐需要改变transform)
                (canvasState.value.resizeing.includes(graph.id) && gridItemAlignRB)
              ) {
                positionStyle = freeformStyle(graph);
              } else {
                positionStyle = gridItemStyle(graph, parent);
              }
            }
          }
        } else {
          positionStyle = freeformStyle(graph);
        }

        // 基础样式
        const baseStyle = graphBaseStyle(graph);

        return { ...positionStyle, ...baseStyle };
      };
      return getStyle(graph.value, parent.value);
    });

    //#region
    // flex、grid布局，布局的属性发生变化后，frame的尺寸要重新计算，frame内的组件的位置、尺寸也要重新计算
    // 位置尺寸正确的情况下，其他图形拖拽到此容器内时计算的位置才准确

    // 子图形监听宽高,重新计算父容器的宽高
    const setParentAndChildrenSize = debounce((frame: Frame, graph: Graph) => {
      if (
        !isFreeform(frame) &&
        // 当前图形忽略了自动布局不需要计算
        !graph.ignoreAutoLayout
      ) {
        const oldFrameWidth = frame.width;
        const oldFrameHeight = frame.height;
        const parentDom = document.querySelector(`[id="${frame.id}"]`) as HTMLElement;

        if (parentDom) {
          frame.width = Math.round(parentDom.clientWidth);
          frame.height = Math.round(parentDom.clientHeight);
        }
        // 在画布中拖拽改变尺寸的图形在onResize事件里处理，这里不调用
        if (!canvasState.value.resizeing.includes(graph.id)) {
          setChildrenPosition(frame, oldFrameWidth, oldFrameHeight);
        }
      }
    }, 300);

    if (parent.value) {
      watch(
        [() => graph.value.width, () => graph.value.height, graph.value.limitSize],
        () => {
          nextTick(() => {
            setParentAndChildrenSize(parent.value as Frame, graph.value);
          });
        },
        {
          deep: true
        }
      );
    }

    //#endregion

    return {
      style,
      className,
      componentName,
      GraphType
    };
  }
});
