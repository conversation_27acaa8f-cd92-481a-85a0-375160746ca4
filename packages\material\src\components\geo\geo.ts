import { defineComponent, ref, type PropType, computed, watch } from 'vue';
import type { Menu } from '@hetu/theme';
import { createTree } from '@hetu/util';
import { useQuasar, QTree } from 'quasar';
import { MaterialGeo } from '../../models';
import { MaterialGeoService } from '../../services';
import HtMaterialGeoInfo from './info/info.vue';

/**
 * 地理坐标系管理
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-material-geo',
  components: { HtMaterialGeoInfo },
  props: {
    /** 当前选中的菜单 */
    activeMenu: {
      type: Object as PropType<Menu>,
      required: true
    }
  },
  setup() {
    const $q = useQuasar();
    const acl = MaterialGeoService.acl;

    /** 列表数据 */
    const list = ref<MaterialGeo[]>([]);
    const loading = ref<boolean>(false);
    /** 获取 geo 列表 */
    const loadData = () => {
      loading.value = true;
      MaterialGeoService.list()
        .then((data) => {
          list.value = data ?? [];
        })
        .finally(() => {
          loading.value = false;
        });
    };
    loadData();

    const tree = computed(() => createTree(list.value, { keepUnParent: true }));
    const treeRef = ref<QTree>();
    const filter = ref('');
    const getParentsLen = (node: MaterialGeo) => tree.value.getParentKeys(node.id).length;

    // 全选状态
    const checkAll = ref(false);
    // 选中列表
    const ticked = ref<string[]>([]);
    watch(
      () => ticked.value.length,
      () => {
        const len = ticked.value.length;
        checkAll.value = !!len && len === list.value.length;
      }
    );
    /** 全选 */
    const selectAll = () => {
      checkAll.value = !checkAll.value;
      ticked.value = checkAll.value ? list.value.map((item) => item.id) : [];
    };

    const id = ref('');
    const create = () => {
      id.value = 'new';
    };
    const edit = (geo: MaterialGeo) => {
      id.value = geo.id;
    };
    const back = (reload?: boolean) => {
      id.value = '';
      reload && loadData();
    };

    /**
     * 更改状态
     * @param geo
     */
    const updateActive = (geo: MaterialGeo) => {
      MaterialGeoService.updateActive(geo.id, geo.active).then((res) => {
        if (res && res.status === 'success') {
          $q.notify({ message: res.message, type: 'positive', position: 'top' });
        } else {
          geo.active = geo.active ? 0 : 1;
        }
      });
    };

    /**
     * 删除
     */
    const del = () => {
      if (!ticked.value.length) return;

      $q.dialog({
        title: '提示',
        message: `确认删除吗?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(() => {
        MaterialGeoService.delete(ticked.value).then((res) => {
          if (res && res.status === 'success') {
            loadData();
            $q.notify({ message: res.message, type: 'positive', position: 'top' });
          }
        });
      });
    };

    return {
      acl,

      list,
      loading,

      tree,
      treeRef,
      filter,
      getParentsLen,

      checkAll,
      ticked,
      selectAll,

      id,
      create,
      edit,
      back,

      updateActive,
      del
    };
  }
});
