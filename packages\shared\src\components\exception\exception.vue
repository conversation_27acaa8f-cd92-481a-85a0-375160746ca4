<template>
  <div class="ht-exception window-height flex flex-center">
    <img :src="config.img" />
    <div class="q-gutter-y-md q-pl-xl">
      <div class="ht-text-gray-700 text-7xl">{{ config.title }}</div>
      <div class="ht-text-gray-400 text-xl">{{ config.desc }}</div>
      <div class="q-gutter-x-md">
        <q-btn v-if="type === 403" label="退出登录" color="grey" unelevated @click="logout" />
        <q-btn v-if="homePath !== false" label="返回首页" color="primary" unelevated @click="toHome" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export type ExceptionType = 403 | 404 | 500 | number;

export interface ExceptionConfig {
  img: string;
  title: string;
  desc: string;
}

export interface ExceptionProps {
  type: ExceptionType;
  config?: Partial<ExceptionConfig>;
}
</script>

<script setup lang="ts">
import { computed, inject } from 'vue';
import { useRouterWithout } from '@hetu/core';
import { TOKEN_HOME_PATH } from '../../models';

defineOptions({ name: 'ht-exception' });

const props = defineProps<ExceptionProps>();
const emits = defineEmits<{ logout: [] }>();

const items: Record<ExceptionType, ExceptionConfig> = {
  403: {
    img: './static/img/403.png',
    title: '403',
    desc: '抱歉, 您无权访问该页面'
  },
  404: {
    img: './static/img/404.png',
    title: '404',
    desc: '抱歉, 您访问的页面不存在'
  },
  500: {
    img: './static/img/500.png',
    title: '500',
    desc: '抱歉, 服务器出错了'
  }
};

const config = computed(() => {
  return Object.assign({}, items[props.type], props.config);
});

const homePath = inject<false | string>(TOKEN_HOME_PATH);

const router = useRouterWithout();
const toHome = () => router.replace({ path: homePath as string });
const logout = () => emits('logout');
</script>
