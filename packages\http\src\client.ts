import Axios, { type AxiosInstance } from 'axios';
import { setApiConfig, type HttpApi, type RootHttpApi } from './api';
import { type HttpConfig, setupHttpConfig } from './config';
import type {
  HttpErrorInterceptor,
  HttpInterceptorKeys,
  HttpRequestFullInterceptor,
  HttpRequestInterceptor,
  HttpRequestInterceptorType,
  HttpResponseFullInterceptor,
  HttpResponseInterceptor,
  HttpResponseInterceptorType
} from './interceptor';

const itrKeys: HttpInterceptorKeys = {
  request: new Map(),
  response: new Map()
};

function addRequestInterceptor(interceptor: HttpRequestInterceptorType) {
  const original = interceptor;
  if (typeof interceptor === 'function') {
    interceptor = new (interceptor as new () => HttpRequestFullInterceptor)();
  }

  const { request } = HttpClient.interceptors;
  const id = request.use(
    (interceptor as HttpRequestInterceptor).intercept
      ? (value) => (interceptor as HttpRequestInterceptor).intercept(value)
      : undefined,
    (interceptor as HttpErrorInterceptor).error
      ? (value) => (interceptor as HttpErrorInterceptor).error(value)
      : undefined
  );
  itrKeys.request.set(original, id);
  return id;
}

function removeRequestInterceptor(interceptor: HttpRequestInterceptorType) {
  const id = itrKeys.request.get(interceptor);
  const { eject } = HttpClient.interceptors.request;
  id && eject(id);
}

function addResponseInterceptor(interceptor: HttpResponseInterceptorType) {
  const original = interceptor;
  if (typeof interceptor === 'function') {
    interceptor = new (interceptor as new () => HttpResponseFullInterceptor)();
  }

  const { response } = HttpClient.interceptors;
  const id = response.use(
    (interceptor as HttpResponseInterceptor).intercept
      ? (value) => (interceptor as HttpResponseInterceptor).intercept(value)
      : undefined,
    (interceptor as HttpErrorInterceptor).error
      ? (value) => (interceptor as HttpErrorInterceptor).error(value)
      : undefined
  );
  itrKeys.response.set(original, id);
  return id;
}

function removeResponseInterceptor(interceptor: HttpResponseInterceptorType) {
  const id = itrKeys.response.get(interceptor);
  const { eject } = HttpClient.interceptors.response;
  id && eject(id);
}

export interface HttpClientInstance extends AxiosInstance {
  /**
   * 拦截器 KEY
   */
  itrKeys: HttpInterceptorKeys;

  /**
   * 添加请求拦截器
   * @param interceptor 拦截器
   */
  addRequestInterceptor(interceptor: HttpRequestInterceptorType): number;

  /**
   * 删除请求拦截器
   * @param interceptor 拦截器
   */
  removeRequestInterceptor(interceptor: HttpRequestInterceptorType): void;

  /**
   * 添加响应拦截器
   * @param interceptor 拦截器
   */
  addResponseInterceptor(interceptor: HttpResponseInterceptorType): number;

  /**
   * 删除响应拦截器
   * @param interceptor 拦截器
   */
  removeResponseInterceptor(interceptor: HttpResponseInterceptorType): void;

  /**
   * 设置 `api` 配置信息
   * @param apiConfig `api` 配置
   * @param moduleKey 业务模块标识
   */
  setApiConfig(apiConfig: RootHttpApi | HttpApi, moduleKey?: string): void;
}

/**
 * HTTP 客户端
 */
export const HttpClient = {
  itrKeys,
  addRequestInterceptor,
  removeRequestInterceptor,
  addResponseInterceptor,
  removeResponseInterceptor,
  setApiConfig
} as HttpClientInstance;

/**
 * 创建 `HttpClient` 实例
 */
export function createHttpClient(config?: Partial<HttpConfig>): HttpClientInstance {
  config = setupHttpConfig(config);

  Object.assign(HttpClient, Axios.create(config.requestConfig));

  // 添加请求拦截器
  const request = config.interceptors?.request;
  request?.forEach(addRequestInterceptor);

  // 添加响应拦截器
  const response = config.interceptors?.response;
  response?.forEach(addResponseInterceptor);

  return HttpClient;
}
