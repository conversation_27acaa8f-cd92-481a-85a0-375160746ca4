import { TokenService } from '@hetu/auth';
import { PageService } from '../services';
import type { RouteActivateGuard, RouteLocationNormalized } from 'vue-router';

/**
 * 路由守卫: 平台 `token` 验证
 */
export const useTokenGuard: RouteActivateGuard = (to: RouteLocationNormalized) => {
  const model = TokenService.get();
  if (!model.token || model.code === 307) {
    if (!model.token) {
      PageService.toLogin(location.href);
      return false;
    }
    return true;
  }
};
