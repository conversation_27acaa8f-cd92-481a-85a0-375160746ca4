import { SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import {
  HttpApiService,
  type HttpApi,
  type ResponseResult,
  responseResult,
  responseData,
  responseReject
} from '@hetu/http';
import { Recycle } from '../models';
import { createSingleClass } from '@hetu/core';
class Recycle<PERSON>pi implements HttpApi {
  // 回收站列表
  visuals = `${SYSTEM_API_PREFIX}/recycle/list`;
}

/**
 * 回收站服务类
 * <AUTHOR>
 */
export class RecycleServiceCtro extends HttpApiService<RecycleApi> {
  httpApi = new RecycleApi();
  httpModuleKey = 'recycle';

  /**
   * 回收站列表
   * @param moduleCode 应用模块
   */
  getlist(moduleCode: string): Promise<Recycle[]> {
    return this.http.get(this.api.visuals, { params: { moduleCode } }).then(responseData, responseReject);
  }
}

export const RecycleService = createSingleClass(RecycleServiceCtro);
