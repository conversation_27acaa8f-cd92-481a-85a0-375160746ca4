@import '../../../../../index.scss';

.#{$vis-prefix}-data-sheet {

  // 容器包装器样式 - 移除焦点轮廓
  &__container-wrapper {
    outline: none;

    &:focus {
      outline: none;
    }
  }

  // 移除整个组件的焦点轮廓
  &:focus {
    outline: none;
  }

  display: flex;
  flex-direction: column;
  background: $input-bg;
  font-size: $primary-font-size;
  border: 1px solid #e1e5e9;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // 多选状态下的全局样式
  &.selecting {
    cursor: crosshair;
    user-select: none;
  }

  // 提示信息样式
  &__tips {
    position: absolute;
    top: $secondary-margin;
    right: $secondary-margin;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__tip {
    padding: 6px 12px;
    border-radius: $border-radius;
    font-size: $primary-font-size;
    font-weight: $title-font-weight;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;

    &--warning {
      background: $info;
    }

    &--info {
      background: $primary;
    }

    &--success {
      background: $positive;
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(100%);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: $input-bg;

    &-left {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    &-right {
      display: flex;
      align-items: center;
      gap: $field-gap;
    }

    .q-btn {
      background: transparent;
      border-radius: $border-radius;
      transition: all 0.15s ease;
      min-width: 24px;
      height: 24px;

      .q-icon {
        font-size: 14px;
      }
      &.active {
        background: rgba($select-active-color, 0.1);
        color: $select-active-color;
      }

      &:hover {
        background: $input-hover-color;
        color: #212529;
      }

      &:disabled {
        opacity: 0.5;
        color: #6c757d;
      }

    }

    .q-separator {
      margin: 0 2px;
      width: 1px;
      height: 28px;
    }

  }

  &__tabs {
    display: flex;
    align-items: center;
    background: $input-bg;
    border-bottom: 1px solid #e1e5e9;
    min-height: 40px;
    padding: 0 $primary-margin;
  }

  &__tabs-container {
    align-items: center;
    flex: 1;
    gap: 0;

    .q-scrollarea__content {
      display: flex;
      align-items: center;
      gap: 0;
    }

  }

  &__tab {
    display: flex;
    align-items: center;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: $border-radius $border-radius 0 0;
    cursor: pointer;
    user-select: none;
    max-width: 150px;
    min-width: 60px;
    transition: all 0.2s ease;
    position: relative;
    color: #6c757d;
    height: 28px;
    flex-shrink: 0; // 防止标签被压缩

    &:hover {
      background: rgba(0, 0, 0, 0.04);
      color: #495057;
    }

    &--active {
      background: transparent;
      border-bottom-color: $select-active-color;
      color: $select-active-color;
      font-weight: 500;

      &:hover {
        background: rgba($select-active-color, 0.04);
        color: $select-active-color;
      }
    }
  }

  &__tab-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 24px;
    padding: 2px 8px;
  }

  &__tab-rename-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 13px;
    font-family: inherit;
    color: inherit;
    padding: 0;
    margin: 0;
    min-width: 60px;
    font-weight: inherit;
  }

  &__tab-close {
    opacity: 0;
    //transition: all 0.2s ease;
    min-width: 24px;
    height: 24px;
    color: #6c757d;

    &:hover {
      opacity: 1;
      background: none !important;
      color: #dc3545;
    }
  }

  &__tab:hover &__tab-close {
    opacity: 0.6;
  }

  &__add-sheet {
    display: flex;
    align-items: center;
    margin-left: 4px;
    min-width: 24px;
    max-width: 24px;
    height: 24px;
    flex-shrink: 0;

    .q-btn {
      color: #6c757d;
      background: transparent;
      transition: all 0.2s ease;
      min-width: 24px;
      height: 24px;

      &:hover {
        background: $input-hover-color;
      }
    }
  }

  &__container {
    position: relative;
    overflow: hidden;
    background: transparent;
    outline: none;

    &:focus {
      outline: none;
    }

    // 冻结时的滚动容器样式
    &.has-frozen {
      .scroll {
        overflow: hidden;
      }
    }
  }

  &__header {
    display: flex;
    background: $input-bg;
    border-bottom: 1px solid #e1e5e9;

    // 冻结时的样式
    &[style*="position: sticky"] {
      background: $input-bg;
      border-bottom: 1px solid #e1e5e9;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &-cell {
      position: relative;
      min-width: 100px;
      height: 36px;
      padding: 0 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #e1e5e9;
      cursor: pointer;
      user-select: none;
      color: $label-color;
      background: $input-bg;
      box-sizing: border-box;

      &:hover {
        background: $input-hover-color;
      }

      &--selected {
        background: $select-active-color;
        color: #fff;

        .vis-icon {
          color: #fff;
        }

        &:hover {
          background: $select-active-color;
        }
      }
    }

    &-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 600;
      font-size: 14px;
    }

    &-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      gap: 4px;
    }
  }

  // 字段类型选择器样式
  &__type-selector {
    position: relative;
    display: flex;
    align-items: center;

    .q-btn {
      color: $select-active-color;
      background: none;
      font-weight: 500;

      .vis-icon {

        font-size: 16px;
      }

      &:hover {
        background: none;
        color: $select-active-color;
      }
    }

    .q-menu {
      .q-list {
        min-width: 80px;
        padding: 4px 0;

        .q-item {
          padding: 6px 12px;
          font-size: 12px;
          color: #495057;

          &:hover {
            background: #f8f9fa;
          }
        }
      }
    }
  }

  &__resize-handle {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.15s ease;

    &:hover {
      background: rgba($select-active-color, 0.3);
    }

    &:active {
      background: rgba($select-active-color, 0.5);
    }
  }

  &__corner {
    width: 50px;
    min-width: 50px;
    height: 36px;
    background: $input-bg;
    border-right: 1px solid #e1e5e9;

    // 冻结时的样式
    &[style*="position: sticky"] {
      background: $input-bg;
      border-right: 1px solid #e1e5e9;
      box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  &__add-column-btn {
    width: 40px;
    min-width: 40px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.15s ease;


    .q-btn {
      color: #495057;
      background: transparent;
      border-radius: 4px;
      min-width: 24px;
      height: 24px;

      &:hover {
        background: #dee2e6;
        color: #212529;
      }

      .q-icon {
        font-size: 14px;
      }
    }
  }

  &__body {
    display: flex;
    flex-direction: column;
    background: #fff;
  }

  &__row {
    display: flex;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.15s ease;

    &:last-child {
      background: none;
      border-bottom: none;
    }
  }

  &__add-row {
    display: flex;
    border-bottom: 1px solid #f1f3f4;

    transition: background-color 0.15s ease;
  }

  &__add-row-btn {
    width: 50px;
    min-width: 50px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $input-bg;
    border-right: 1px solid #e1e5e9;
    cursor: pointer;
    transition: background-color 0.15s ease;

    // 冻结时的样式
    &[style*="position: sticky"] {
      background: $input-bg;
      border-right: 1px solid #e1e5e9;
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    }

    .q-btn {
      color: #495057;
      background: transparent;
      transition: all 0.15s ease;
      font-size: $primary-font-size;
      min-width: 24px;
      height: 24px;

      .q-icon {
        font-size: 14px;
      }

      &:hover {
        background: #dee2e6;
        color: #212529;
      }

    }
  }

  &__row-header {
    width: 50px;
    min-width: 50px;
    height: 32px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    cursor: pointer;
    user-select: none;
    font-weight: 600;
    color: #6c757d;
    font-size: 11px;
    transition: all 0.15s ease;

    // 冻结时的样式
    &[style*="position: sticky"] {
      border-right: 1px solid #e1e5e9;
      box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    }

    &:hover {
      background: #e9ecef;
      color: #495057;
    }

    &--selected {
      background: $select-active-color ;
      color: white;

      &:hover {
        background: $select-active-color;
      }
    }
  }

  &__cell {
    position: relative;
    min-width: 100px;
    height: 32px;
    padding: 0 8px;
    border-right: 1px solid #f1f3f4;
    cursor: cell;
    outline: none;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    background: #fff;
    box-sizing: border-box;

    &:hover {
      background: #f8f9fa;
    }

    &--selected {
      background: #e3f2fd;
    }

    &--focused {
      background: #bbdefb;
      box-shadow: inset 0 0 0 1px #1976d2;
    }

    &--editing {
      padding: 0;
      background: #fff;
      box-shadow: inset 0 0 0 1px #2196f3;
      overflow: hidden;
    }
  }

  &__cell-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    color: #212529;
    line-height: 1.4;
    flex: 1;
  }

  &__edit-input {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    padding: 0 8px;
    background: transparent;
    box-sizing: border-box;
    color: #212529;
    line-height: 1.4;
    margin: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    &::placeholder {
      color: #6c757d;
    }
  }


  // 调整列宽时的全局样式
  &.resizing {
    cursor: col-resize;
    user-select: none;
  }

}

// 对话框样式优化
.q-dialog {

  &__inner {
    padding: 0;
  }

  // 全屏模式样式
  &.fullscreen {
    .vis-data-sheet {
      max-width: 100vw;
      max-height: 100vh;
      margin: 0;

      .vis-data-sheet__tips {
        top: 40px;
        right: 40px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .vis-data-sheet {
    &__toolbar {
      padding: 6px 8px;

      &-left,
      &-right {
        gap: 2px;
      }
    }

    &__tabs {
      padding: 0 8px;
      min-height: 36px;
    }

    &__tab {
      padding: 6px 12px;
      min-width: 60px;
      max-width: 100px;
      font-size: 12px;
    }

    &__add-sheet {
      margin-left: 2px;

      .q-btn {
        min-width: 24px;
        height: 24px;

        .q-icon {
          font-size: 14px;
        }
      }
    }

    &__header-cell,
    &__cell {
      min-width: 80px;
      padding: 0 6px;
    }

    &__row-header {
      width: 40px;
      min-width: 40px;
    }

    &__corner {
      width: 40px;
      min-width: 40px;
    }
  }
}