import { SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import { createSingleClass } from '@hetu/core';
import { HttpApiService, type HttpApi, responseReject, responseData } from '@hetu/http';
import { type OperateLog } from '../models';

class OperateApi implements HttpApi {
  user = `${SYSTEM_API_PREFIX}/work/log/current/user`;
  team = `${SYSTEM_API_PREFIX}/work/log/teams`;
}
/**
 * 工作动态服务类
 */
export class OperateServiceCtor extends HttpApiService<OperateApi> {
  httpApi = new OperateApi();
  httpModuleKey = 'operateLog';
  /**
   * 我最近的修改
   * @param moduleCode
   */
  operateUser(moduleCode: string): Promise<OperateLog[]> {
    return this.http.get(this.api.user, { params: { moduleCode } }).then(responseData, responseReject);
  }
  /**
   * 团队动态
   * @param moduleCode
   */
  operateTeam(moduleCode: string): Promise<OperateLog[]> {
    return this.http.get(this.api.team, { params: { moduleCode } }).then(responseData, responseReject);
  }
}

export const OperateService = createSingleClass(OperateServiceCtor);
