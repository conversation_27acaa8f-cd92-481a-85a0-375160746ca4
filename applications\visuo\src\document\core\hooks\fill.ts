import { cloneDeep } from 'lodash-es';
import { FillType, Image, ImageFit, ImageType, type ColorStop, type FillPaints } from '../models';
import { CacheService, type Records } from '@hetu/util';
import { AttachmentService } from '@hetu/platform-shared';

/**
 * 颜色填充公用方法
 * <AUTHOR>
 */

export const useFill = () => {
  /**
   * 计算多个填充混合后的样式
   * @param fill
   */
  const getFillPaintsStyle = (fills?: FillPaints[]) => {
    const fillObj: Records<string> = {
      backgroundImage: 'initial',
      backgroundPosition: 'initial',
      backgroundSize: 'initial',
      backgroundRepeat: 'initial',
      backgroundColor: 'initial'
    };
    if (!fills || !fills.length) return fillObj;

    const visibleFills = fills.filter((f) => f.visible);
    if (!visibleFills.length) return fillObj;

    const images: string[] = [],
      positions: string[] = [],
      sizes: string[] = [],
      repeats: string[] = [];
    visibleFills.forEach((fill) => {
      const fillStyle = getFillStyle(fill);
      if (visibleFills.length === 1) {
        Object.assign(fillObj, fillStyle);
      } else {
        // 多填充值时，纯色也按照渐变处理
        if (fillStyle.backgroundColor) {
          fillStyle.backgroundImage = `linear-gradient(${fillStyle.backgroundColor} 0%, ${fillStyle.backgroundColor} 100%)`;
          fillStyle.backgroundPosition = 'center';
          fillStyle.backgroundSize = '100%';
          fillStyle.backgroundRepeat = 'no-repeat';
        }
        images.push(fillStyle.backgroundImage);
        positions.push(fillStyle.backgroundPosition);
        sizes.push(fillStyle.backgroundSize);
        repeats.push(fillStyle.backgroundRepeat);
      }
    });
    if (images.length) {
      fillObj.backgroundImage = images.join(', ');
      fillObj.backgroundPosition = positions.join(', ');
      fillObj.backgroundSize = sizes.join(', ');
      fillObj.backgroundRepeat = repeats.join(', ');
    }

    return fillObj;
  };
  /**
   * 获取填充样式
   * @param fill
   * @param isText 当前是否为文本
   * @returns
   */
  const getFillStyle = (fill: FillPaints, isText?: boolean) => {
    const fillObj: Records<string> = {};
    if (!fill?.visible) return Object.assign(fillObj, isText ? { color: 'transparent' } : {});

    if (fill.type === FillType.Solid) {
      const { r, g, b, a } = fill.color;
      if (isText) {
        // 文本纯色返回为颜色
        fillObj.color = `rgba(${r},${g},${b},${a})`;
      } else {
        // 非文本纯色返回为背景色
        fillObj.backgroundColor = `rgba(${r},${g},${b},${a})`;
      }
    } else if (fill.type?.includes('gradient')) {
      Object.assign(fillObj, gradientBackground(fill.type, fill.stops, fill.rotation, fill.opacity));
    } else if (fill.type === FillType.Image) {
      fill.image && Object.assign(fillObj, imageBackground(fill.image));
    }
    return fillObj;
  };

  const imageBackground = (image: Image) => {
    const imageObj: Records<string> = {};
    if (!image) return imageObj;

    const { type, url, objectFit } = image;
    const imageUrl = type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
    if (image) {
      imageObj.backgroundImage = `url(${imageUrl.startsWith('./') ? imageUrl.slice(1) : imageUrl})`;
    }
    switch (objectFit) {
      case ImageFit.Cover:
        imageObj.backgroundPosition = 'center';
        imageObj.backgroundSize = 'cover';
        imageObj.backgroundRepeat = 'no-repeat';
        break;
      case ImageFit.Contain:
        imageObj.backgroundPosition = 'center';
        imageObj.backgroundSize = 'contain';
        imageObj.backgroundRepeat = 'no-repeat';
        break;
      case ImageFit.Repeat:
        imageObj.backgroundPosition = 'initial';
        imageObj.backgroundSize = 'auto';
        imageObj.backgroundRepeat = 'repeat';
        break;
      case ImageFit.Strech:
        imageObj.backgroundPosition = 'initial';
        imageObj.backgroundSize = '100% 100%';
        imageObj.backgroundRepeat = 'no-repeat';
        break;
    }
    return imageObj;
  };

  /**
   * 根据渐变类型计算样式
   * @param type
   * @param stops
   * @param rotation
   * @returns
   */
  const gradientBackground = (type: FillType, stops: ColorStop[] = [], rotation: number = 0, opacity: number = 1) => {
    if (!stops.length) return {};

    const sorted = sortByPosition(cloneDeep(stops));
    const colorStr = sorted
      .map((p) => {
        const { r, g, b, a } = p.color;
        return `rgba(${r},${g},${b},${a * opacity}) ${p.position * 100}%`;
      })
      .join(', ');

    switch (type) {
      case FillType.Linear:
        return {
          backgroundImage: `linear-gradient(${rotation}deg, ${colorStr})`,
          backgroundPosition: 'center',
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat'
        };
      case FillType.Radial:
        return {
          backgroundImage: `radial-gradient(${colorStr})`,
          backgroundPosition: 'center',
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat'
        };
      case FillType.Angular:
        return {
          backgroundImage: `conic-gradient(from ${rotation}deg, ${colorStr})`,
          backgroundPosition: 'center',
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat'
        };
      case FillType.Diamond: {
        return {
          backgroundImage: `linear-gradient(to right bottom, ${colorStr}), linear-gradient(to left bottom, ${colorStr}), linear-gradient(to left top, ${colorStr}), linear-gradient(to right top, ${colorStr})`,
          backgroundPosition: 'right bottom, left bottom, left top, right top',
          backgroundSize: '50% 50%',
          backgroundRepeat: 'no-repeat'
        };
      }
    }
    return {};
  };

  /**
   * 渐变色数组按照position从左到右排序
   * @param array
   * @returns
   */
  const sortByPosition = (array: ColorStop[]) => {
    return array.sort((a, b) => {
      // 处理可能的 undefined 或空字符串情况
      const positionA = a.position !== undefined ? a.position : -Infinity;
      const positionB = b.position !== undefined ? b.position : -Infinity;

      return positionA - positionB;
    });
  };

  const paletteKey = 'palette_color';
  /**
   * 保存用户选择的颜色至本地缓存
   * @param color
   * @returns
   */
  const savePaletteColor = (type: string, color: string | Records) => {
    if (!color) return;

    const colors = CacheService.getNone(paletteKey) || {};

    if (!colors[type]) {
      CacheService.set(paletteKey, { ...colors, [type]: [color] });
    } else {
      const newColors = colors[type]
        .filter((c: string | Object) => {
          if (typeof c === 'string') {
            return c !== color;
          } else {
            return JSON.stringify(c) !== JSON.stringify(color);
          }
        })
        .splice(0, 8);
      newColors.unshift(color);
      CacheService.set(paletteKey, { ...colors, [type]: newColors });
    }
  };

  /**
   * 获取颜色数组
   * @returns
   */
  const getPaletteColors = (type: string = FillType.Solid) => {
    const colors = CacheService.getNone(paletteKey) || {};
    return colors[type]?.splice(0, 9) || [];
  };

  /**
   * 字符串rgba值转为对象
   * @param color
   * @returns
   */
  const getRgbaFromString = (color: string) => {
    const match = color.match(/[\d|\.]+/g);
    if (!match) return { r: 0, g: 0, b: 0, a: 1 };

    const [r, g, b, a] = match.map(Number);
    return { r, g, b, a: a === undefined ? 1 : a };
  };

  /**
   * 颜色hex值转为rgba形式
   * @param hex
   * @returns
   */
  const hexToRgba = (hex: string) => {
    // 移除 # 符号
    hex = hex.replace('#', '');

    // 处理 3/4 位 HEX
    if (hex.length === 3 || hex.length === 4) {
      hex = hex
        .split('')
        .map((c) => c + c)
        .join('');
    }

    // 解析 RGB 和 Alpha
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    let a = 1;

    // 处理 8 位 HEX（含 Alpha）
    if (hex.length === 8) {
      a = parseFloat((parseInt(hex.substring(6, 8), 16) / 255).toFixed(2));
    }
    return `rgba(${r}, ${g}, ${b}, ${a})`;
  };

  return {
    getFillPaintsStyle,
    getFillStyle,
    imageBackground,
    gradientBackground,
    sortByPosition,
    savePaletteColor,
    getPaletteColors,
    getRgbaFromString,
    hexToRgba
  };
};
