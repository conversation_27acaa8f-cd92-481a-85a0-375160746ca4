import { useDocumentStore } from '@vis/document-core';
import { useActionStore, useDesignStore } from '../stores';
import { useAction } from './action';
import { CacheService } from '@hetu/util';
import { computed } from 'vue';
import { VIS_DESIGN_INFINITE_CANVAS } from '../models';

/**
 * 快捷键
 * <AUTHOR>
 */
export const useShortcutKeys = () => {
  const docStore = useDocumentStore();
  const actionStore = useActionStore();
  const designStore = useDesignStore();

  const { move, hand, del } = useAction();

  const active = computed(() => designStore.active.value);

  /**
   * 添加快捷键
   */
  const addShortcuts = () => {
    window.addEventListener('keydown', onKeydown);
    window.addEventListener('keyup', onKeyup);

    window.addEventListener('mousedown', onMouseDown);
    window.addEventListener('mouseup', onMouseUp);
  };

  /**
   * 移除快捷键
   */
  const removeShortcuts = () => {
    window.removeEventListener('keydown', onKeydown);
    window.removeEventListener('keyup', onKeyup);

    window.removeEventListener('mousedown', onMouseDown);
    window.removeEventListener('mouseup', onMouseUp);
  };

  const onKeydown = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;
    const target = e.target as HTMLElement;

    if (target) {
      const tagName = target.tagName;
      const exceptTags = ['INPUT', 'TEXTAREA', 'SELECT'];
      if (exceptTags.indexOf(tagName) !== -1) {
        return;
      }
      if (target.contentEditable === 'true') {
        return;
      }
    }

    // 按空格键移动画布
    if (e.code === 'Space') {
      e.preventDefault();
      hand();
    }
    if ((e.metaKey || e.ctrlKey) && e.code === 'KeyS') {
      const doc = docStore.document.value;

      doc.home = doc.children[0].id;
      active.value.page.main = active.value.page.children[0].id;
      console.log('保存:', docStore.document.value);
      CacheService.set(`doc_${docStore.document.value.id}`, docStore.document.value);
      // 阻止事件的默认行为
      e.preventDefault();
    }
    if (e.key === 'Delete' || e.key === 'Backspace') {
      del();
    }
  };

  const onKeyup = (e: KeyboardEvent) => {
    actionStore.isMetaCtrl.value = e.metaKey || e.ctrlKey;

    // 松开空格键框选组件
    if (e.code === 'Space') {
      move();
    }
  };

  const onMouseDown = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseDown:--------', true);
    }
  };

  const onMouseUp = (e: MouseEvent) => {
    if (e.button === 0) {
      // console.log('onMouseUp:--------', false);
    }
  };

  return {
    addShortcuts,
    removeShortcuts,
    onKeydown,
    onKeyup
  };
};
