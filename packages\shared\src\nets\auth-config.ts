import { SettingsService, type User } from '@hetu/theme';
import { useAuthConfig, type AuthConfig } from '@hetu/auth';
import { type CookieOptions } from '@hetu/util';
import { CookieStorageStore, LocalStorageStore, type TokenModel, type TokenStore } from '@hetu/auth';
import { PlatformService } from '../services';

class AppTokenStore implements TokenStore {
  private readonly localStore = new LocalStorageStore();

  private _cookieStore!: TokenStore;

  private get cookieStore() {
    this._cookieStore = this._cookieStore || new CookieStorageStore();
    return this._cookieStore;
  }

  private get store(): TokenStore {
    if (PlatformService.domain) {
      return this.cookieStore;
    } else {
      return this.localStore;
    }
  }

  get(key: string): TokenModel {
    return this.store.get(key);
  }

  set(key: string, value: TokenModel, options?: CookieOptions | (() => CookieOptions)): boolean {
    return this.store.set(key, value, options);
  }

  remove(key: string, options?: CookieOptions | (() => CookieOptions)) {
    this.store.remove(key, options);
  }
}

/**
 * `token` 配置信息
 * <AUTHOR>
 */
export class AppAuthConfig implements AuthConfig {
  store = new AppTokenStore();

  loginUrl = '';

  constructor() {
    const key = 'user';
    const desc = Object.getOwnPropertyDescriptor(SettingsService, key) as PropertyDescriptor;
    const get = desc.get as () => User;
    Object.defineProperty(SettingsService, key, {
      get: () => {
        // 域名模式 `User` 信息从 `TokenStore` 获取
        if (PlatformService.domain) {
          const authConfig = useAuthConfig();
          return this.store.get(authConfig.storeKey!).user;
        }

        return get.call(SettingsService);
      }
    });
  }

  cookieOptions() {
    return {
      expires: SettingsService.app.tokenExpires,
      SameSite: 'strict',
      domain: SettingsService.app.domain
    } as CookieOptions;
  }
}

export class SpaceAuthConfig implements AuthConfig {
  storeKey = '';

  constructor() {
    SettingsService.userCacheKey = 'space_user';
  }
}
