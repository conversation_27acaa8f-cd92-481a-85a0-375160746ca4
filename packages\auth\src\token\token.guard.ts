import type { RouteActivateGuard, RouteLocationNormalized } from 'vue-router';
import { TokenService } from './token.service';

/**
 * 路由守卫: `Token` 验证
 * <AUTHOR>
 */
export const useTokenGuard: RouteActivateGuard = (to: RouteLocationNormalized) => {
  const token = TokenService.get();
  if (!token.token || token.code === 307) {
    if (to.path !== TokenService.loginUrl) {
      return {
        path: TokenService.loginUrl,
        query: {
          redirect: to.fullPath
        }
      };
    }
    return false;
  }
};
