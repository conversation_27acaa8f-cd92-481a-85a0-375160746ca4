import { Graph } from '@vis/document-core';
import { computed, defineComponent, nextTick, ref, watch, type PropType } from 'vue';

/**
 * 外观
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-aspect',
  props: {
    option: {
      type: Object as PropType<Graph>,
      required: true
    }
  },
  setup(props) {
    const graph = computed(() => props.option as Graph);

    //#region 圆角
    /** 圆角 */
    const radius = ref(0);

    const radiusChange = () => {
      graph.value.radius = [radius.value, radius.value, radius.value, radius.value];
    };

    const showRadius = ref(false);

    watch(
      () => graph.value.id,
      () => {
        radius.value = graph.value.radius[0];
        showRadius.value = !graph.value.radius.every((item) => item === graph.value.radius[0]);
      },
      {
        immediate: true
      }
    );

    //#endregion

    return {
      graph,

      radius,
      radiusChange,
      showRadius
    };
  }
});
