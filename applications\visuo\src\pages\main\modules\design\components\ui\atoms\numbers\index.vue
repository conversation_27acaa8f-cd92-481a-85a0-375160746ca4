<template>
  <div class="vis-numbers rounded-borders overflow-hidden flex-1">
    <div v-if="isMulti" class="row items-center">
      <!-- 多值 -->
      <q-btn class="q-pa-none" dense flat @click="onChange">
        <q-icon class="hticon-vis-padding-four hover:text-primary">
          <q-tooltip> 多值 </q-tooltip>
        </q-icon>
      </q-btn>
      <q-input v-model.number="values[0]" type="number" :min="0" placeholder="上" dense borderless
        input-class="text-center" class="col vis-field--mini no-spin">
        <q-tooltip> 上 </q-tooltip>
      </q-input>
      <q-input v-model.number="values[1]" type="number" :min="0" placeholder="右" dense borderless
        input-class="text-center" class="col vis-field--mini no-spin">
        <q-tooltip> 右 </q-tooltip>
      </q-input>
      <q-input v-model.number="values[2]" type="number" :min="0" placeholder="下" dense borderless
        input-class="text-center" class="col vis-field--mini no-spin">
        <q-tooltip> 下 </q-tooltip>
      </q-input>
      <q-input v-model.number="values[3]" type="number" :min="0" placeholder="左" dense borderless
        input-class="text-center" class="col vis-field--mini no-spin">
        <q-tooltip> 左 </q-tooltip>
      </q-input>
    </div>
    <div v-else class="row items-center">
      <!-- 单值 -->
      <q-btn class="q-pa-none q-mr-xs" dense flat @click="onChange">
        <q-icon class="hticon-vis-padding text-gray-4 hover:text-primary">
          <q-tooltip> 单值 </q-tooltip>
        </q-icon>
      </q-btn>

      <q-input v-model.number="value" @update:model-value="updateValue" type="number" placeholder="单值" dense borderless
        class="flex-1 vis-field--mini no-spin pl-1" />
    </div>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
