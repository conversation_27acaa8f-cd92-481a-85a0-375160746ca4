import { createApp as _createApp, inject, type App, type CreateAppFunction } from 'vue';

let app: ReturnType<typeof _createApp>;

export const createApp: CreateAppFunction<Element> = (...args) => {
  app = _createApp(...args);
  return app;
};

export const useApp = () => app;

export const useAppContext: App['runWithContext'] = (fn) => app.runWithContext(fn);

export const useProvide: App['provide'] = (key, value) => app.provide(key, value);

export const useInject: typeof inject = (key) => useAppContext(() => inject(key));
