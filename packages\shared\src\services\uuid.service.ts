import { responseData, HttpApiService, responseReject, type HttpApi } from '@hetu/http';
import { API_PREFIX, SYSTEM_API_PREFIX } from '../models';
import { createSingleClass } from '@hetu/core';

class UUIDApi implements HttpApi {
  uuid = `${API_PREFIX}${SYSTEM_API_PREFIX}/uuid`;
}

class UUIDServiceCtor extends HttpApiService<UUIDApi> {
  httpApi = new UUIDApi();

  httpModuleKey = 'uuid';

  /**
   * 获取UUID
   */
  getUUID(): Promise<string> {
    return this.http.get(this.api.uuid).then(responseData, responseReject);
  }
}

/**
 * UUID服务类
 */
export const UUIDService = createSingleClass(UUIDServiceCtor);
