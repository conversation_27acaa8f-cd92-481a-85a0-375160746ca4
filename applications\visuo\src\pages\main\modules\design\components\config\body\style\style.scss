// 引入共享变量和样式
@import '../../../index.scss';

.#{$vis-prefix}-config-style {

  &-header {
    @apply p-3 h-10 flex items-center justify-between border-0 border-b border-gray-200 border-solid;
  }

  &-title {
    font-size: $primary-font-size;
    font-weight: 500;
  }

  &-tabs {
    @apply h-6 lh-6 flex items-center;

    :deep(.q-tab) {
      @apply min-h-6 w-6 h-6 lh-6 p-0 m-0;

      .q-tab__content {
        @apply min-w-6 p-0 m-0 w-6;
      }

      .q-tab__icon {
        font-size: 14px;
        color: $label-color;
      }


      // active 状态
      &.q-tab--active {
        border-radius: $border-radius;
        background-color: $input-bg;

        .vis-icon,
        .q-icon {
          color: rgba($dark, 0.88);
        }
      }
    }
  }

  &-scroll {
    @apply flex flex-col w-full;

    height: calc(100vh - 130px);

    // 底部额外滚动空间
    :deep(.q-scrollarea__content) {
      padding-bottom: var(--q-scrollarea-content-padding-bottom, 0);
    }
  }
}