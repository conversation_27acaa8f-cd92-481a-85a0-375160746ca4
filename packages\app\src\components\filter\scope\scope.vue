<template>
  <div class="ht-filter-scope">
    <div class="px-5 py-4">
      <q-btn-group spread no-caps unelevated class="border border-solid border-primary">
        <q-btn
          :text-color="scopeFilter.dateType === option.value ? '' : 'primary'"
          :label="option.label"
          v-for="option in options"
          :key="option.value"
          :color="scopeFilter.dateType === option.value ? 'primary' : ''"
          @click="onChangeType(option.value)"
        />
      </q-btn-group>
      <div class="row py-4">
        <div class="offset-2 col-10">
          <div class="row items-center q-gutter-y-sm">
            <div class="col-3">
              <q-radio
                size="xs"
                v-model="scopeFilter.dataScopeType"
                :val="1"
                :label="`前一${(scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'}`"
                class="!flex mb-2"
                :disable="!scopeFilter.dateType"
              />
            </div>
            <div class="col-9">
              <div class="row mb-2">
                <q-radio
                  size="xs"
                  v-model="scopeFilter.dataScopeType"
                  :val="4"
                  label="前"
                  class="!flex mr-2"
                  :disable="!scopeFilter.dateType"
                />
                <q-input
                  outlined
                  dense
                  v-model="scopeFilter.dateValue"
                  type="number"
                  :disable="scopeFilter.dataScopeType !== 4"
                  input-class="text-center"
                >
                  <template v-slot:after>
                    <span class="text-sm text-dark">{{
                      (scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'
                    }}</span>
                  </template>
                </q-input>
              </div>
            </div>
            <div class="col-3">
              <q-radio
                size="xs"
                v-model="scopeFilter.dataScopeType"
                :val="2"
                :label="`本${(scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'}`"
                class="!flex mb-2"
                :disable="!scopeFilter.dateType"
              />
            </div>
            <div class="col-9">
              <div class="row mb-2">
                <q-radio
                  size="xs"
                  v-model="scopeFilter.dataScopeType"
                  :val="5"
                  label="后"
                  class="!flex mr-2"
                  :disable="!scopeFilter.dateType"
                />
                <q-input
                  outlined
                  dense
                  v-model="scopeFilter.dateValue"
                  type="number"
                  :disable="scopeFilter.dataScopeType !== 5"
                  input-class="text-center"
                >
                  <template v-slot:after>
                    <span class="text-sm text-dark">{{
                      (scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'
                    }}</span>
                  </template>
                </q-input>
              </div>
            </div>
            <div class="col-3">
              <q-radio
                size="xs"
                v-model="scopeFilter.dataScopeType"
                :val="3"
                :label="`后一${(scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'}`"
                class="!flex mb-2"
                :disable="!scopeFilter.dateType"
              />
            </div>
            <div class="col-9">
              <q-radio
                v-if="scopeFilter.dateType !== ScopeDateType.Days"
                size="xs"
                v-model="scopeFilter.dataScopeType"
                :val="6"
                :label="`本${(scopeFilter.dateType && ScopeDateTypeName[scopeFilter.dateType]) || '年'}迄今`"
                class="!flex mb-2"
                :disable="!scopeFilter.dateType"
              />
            </div>
            <div class="col-3">
              <q-checkbox
                v-model="scopeFilter.anchor"
                label="锚点相对于"
                size="xs"
                class="mr-2"
                :disable="!scopeFilter.dateType"
              />
            </div>
            <div class="col-6">
              <q-input
                outlined
                dense
                v-model="scopeFilter.anchorDate"
                :disable="!scopeFilter.anchor"
                placeholder="今天"
              >
                <template v-slot:prepend>
                  <q-icon name="event" class="cursor-pointer">
                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                      <q-date v-model="scopeFilter.anchorDate" :mask="format"> </q-date>
                    </q-popup-proxy>
                  </q-icon>
                </template>
                <template v-slot:append v-if="isDateTime">
                  <q-icon name="access_time" class="cursor-pointer">
                    <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                      <q-time v-model="scopeFilter.anchorDate" :mask="format" format24h> </q-time>
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./scope"></script>
