import { CookieStorageStore } from './cookie-storage';
import { LocalStorageStore } from './local-storage';
import { MemoryStorageStore } from './memory-storage';
import { SessionStorageStore } from './session-storage';

import type { CookieOptions } from '@hetu/util';

export { CookieStorageStore } from './cookie-storage';
export { LocalStorageStore } from './local-storage';
export { MemoryStorageStore } from './memory-storage';
export { SessionStorageStore } from './session-storage';

export interface TokenModel {
  /** 发送 token 参数名, 可由登录请求返回, 如存在优先级高于配置中的 `sendKey` */
  key?: string;

  token: string | null | undefined;

  [key: string]: any;
}

export interface TokenStore {
  get(key: string): TokenModel;
  set(key: string, value: TokenModel, options?: CookieOptions | (() => CookieOptions)): boolean;
  remove(key: string, options?: CookieOptions | (() => CookieOptions)): void;
}

export enum TokenStoreType {
  Local,
  Session,
  Cookie,
  Memory
}

export function createStore(store: TokenStoreType | TokenStore): TokenStore {
  let tokenStore;
  switch (store) {
    case TokenStoreType.Local:
      tokenStore = new LocalStorageStore();
      break;
    case TokenStoreType.Session:
      tokenStore = new SessionStorageStore();
      break;
    case TokenStoreType.Cookie:
      tokenStore = new CookieStorageStore();
      break;
    case TokenStoreType.Memory:
      tokenStore = new MemoryStorageStore();
      break;
    default:
      tokenStore = store;
      break;
  }

  return tokenStore;
}
