import type { RouteRecordRaw } from 'vue-router';
import { setIgnoreMySpace } from '../guards';
import { getSpacePath, getSpacePathPrefix, getSpaceRoutePath } from '../utils';

/**
 * 创建空间路由
 * @param spaceRoute 路由
 * @param ignoreMySpace 忽略个人空间, 默认 `false`
 */
export const createSpaceRoutes = (spaceRoute: RouteRecordRaw, ignoreMySpace = false) => {
  const local = Object.assign({}, spaceRoute);
  const { path } = local;
  local.path = getSpaceRoutePath(path);
  local.props = true;

  const fillLocalRoute = (route: RouteRecordRaw) => {
    route.name && (route.name = `${route.name as string}-local`);
    route.meta = Object.assign({}, route.meta);
    route.meta.isLocal = true;
    if (route.children) {
      const children = [] as RouteRecordRaw[];
      addLocalChildren(route.children, children);
      route.children = children;
    }
  };

  const addLocalChildren = (source: RouteRecordRaw[], target: RouteRecordRaw[]) => {
    source.forEach((route) => {
      const localChild = Object.assign({}, route);
      fillLocalRoute(localChild);
      target.push(localChild);
    });
  };

  fillLocalRoute(local);

  const routes = [spaceRoute, local];
  if (ignoreMySpace) {
    setIgnoreMySpace(true);
  } else if (spaceRoute.meta?.spaceRoot !== false) {
    routes.push({
      path: getSpacePathPrefix(),
      redirect: getSpacePath(path)
    });
  }
  return routes;
};
