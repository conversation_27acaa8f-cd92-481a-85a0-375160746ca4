<template>
  <q-input
    ref="numberInput"
    class="vis-number flex-1 rounded-borders overflow-hidden vis-field--mini px-2 no-spin"
    :class="readonly ? 'no-drag' : ''"
    v-model.number="numberValue"
    type="text"
    borderless
    dense
    @focus="handleFocus"
    @keypress.enter="handleBlur"
    @blur="handleBlur"
    :placeholder="placeholder"
    :disable="disabled"
    :readonly="readonly"
    v-stop-change
    :step="step"
  >
    <template v-if="$slots.icon || icon" #prepend>
      <slot name="icon">
        <template v-if="icon">
          <ht-icon v-if="isIconFont" :name="icon" class="vis-icon drag-icon" />
          <q-icon v-else :name="icon" class="vis-icon drag-icon" />
        </template>
      </slot>
    </template>
    <!-- 后置插槽 -->
    <template v-if="$slots.append" #append><slot name="append"></slot></template>

    <template v-if="$slots.default" #default>
      <slot name="default"></slot>
    </template>

    <!-- 在输入框之前的插槽 -->
    <template v-if="$slots.before" #before>
      <slot name="before"></slot>
    </template>

    <!-- 在输入框之后的插槽 -->
    <template v-if="$slots.after" #after>
      <slot name="after"></slot>
    </template>
  </q-input>
</template>
<script lang="ts" src="./index.ts"></script>
