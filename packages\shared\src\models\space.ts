import type { HttpApi } from '@hetu/http';
import type { AccountConfig } from './account';
import { API_PREFIX, SYSTEM_API_PREFIX } from '.';

export interface SpaceLoginModel {
  username: string;
  password: string;
  code?: string;
  codeId?: string;
  domain?: string;
}

export interface SpaceAccountConfig extends AccountConfig {
  ssoConfig?: { clientName: string };
}

/**
 * 空间状态
 */
export interface SpaceStatus {
  /**
   * 租户类型
   * - 0: 个人
   * - 1: 团队
   */
  tenantType: 0 | 1;
  /**
   * 租户状态
   * - 0: 禁用
   * - 1: 启用
   */
  tenantStatus: 0 | 1;
}

export class SpaceApi implements HttpApi {
  /** 查询当前空间状态 */
  status = `${SYSTEM_API_PREFIX}/tenant/status`;
  /** 查询账号配置 */
  accountConfig = `${API_PREFIX}/space/login/config`;
  /** 获取登录用户信息 */
  info = `${API_PREFIX}/space/login/member/current/info`;
  /** 获取验证码图片 */
  validatorCode = `${API_PREFIX}/space/login/verify/code`;
  /** 登录 */
  login = `${API_PREFIX}/space/login`;
  /** 单点登录 */
  sso = `${API_PREFIX}/space/login/sso`;
  /** 退出登录 */
  logout = `${API_PREFIX}/space/login/logout`;
}
