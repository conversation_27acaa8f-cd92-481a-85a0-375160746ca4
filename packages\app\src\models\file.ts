import { BaseModel } from '@hetu/http';
import { ACLResource, type ACLType } from '@hetu/acl';

/**
 * 文件信息
 */
export interface FileInfo extends BaseModel {
  id: string;

  /** 名称 */
  title: string;

  /** 父级ID */
  parentId: string;

  /**
   * 数据类别
   * - 0: 文件
   * - 1: 文件夹
   */
  dataType: number;

  /** 文件类别 */
  category: string;

  /** 是否星标 */
  favorite: 0 | 1;

  attachmentId?: string;

  /** 封面截图 */
  snapshot?: string;
}

/**
 * 文件列表响应数据模型
 */
export interface FileData<T extends K, K extends FileInfo = FileInfo> {
  /** 文件夹列表 */
  directorys: K[];

  /** 文件列表 */
  files: T[];
}

export enum FileACLResourceCode {
  /** 编辑 */
  Edit = 'edit',

  /** 删除 */
  Delete = 'delete',

  /** 删除到回收站 */
  Recycle = 'recycle',

  /** 移动 */
  Move = 'move',

  /** 移动到空间 */
  SpaceMove = 'spaceMove',

  /** 添加星标 */
  SaveFavorite = 'saveFavorite',

  /** 取消星标 */
  DeleteFavorite = 'deleteFavorite',

  /** 取消所有星标 */
  DeleteAllFavorite = 'deleteAllFavorite',

  /** 设置通知消息方式 */
  SetupNotifyMsg = 'setupNotifyMsg'
}

export enum DirectoryACLResourceCode {
  /** 新建文件夹 */
  CreateDirectory = 'createDirectory',
  /** 删除文件夹 */
  DeleteDirectory = 'deleteDirectory',
  /** 编辑文件夹 */
  EditDirectory = 'editDirectory',
  /** 移动文件夹 */
  MoveDirectory = 'moveDirectory',
  /** 添加文件夹星标 */
  SaveFavoriteDir = 'saveFavoriteDir',
  /** 取消文件夹星标 */
  DeleteFavoriteDir = 'deleteFavoriteDir',
  /** 设置通知消息方式 */
  SetupNotifyMsgDir = 'setupNotifyMsgDir'
}

export abstract class DirectoryACLResource<T> extends ACLResource<typeof DirectoryACLResourceCode & T> {
  get toolbarDir() {
    const code = this.resourceCode as typeof DirectoryACLResourceCode;
    return {
      resource: {
        [this.group as string]: [
          code.EditDirectory,
          code.SaveFavoriteDir,
          code.DeleteFavoriteDir,
          code.MoveDirectory,
          code.DeleteDirectory
        ]
      }
    } as ACLType;
  }
}
