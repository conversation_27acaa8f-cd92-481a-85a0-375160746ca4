$ht-release-history-prefix-cls: '#{$ht-prefix}-release-history';

.#{$ht-release-history-prefix-cls} {
  .q-table__container {
    width: 700px;
    max-width: 700px;
    height: 500px;
  }

  tr th {
    position: sticky;
    z-index: 2;
  }

  tr th,
  td:first-child,
  td:last-child {
    background: #fff;
  }

  .q-dark {
    tr th,
    td:first-child,
    td:last-child {
      background: $dark;
    }
  }

  thead tr:last-child th {
    z-index: 3;
    top: 48px;
  }

  thead tr:first-child th {
    z-index: 1;
    top: 0;
  }

  tr:first-child th:first-child,
  tr:first-child th:last-child {
    z-index: 3;
  }

  td:first-child,
  td:last-child {
    z-index: 1;
  }

  td:first-child,
  th:first-child {
    position: sticky;
    left: 0;
  }

  th:last-child,
  td:last-child {
    position: sticky;
    right: 0;
  }

  tbody {
    scroll-margin-top: 48px;
  }
}
