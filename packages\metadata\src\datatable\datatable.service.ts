import { responseData, responseReject, HttpApiService } from '@hetu/http';
import { DatatableApi, type DatatableFields, type DatatableFile } from './models';
import { createSingleClass } from '@hetu/core';

/**
 * 数据表服务类
 * <AUTHOR>
 */
export class DatatableServiceCtor<T extends DatatableApi = DatatableApi> extends HttpApiService<DatatableApi | T> {
  httpApi: DatatableApi = new DatatableApi();

  httpModuleKey = 'datatable';

  /**
   * 查询数据源中的表
   * @param datasourceId 数据源id
   */
  getList(datasourceId: string): Promise<DatatableFile[]> {
    return this.http.get(this.api.list, { params: { datasourceId } }).then(responseData, responseReject);
  }

  /**
   * 查询数据源中的表 导入后的
   * @param datasourceId 数据源id
   */
  getImportList(datasourceId: string): Promise<DatatableFile[]> {
    return this.http.get(this.api.importList, { params: { datasourceId } }).then(responseData, responseReject);
  }

  /**
   * 根据表名及数据源id获取库表字段列表
   * @param datasourceId 数据源id
   * @param id 数据表id
   * @param tableName 表名
   */
  getFields(datasourceId: string, id: string, tableName: string): Promise<DatatableFields> {
    return this.http
      .get(this.api.fields, {
        params: { datasourceId, id, tableName }
      })
      .then(responseData, responseReject);
  }

  /**
   * 通过数据id查询查询tableName
   * @param datasetId
   * @returns
   */
  getListFromDataframe(datasetId: string): Promise<{ tableName: string }[]> {
    return this.http
      .get(this.api.getListFromDataframe, {
        params: { datasetId }
      })
      .then(responseData, responseReject);
  }
}

export const DatatableService = createSingleClass(DatatableServiceCtor);
