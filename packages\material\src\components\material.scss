$ht-material-cls: '#{$ht-prefix}-material';

.#{$ht-material-cls} {
  .q-layout,
  .q-page {
    min-height: 100% !important;
  }

  .#{$ht-prefix}-app-page--dense {
    .#{$ht-prefix}-app-page__main {
      margin-right: 0;
      padding: 16px;
    }

    .#{$ht-prefix}-app-page__sider + .#{$ht-prefix}-app-page__main {
      padding: 24px 16px;
    }
  }

  &-media {
    &__cover {
      position: relative;
      width: 128px;
      height: 108px;
      border: 1px solid transparent;
      background: url('../../assets/imgs/img-bg.png') repeat;
    }

    &.is-active &__cover {
      border-color: $primary;
    }

    &.is-active .#{$ht-prefix}-app-file__title {
      color: $primary;
    }

    &__type {
      padding: 2px;
      transform: scale(0.85);
      transform-origin: 0 0;
      border-top-left-radius: 4px;
      border-bottom-right-radius: 2px;
      font-size: 12px;
      text-transform: uppercase;
    }
  }

  &-geo {
    .q-tree {
      .q-tree__node-header {
        margin: 0;
        padding: 0;
        padding-left: 4px;
        border-bottom: 1px solid $separator-color;
        border-radius: 0;

        > .q-tree__arrow {
          display: none;
        }

        .q-tree__arrow {
          color: $grey;
        }

        .q-focus-helper {
          display: none;
        }
      }

      .q-tree__node--selected {
        @apply bg-primary-lighter;

        .q-focus-helper {
          display: none;
        }
      }

      .q-tree__node {
        padding: 0;
      }

      .q-tree__children,
      .q-tree__node--child {
        padding-left: 0;
      }
    }
  }
}

.body--dark .#{$ht-material-cls} {
  &-media__cover {
    background: rgb(255 255 255 / 7%);
  }

  &-media.is-active .#{$ht-material-cls}-media__cover {
    background: none;
  }
}
