/**
 * 数据集查询参数
 * <AUTHOR>
 */
export class QueryParam {
  /**
   * 字段中文名
   */
  alias?: string;
  /**
   * 参数字段名
   */
  paramName?: string;
  /**
   * 参数字段值
   */
  paramValue?: string;
  /**
   * 排序值
   */
  orderNo?: number;

  constructor(paramName?: string, paramValue?: string, orderNo?: number, alias?: string) {
    this.paramName = paramName;
    this.paramValue = paramValue;
    this.orderNo = orderNo;
    this.alias = alias;
  }
}

/**
 * 数据集查询参数对象数组
 * @type
 */
export type QueryParams = QueryParam[];
