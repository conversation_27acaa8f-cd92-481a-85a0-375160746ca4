import { computed, defineComponent, ref } from 'vue';
import {
  AttachmentService,
  SpaceService,
  TeamService,
  Team,
  type Teams,
  PageService,
  PlatformService
} from '@hetu/platform-shared';
import { SettingsService } from '@hetu/theme';
import { useQuasar } from 'quasar';
import { useDomain } from '../../hooks';
import { TokenService } from '@hetu/auth';

export default defineComponent({
  name: 'ht-space',
  props: {
    mini: Boolean
  },
  setup() {
    const $q = useQuasar();
    const acl = PlatformService.acl;

    // 当前空间标识
    const domain = useDomain();

    // 头像地址
    const avatar = (id: string) => AttachmentService.downloadFileUrl(id);

    // 用户信息
    const user = SettingsService.user;

    // 是否为子账号 loginType 账号类型:0-平台用户, 1-团队用户
    const isSubAccount = computed<boolean>(() => user?.loginType === 1);

    // 当前团队信息
    const activeTeam = computed(() => (domain.value ? TeamService.current : undefined));

    // 团队列表
    const teams = ref<Teams>([]);
    const loading = ref<boolean>(false);
    let loaded = false;

    const loadTeams = async () => {
      loading.value = true;
      try {
        const data = await TeamService.getTeams();
        teams.value = data?.filter((team) => !(team.owner && !team.teamType)) || [];
        loaded = true;
        // eslint-disable-next-line no-empty
      } catch (error) {}
      loading.value = false;
    };

    const showTeamsPopup = () => !loaded && loadTeams();

    // 切换空间
    const switchSpace = (spaceDomain?: string) => SpaceService.change(spaceDomain);

    // 创建团队
    const createTeam = () => {
      $q.dialog({
        title: '创建空间',
        message: '请输入空间名称',
        prompt: {
          model: '',
          isValid: (val) => val.length > 0 && val.length <= 30,
          type: 'text'
        },
        cancel: true
      }).onOk(async (data) => {
        const team = new Team();
        team.name = data;
        const res = await TeamService.create(team);
        if (res && res.status === 'success') {
          $q.notify({ message: res.message, type: 'positive', position: 'top' });
          PageService.toMain(res.data.domain);
          loadTeams();
        }
      });
    };

    // 退出团队
    const quitTeam = (team: Team) => {
      $q.dialog({
        title: '提示',
        message: `确定退出 [${team.name}]?`,
        cancel: true
      }).onOk(async () => {
        const res = await TeamService.leave(team.id);

        if (res && res.status === 'success') {
          $q.notify({ message: '退出成功', type: 'positive', position: 'top' });
          if (domain.value === team.domain) {
            PageService.toMain();
          }
          teams.value = [];
          loaded = false;
        }
      });
    };

    return {
      acl,
      domain,
      isSubAccount,
      avatar,
      user,
      activeTeam,
      loading,
      teams,
      showTeamsPopup,
      switchSpace,
      createTeam,
      quitTeam
    };
  }
});
