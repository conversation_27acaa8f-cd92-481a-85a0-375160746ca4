import { SettingsService } from '@hetu/theme';
import { getSpacePath } from '../utils';
import { ROUTE_DOMAIN_KEY } from '../models';
import { PlatformService, SpaceService } from '../services';
import type { RouteActivateGuard, RouteLocationNormalized } from 'vue-router';

let ignoreMySpace = false;

export const setIgnoreMySpace = (ignore = false) => {
  ignoreMySpace = ignore;
};

/**
 * 空间路由守卫: 根据当前平台空间模式拦截非法路由访问, 非云平台模式设置空间标识
 * - 云平台: 以域名区分空间
 * - 私有部署: 以路由参数区分空间
 */
export const useSpaceGuard: RouteActivateGuard = (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
  const domain = to.params[ROUTE_DOMAIN_KEY] as string;
  if (import.meta.env.DEV) {
    console.log('to:', to);
    console.log('from:', from);
    console.log('isDomainMode:', !!PlatformService.domain, 'isLocal:', to.meta.isLocal, 'domain:', domain);
  }

  if (PlatformService.domain) {
    // 云平台模式
    if (to.meta?.isLocal || (ignoreMySpace && !SpaceService.getDomainCode())) {
      if (from.path === '/404') {
        SettingsService.setFetching(false);
        return false;
      }
      return { path: '/404' };
    }

    const spaceRedirect = to.matched[to.matched.length - 1].meta.spaceRedirect as string;
    if (spaceRedirect) {
      return { path: spaceRedirect };
    }
  } else if (!domain) {
    SettingsService.setFetching(false);
    if (ignoreMySpace) {
      return { path: '/404' };
    }

    // 默认跳转至个人空间, 如根据当前路径获取到的个人空间路径不存在则进入 `/404`
    return { path: getSpacePath(to.path) };
  } else {
    SpaceService.set(domain);

    let spaceRedirect = to.matched[to.matched.length - 1].meta.spaceRedirect as string;
    if (spaceRedirect) {
      spaceRedirect = getSpacePath(spaceRedirect, domain);
      if (spaceRedirect === from.path) {
        SettingsService.setFetching(false);
      }

      return { path: spaceRedirect };
    }
  }

  return true;
};
