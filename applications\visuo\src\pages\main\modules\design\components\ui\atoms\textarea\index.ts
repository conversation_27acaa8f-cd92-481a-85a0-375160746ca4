import { computed, defineComponent, ref } from 'vue';

export default defineComponent({
  name: 'vis-textarea',
  props: {
    modelValue: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    const content = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        emit('update:modelValue', value);
      }
    });

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    return {
      content,
      popupRef,
      popupShow,
      showPopup
    };
  }
});
