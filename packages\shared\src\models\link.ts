export class Link {
  /** 类型 */
  linkType: LinkType = LinkType.Dashboard;

  /** 外链地址、大屏、看板id */
  link: string = '';

  /** 大屏或看板名称 */
  linkName: string = '';

  /** 打开方式 */
  target: LinkOpenTarget = LinkOpenTarget.Blank;

  /** 携带参数 */
  params: LinkParam[] = [];

  /** 弹窗设置 */
  targetOptions?: TargetOptions;

  /** 封面截图 */
  snapshot?: string;
}

/**
 * 跳转类型
 */
export enum LinkType {
  Url = 'url',
  Dashboard = 'dashboard',
  Screen = 'screen'
}

/**
 * 打开方式
 */
export enum LinkOpenTarget {
  Blank = 'blank',
  Self = 'self',
  /** 弹窗 */
  Dialog = 'dialog',
  /** 抽屉 */
  Drawer = 'drawer',
  /** 驾驶舱默认 */
  IFrame = 'iframe'
}
/**
 * 携带参数
 */
export class LinkParam {
  /** 参数类型：组件字段、固定值，全局参数 */
  type: 'fixed' | 'fields' | 'params' = 'fixed';
  /** 参数名 */
  name: string = '';
  /** 参数值 */
  value: string = '';
}

/**
 * 弹窗设置
 */
export class TargetOptions {
  /** 弹窗位置 */
  type: 'center' | 'left' | 'right' | 'bottom' | 'top' = 'center';
  /** 弹窗大小 */
  size: number[] | 'full' = 'full';
  /** 是否显示标题 */
  isShowTitle: boolean = false;
  /** 标题内容 */
  title?: string;
}
