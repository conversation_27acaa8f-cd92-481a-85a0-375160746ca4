import { useApp } from '@hetu/core';
import { CooperationACLDirective } from './directives/cooperation-acl.directive';
import type { App } from 'vue';

export * from './models';
export * from './utils';
export * from './hooks';
export * from './guards';
export * from './services';
export * from './components';

function install(app: App) {
  app.directive(CooperationACLDirective.name, CooperationACLDirective);
}

export function setupPlatformCooperation() {
  const app = useApp();
  install(app);
}

export const PlatformCooperationModule = { install };
