<template>
  <transition name="ht-transition--fade" mode="out-in">
    <ht-empty v-if="empty" description="~ 空空如也 ~" />
    <transition-group
      v-else
      :key="group"
      tag="div"
      class="ht-app-file-container row warp"
      :class="dense && 'ht-app-file-container--dense'"
      name="ht-transition-list"
    >
      <slot />
    </transition-group>
  </transition>
</template>

<style lang="scss">
.#{$ht-prefix}-app-file-container {
  margin-right: -6px;
  margin-left: -6px;

  &--dense {
    margin-right: -4px;
    margin-left: -4px;
  }
}
</style>

<script lang="ts" setup>
defineOptions({ name: 'ht-app-file-container' });
defineProps<{
  group: string | number;
  empty?: boolean | 0 | 1;
  dense?: boolean;
}>();
</script>
