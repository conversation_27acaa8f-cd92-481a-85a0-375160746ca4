import { Image, type IconOption } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';

/**
 * 图标状态组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-status-icon',
  props: {
    /**
     * 图标配置
     */
    statusOption: {
      type: Object as PropType<IconOption>,
      required: true
    }
  },
  setup(props) {
    const iconOption = ref(props.statusOption);
    const iconStyle = computed(() => props.statusOption);

    const handleUpdateIcon = (icon: IconOption) => {
      iconStyle.value.image = icon.image ? JSON.parse(JSON.stringify(icon.image)) : icon.image;
    };
    return {
      iconStyle,
      iconOption,
      handleUpdateIcon
    };
  }
});
