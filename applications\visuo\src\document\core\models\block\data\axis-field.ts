/**
 * 数据资源字段
 * <AUTHOR>
 */
export class AxisField {
  /**
   * 字段名
   */
  fieldName?: string;

  /**
   * 字段别名
   */
  fieldAlias?: string;

  /**
   * 函数
   */
  aggregator?: string;

  /**
   * 字段类型 `['dimension' | 'measure' | ...]`
   */
  fieldType?: string;

  /**
   * 下级字段
   */
  hierarchy?: Array<AxisField>;

  /**
   * 分组名
   */
  group?: string;

  /**
   * 上级字段别名
   */
  parentAlias?: string;

  /**
   * 字段的实际类型
   */
  dataType?: string;

  [key: string]: any;

  /**
   *
   * @param fieldName 字段名
   * @param fieldAlias 字段别名
   * @param aggregator 函数
   * @param fieldType 字段类型 `['dimension' | 'measure' | ...]`
   * @param parentAlias 上级字段别名
   * @param dataType 字段的实际类型
   */
  constructor(
    fieldName: string | undefined,
    fieldAlias: string | undefined,
    aggregator?: string,
    fieldType?: string,
    parentAlias?: string,
    dataType?: string
  ) {
    this.fieldName = fieldName;
    this.fieldAlias = fieldAlias;
    if (aggregator) {
      this.aggregator = aggregator;
    }
    if (fieldType) {
      this.fieldType = fieldType;
    }
    if (parentAlias) {
      this.parentAlias = parentAlias;
    }
    if (dataType) {
      this.dataType = dataType;
    }
  }
}

export type AxisFields = Array<AxisField>;

/**
 * 过滤筛选字段的过滤数据
 */
export interface FilterAxisField extends AxisField {
  /** 过滤值: `DataFilter` */
  filter: any;

  /** 过滤类型: `DataFilterType` */
  filterType: any;
}

/**
 * 排序字段的排序数据
 */
export interface OrderAxisField extends AxisField {
  /**
   * 过滤方式
   *
   * - `0`: 无
   * - `1`: 升序
   * - `2`: 降序
   */
  order: 0 | 1 | 2;
}
