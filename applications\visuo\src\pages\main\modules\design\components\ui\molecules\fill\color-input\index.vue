<template>
  <div class="vis-fill-color-input mt-0.5 vis-form-inline">
    <vis-select v-model="valueType" :options="valueOptions" class="w-16"></vis-select>
    <div class="flex-1 vis-form-inline__content">
      <div class="vis-fill__content flex rounded-borders">
        <!-- RGB模式 -->
        <template v-if="valueType === ColorValueType.RGB">
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="rgb.r"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="255"
            input-class="text-center"
            tooltip="R"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="rgb.g"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="255"
            input-class="text-center"
            tooltip="G"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="rgb.b"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="255"
            input-class="text-center"
            tooltip="B"
          ></vis-number>
        </template>

        <!-- HEX模式 -->
        <template v-else-if="valueType === ColorValueType.HEX">
          <q-input
            v-model="hex"
            @blur="checkHex"
            @keypress.enter="checkHex"
            maxlength="6"
            class="vis-form-field__content no-outline vis-number px-2"
            dense
            borderless
          />
        </template>

        <!-- HSB模式 -->
        <template v-else-if="valueType === ColorValueType.HSB">
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsb.h"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="360"
            input-class="text-center"
            tooltip="H"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsb.s"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="100"
            input-class="text-center"
            tooltip="S"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsb.v"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="100"
            input-class="text-center"
            tooltip="B"
          ></vis-number>
        </template>

        <!-- HSL模式 -->
        <template v-else-if="valueType === ColorValueType.HSL">
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsl.h"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="360"
            input-class="text-center"
            tooltip="H"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsl.s"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="100"
            input-class="text-center"
            tooltip="S"
          ></vis-number>
          <vis-number
            class="vis-form-field__content no-outline"
            v-model="hsl.l"
            @update:modelValue="changeColor"
            dense
            :min="0"
            :max="100"
            input-class="text-center"
            tooltip="L"
          ></vis-number>
        </template>

        <!-- Css模式 -->
        <template v-else-if="valueType === ColorValueType.CSS">
          <q-input
            v-model="css"
            @update:modelValue="changeColor"
            @blur="checkCss"
            @keypress.enter="checkCss"
            class="vis-form-field__content no-outline vis-number px-2"
            dense
            borderless
          />
        </template>

        <!-- 透明度 -->
        <q-input
          v-if="valueType !== ColorValueType.CSS"
          ref="alphaRef"
          borderless
          dense
          v-model="alpha"
          @focus="focusAlpha"
          @keypress.enter="updateAlpha"
          @blur="updateAlpha"
          class="vis-input vis-form-field__content no-outline !w-10 !flex-none"
        >
          <q-tooltip :offset="[0, 4]">A</q-tooltip>
        </q-input>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
