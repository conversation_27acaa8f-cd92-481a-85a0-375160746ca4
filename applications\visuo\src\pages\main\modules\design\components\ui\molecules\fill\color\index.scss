@import '../../../../index.scss';

// 颜色选择器
.#{$vis-prefix}-color-picker .q-color-picker {
  &__header {
    margin-bottom: 4px;
    border-radius: $border-radius;

    &-content {
      background: $input-bg;
      color: $icon-color;
    }
  }

  &__palette-rows {
    justify-content: flex-start;
  }

  &__cube {
    width: 16px !important;
    height: 16px;
    padding-bottom: 0;
    border: 1px solid rgba(0, 0, 0, 0.21);
    border-radius: $border-radius;
    margin: 0 9px 8px 0;

    &:nth-of-type(9n) {
      margin-right: 0;
    }
  }
}