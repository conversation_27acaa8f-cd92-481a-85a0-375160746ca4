import { ACLResource } from '@hetu/acl';
import { BaseModel } from '@hetu/http';

/**
 * 素材管理-地理坐标系对象
 * <AUTHOR>
 */
export class MaterialGeo extends BaseModel {
  id = '';
  name = '';
  /** 区域编码 */
  cityCode = '';
  parentId = '';
  /** 是否激活 */
  active: 0 | 1 = 1;
  /** 文件id */
  attachmentId = '';
  /** 文件名称 */
  fileName?: string;
}

export enum GeoACLResourceCode {
  /** 新建 */
  Create = 'create',
  /** 删除到回收站 */
  Delete = 'delete',
  /** 编辑 */
  Edit = 'edit',
  /** 保存 */
  Save = 'save',
  /** 状态 */
  Active = 'active'
}

export class GeoACLResource extends ACLResource<typeof GeoACLResourceCode> {
  readonly group = 'geoJson';

  get noEdit() {
    const code = this.resourceCode as typeof GeoACLResourceCode;
    return {
      resource: {
        [this.group]: [code.Edit]
      },
      except: true
    };
  }

  get noActive() {
    const code = this.resourceCode as typeof GeoACLResourceCode;
    return {
      resource: {
        [this.group]: [code.Active]
      },
      except: true
    };
  }

  constructor() {
    super();
    this.resourceCode = GeoACLResourceCode;
  }
}
