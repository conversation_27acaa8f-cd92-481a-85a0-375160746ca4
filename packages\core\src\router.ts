import type { Router, NavigationGuardReturn, RouteActivateGuard } from 'vue-router';
import { createRouter as _createRouter } from 'vue-router';

function createCanActivateHook(router: Router) {
  router.beforeEach((to, from) => {
    const len = to.matched.length;
    if (!len) return;

    let guards: RouteActivateGuard[] = [];
    to.matched.forEach((route, index) => {
      const { canActivate, canActivateChild } = route.meta;
      guards = guards.concat(canActivate || []);
      if (index !== len - 1 && canActivateChild) {
        guards = guards.concat(canActivateChild);
      }
    });
    if (!guards.length) return;

    let next = guards.shift() as RouteActivateGuard;
    const handle = async (guard: RouteActivateGuard): Promise<NavigationGuardReturn> => {
      let result = guard(to, from);

      if (result instanceof Promise) {
        result = await result;
      }

      if (result !== true && result !== void 0) {
        return result;
      }

      next = guards.shift() as RouteActivateGuard;
      return next ? handle(next) : result;
    };

    return handle(next);
  });
}

let router: Router;

export const createRouter: typeof _createRouter = (...args) => {
  router = _createRouter(...args);

  const install = router.install;

  router.install = (...arg) => {
    createCanActivateHook(router);

    return install.call(router, ...arg);
  };

  return router;
};

export const useRouterWithout = () => router;
