import { AbstractFileTree, type FileTreeNode } from '@hetu/platform-app';
import { type CooperationFile, type CooperationFileData, CooperationFileMode } from '../models';
import { SettingsService } from '@hetu/theme';

/**
 * 协作文件树
 */
export class CooperationFileTree<T extends CooperationFile> extends AbstractFileTree<T, CooperationFile> {
  get preconditions(): ((node: FileTreeNode<T, CooperationFile>) => boolean) | undefined {
    let fn;
    if (this.mode !== CooperationFileMode.All) {
      fn = (node: FileTreeNode<T, CooperationFile>) => {
        let result = true;

        switch (this.mode) {
          case CooperationFileMode.Creator:
            result = node.creatorId === this.creatorId;
            break;
          case CooperationFileMode.Collaborator:
            result = node.creatorId !== this.creatorId;
            break;
          case CooperationFileMode.Favorite:
            result = this.checkFavorite(node);
            break;
          default:
            break;
        }

        return result;
      };
    }

    return fn;
  }

  private mode = CooperationFileMode.All;

  private get creatorId() {
    return SettingsService.user.id;
  }

  /**
   * 设置分类模式
   * @param mode 模式
   */
  setMode(mode = CooperationFileMode.All) {
    this.mode = mode;
  }

  private checkFavorite(node: FileTreeNode<T, CooperationFile>) {
    const fn = (value = node): boolean => {
      const { favorite, parentId } = value;
      let result = !!favorite;
      if (!result && parentId && this.getFile(parentId)) {
        result = fn(this.getFile(parentId));
      }

      return result;
    };

    return fn();
  }
}
