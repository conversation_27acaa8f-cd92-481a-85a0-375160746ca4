<template>
  <ht-exception :type="403" @logout="logout" />
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import HtException from './exception.vue';
import { PageService, PassportService, SpaceService } from '@hetu/platform-shared';
import { TokenService } from '@hetu/auth';

defineOptions({ name: 'ht-exception-403' });

const $q = useQuasar();

function logout() {
  $q.dialog({
    title: '提示',
    message: '确认退出 ?',
    ok: '确定',
    cancel: '取消'
  }).onOk(() => {
    if (TokenService.loginUrl) {
      SpaceService.logout();
    } else {
      PassportService.logout().then(() => PageService.toLogin());
    }
  });
}
</script>
