const { normalizePath } = require('vite');
const path = require('path');
const { assetsDir, Applications } = require('@hetu/cli');

const resolve = (...dirs) => normalizePath(path.join(__dirname, '../', ...dirs));
const resolvePage = (...dirs) => normalizePath(path.join(__dirname.replace(path.resolve(''), ''), '../', ...dirs));

// function monacoEditorAssets() {
//   const arr = [];
//   const folders = [
//     '/base',
//     '/basic-languages/html',
//     '/editor',
//     '/language/json',
//     '/basic-languages/javascript',
//     '/language/typescript',
//     '/language/html'
//   ];
//   const src = resolve('node_modules/monaco-editor/min/vs'),
//     dest = `${assetsDir}/libs/monaco-editor/vs`;

//   folders.forEach((folder) => {
//     if (folder === '/editor') {
//       ['/editor.main.css', '/editor.main.js', '/editor.main.nls.zh-cn.js'].forEach((file) => {
//         arr.push({
//           src: resolve(src + folder + file),
//           dest: dest + folder
//         });
//       });
//     } else {
//       arr.push({
//         src: resolve(src + folder),
//         dest: dest + folder
//       });
//     }
//   });
//   arr.push({
//     src: resolve(src + '/loader.js'),
//     dest
//   });
//   return arr;
// }

exports.hooks = {
  sassAdditionalData: "@import '@vis/styles/variables.scss';",
  getPages() {
    return {
      main: {
        entry: resolvePage('src/pages/main/main.ts'),
        filename: `${Applications.Visuo}/index.html`
      },
      preview: {
        entry: resolvePage('src/pages/preview/main.ts'),
        filename: `${Applications.Visuo}/preview/index.html`
      }
      // share: {
      //   entry: resolvePage('src/pages/share/main.ts'),
      //   filename: `${Applications.Visuo}/share/index.html`
      // }
    };
  },
  getAssets() {
    return [
      {
        src: resolve(assetsDir, '/*'),
        dest: assetsDir
      }
      // ...monacoEditorAssets()
    ];
  },
  getAlias() {
    const data = {};
    const paths = require('../tsconfig.json').compilerOptions.paths;
    Object.keys(paths).forEach((key) => {
      data[key.replace('/*', '')] = resolve(paths[key][0].replace('/*', ''));
    });

    return data;
  },
  getManualChunks(id) {
    if (id.startsWith(normalizePath(resolve(`./src/document/base`)))) {
      return 'vi-base';
    } else if (id.startsWith(normalizePath(resolve(`./src/document/core`)))) {
      return 'vi-document-core';
    } else if (id.startsWith(normalizePath(resolve(`./src/pages/main/common`)))) {
      return 'vi-main-common';
    } else if (id.includes('@visactor')) {
      return 'visactor';
    }
  }
};
