export type ValidateResult = null | {
  illegalChar?: boolean;
  illegalFirstChar?: boolean;
  message: string;

  [key: string]: any;
};

/**
 * 非法字符验证
 * 由字母/数字/下划线组成
 * @param value 待验证值
 */
export function illegalChar(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[A-Za-z0-9_]*$/) ? null : { illegalChar: true, message: '只能由字母/数字/下划线组成' };
  }
  return result;
}

/**
 * 非法字符验证
 * 1.开头必须是字母
 * 2.只能由字母/数字/下划线组成
 * @param value 待验证值
 */
export function illegalFirstChar(value: string): ValidateResult {
  let result = null;
  if (value) {
    const firstChar = value.split('')[0];
    result = firstChar.match(/^[A-Za-z]$/) ? null : { illegalFirstChar: true, message: '开头必须是字母' };
    if (!result) {
      result = value.match(/^[A-Za-z][A-Za-z0-9_]*$/)
        ? null
        : { illegalChar: true, message: '只能由字母/数字/下划线组成' };
    }
  }
  return result;
}

/**
 * 字母验证
 * @param value 待验证值
 */
export function letter(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[a-zA-Z]+$/) ? null : { illegalChar: true, message: '只能由字母组成' };
  }
  return result;
}

/**
 * 密码校验验证 字符数字 @ # . ! $ _ =
 * @param value 待验证值
 */
export function passwordChar(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[a-zA-Z0-9@#.!$_=]+$/)
      ? null
      : { illegalChar: true, message: '只能由字母/数字及特殊字符(@#.!$_=)组成' };
  }
  return result;
}

/**
 * 字母数字验证
 * @param value 待验证值
 */
export function letterNumber(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[0-9a-zA-Z]+$/) ? null : { illegalChar: true, message: '只能由字母/数字组成' };
  }
  return result;
}

/**
 * 字母数字中文验证
 * @param value 待验证值
 */
export function letterNumberChinese(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[\u4E00-\u9FA5A-Za-z0-9]+$/)
      ? null
      : { illegalChar: true, message: '只能由字母/数字/中文组成' };
  }
  return result;
}

/** 是否为数字 */
export function isNum(value: string | number): boolean {
  return /^((-?\d+\.\d+)|(-?\d+)|(-?\.\d+))$/.test(value.toString());
}

/** 是否为整数 */
export function isInt(value: string | number): boolean {
  // tslint:disable-next-line:triple-equals
  return isNum(value) && parseInt(value.toString(), 10) == value;
}

/** 是否为小数 */
export function isDecimal(value: string | number): boolean {
  return isNum(value) && !isInt(value);
}

/** 是否为身份证 */
export function isIdCard(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/(^\d{15}$)|(^\d{17}([0-9]|X)$)/) ? null : { illegalChar: true, message: '身份证号不合法' };
  }
  return result;
}

/** 是否为手机号 */
export function isMobile(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^1[3-9]\d{9}$/) ? null : { illegalChar: true, message: '号码不合法' };
  }
  return result;
}

/**
 * 验证邮箱
 * @param value 待验证值
 */
export function isEmail(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/)
      ? null
      : { illegalChar: true, message: '邮箱格式错误' };
  }
  console.log('isEmail', result);
  return result;
}

/**
 * 验证邮政编码
 * @param value 待验证值
 */
export function isPostCode(value: string): ValidateResult {
  let result = null;
  if (value) {
    result = value.match(/^[0-9]{6}$/) ? null : { illegalChar: true, message: '邮政编码格式错误' };
  }
  return result;
}

/** 必填 */
export const required = (v: string | number) => !!(`${v}` && `${v}`.length);

/** 最大长度 */
export const maxLen = (v: string, maxLen: number) => !(v && v.length) || v.length <= maxLen;

/** 最小长度 */
export const minLen = (v: string, minLen: number) => !(v && v.length) || v.length >= minLen;

/** 最大值 */
export const max = (v: string | number, max: number) => {
  v = v + '';
  return !(v && v.length) || parseInt(v, 10) <= max;
};

/** 最小值 */
export const min = (v: string | number, min: number) => {
  v = v + '';
  return !(v && v.length) || parseInt(v, 10) >= min;
};
