<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>居中对齐</title>
    <g id="图标2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标" transform="translate(-52.000000, -131.000000)">
            <g id="居中对齐" transform="translate(52.000000, 131.000000)">
                <rect id="矩形备份-187" fill="#FFFFFF" opacity="0" x="0" y="0" width="16" height="16"></rect>
                <g id="编组-9" transform="translate(3.000000, 2.000000)">
                    <rect id="矩形备份-52" fill="#D3D4DB" x="4.5" y="0" width="1" height="12"></rect>
                    <polygon id="矩形备份-53" fill="#000000" transform="translate(4.968750, 3.937500) rotate(-270.000000) translate(-4.968750, -3.937500) " points="4.21875 -1.03125 5.71875 -1.03125 5.71875 8.90625 4.21875 8.90625"></polygon>
                    <polygon id="矩形备份-54" fill="#000000" transform="translate(4.968750, 8.062500) rotate(-270.000000) translate(-4.968750, -8.062500) " points="4.21875 4.96875 5.71875 4.96875 5.71875 11.15625 4.21875 11.15625"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>
