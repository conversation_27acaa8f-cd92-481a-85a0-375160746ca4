import type { FileData, FileInfo } from '@hetu/platform-app';
import type { Collaborator } from './collaborator';

/**
 * 协作文件信息
 */
export interface CooperationFile extends FileInfo {
  /**
   * 当前数据所有者
   * - 0: 非所有者
   * - 1: 所有者
   */
  owner: 0 | 1;

  /** 权重最高的协作身份ID */
  privilegeId: string;

  /** 协作者 */
  privileges: Collaborator[];
}

/**
 * 协作文件列表响应数据模型
 */
export type CooperationFileData<T extends CooperationFile> = FileData<T, CooperationFile>;

/**
 * 协作文件分类模式
 */
export enum CooperationFileMode {
  /** 全部 */
  All,
  /** 我创建的 */
  Creator,
  /** 我加入的 */
  Collaborator,
  /** 我的星标 */
  Favorite
}
