<template>
  <div class="vis-color-eyedropper">
    <q-btn v-if="isEye" flat dense @click="pickColor">
      <q-icon class="hticon-vis-eyedropper" />
      <q-tooltip :offset="[0, 4]">{{ isEye ? '取色器' : '浏览器不支持取色' }}</q-tooltip>
    </q-btn>
    <div v-else class="vis-color-eyedropper__color"></div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
