import { defineComponent, ref } from 'vue';
import { createQFormRules } from '@hetu/util';
import { SettingsService } from '@hetu/theme';
import { QInput, useQuasar } from 'quasar';
import { useRouterWithout } from '@hetu/core';
import { ROUTE_DOMAIN_KEY, type SpaceAccountConfig, type SpaceLoginModel, type ValidatorCode } from '../../models';
import { PassportService, SpaceService } from '../../services';

export default defineComponent({
  name: 'ht-space-login',
  setup() {
    if (self !== top) {
      // 在 iframe 中
      window.parent.postMessage({ login: true });
    }

    const router = useRouterWithout();
    const route = router.currentRoute.value;
    const $q = useQuasar();

    const title = (route.query.dataName as string) ?? '';
    const domain = (route.query[ROUTE_DOMAIN_KEY] as string) ?? '';
    SpaceService.set(domain);

    const formRef = ref();
    const loginModel = ref<SpaceLoginModel>({
      username: '',
      password: '',
      domain
    });

    const code = ref<ValidatorCode & { valid: boolean }>({ valid: false });
    const codeInputRef = ref<QInput>();
    const fetchCodeImg = async () => {
      const data = await PassportService.validatorCode();
      Object.assign(code.value, data ?? {});
    };

    const accountConfig = ref<SpaceAccountConfig>();
    SpaceService.getAccountConfig().then((config) => {
      if (!config) return;

      accountConfig.value = config;
      code.value.valid = !!config?.requireAccountVerified;
      if (code.value.valid) {
        fetchCodeImg();
        loginModel.value.code = '';
      }
    });

    const codeValidator = async () => {
      const result = await PassportService.loginCodeCheck(loginModel.value.code!, code.value.codeId!);
      if (result && result.status === 'error') {
        fetchCodeImg();
        return '验证码错误，请重新输入!';
      }
      return true;
    };
    const rules = createQFormRules<SpaceLoginModel>({
      username: [{ required: true, message: '请输入用户名' }],
      password: [{ required: true, message: '请输入密码' }],
      code: [{ required: true, message: '请输入验证码' }, { validator: codeValidator }]
    });

    const login = async () => {
      const success = await formRef.value.validate();
      if (!success) return;

      loginModel.value.codeId = code.value.codeId;
      const result = await SpaceService.login(loginModel.value);
      if (result?.status === 'success') {
        const user = await SpaceService.getProfile();
        SettingsService.setUser(user!);
        const redirect = route.query.redirect;
        if (!redirect) {
          $q.notify({ position: 'top', type: 'negative', message: '未获取到跳转信息' });
        }
        router.push(decodeURIComponent(redirect as string) ?? '/404');
      } else if (code.value.valid) {
        fetchCodeImg();
        loginModel.value.code = '';
      }
    };

    const sso = () => {
      if (!accountConfig.value?.ssoConfig) return;

      let url = location.href.split('#')[0];
      const redirect = route.query.redirect as string;
      url += '#' + decodeURIComponent(redirect);
      SpaceService.toSSO(url);
    };

    return {
      title,
      domain,

      code,
      codeInputRef,
      fetchCodeImg,

      accountConfig,

      formRef,
      loginModel,
      rules,

      login,
      sso
    };
  }
});
