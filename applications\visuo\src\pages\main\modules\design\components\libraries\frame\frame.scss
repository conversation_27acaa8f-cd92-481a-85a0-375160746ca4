.#{$vis-prefix}-frame {
  .#{$vis-prefix}-frame-title {
    position: absolute;
    top: -20px;
    left: 0;
    color: #909399;
    font-size: 12px;
    cursor: pointer;
    white-space: nowrap;

    input {
      padding: 0;
      border: none;
      outline: none;
      background: transparent;
      color: #909399;
    }
  }

  &.active {
    > .#{$vis-prefix}-frame-title {
      z-index: 99999;
      color: $primary;
    }
  }
  &.chosen-frame {
    > .wrap {
      outline: 1px dashed #4af;
    }
  }

  &.active,
  &.chosen-frame,
  &.active-frame {
    .#{$vis-prefix}-frame-grid-ghost {
      @apply w-full h-full;

      div {
        outline: 1px solid rgba(#4af, 0.2);

        &.active {
          background: rgba(#4af, 0.2);
        }
      }
    }
  }

  &.grid > .wrap {
    > .#{$vis-prefix}-graph.dragging {
      opacity: 0.1 !important;
    }
  }
}
