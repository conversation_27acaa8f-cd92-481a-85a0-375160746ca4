<template>
  <div class="vis-link relative">
    <div class="vis-form-inline">
      <div class="vis-form-inline__content">
        <div class="vis-form-field">
          <div class="vis-fill__content flex">
            <!-- 类型切换 -->
            <q-btn flat dense>
              <ht-icon class="vis-icon" :name="isUrl ? 'vis-associate' : 'vis-frame'" />
              <q-menu v-model="showMenuLink" style="width: 120px" class="vis-menu" dense>
                <q-list dense>
                  <q-item
                    :active="computedModel.type === LinkType.Url"
                    @click="changeType(LinkType.Url)"
                    clickable
                    v-close-popup
                  >
                    <q-item-section avatar>
                      <ht-icon name="vis-associate" />
                    </q-item-section>
                    <q-item-section>外链</q-item-section>
                  </q-item>
                  <q-item
                    :active="computedModel.type === LinkType.Frame"
                    @click="changeType(LinkType.Frame)"
                    clickable
                    v-close-popup
                  >
                    <q-item-section avatar>
                      <ht-icon name="vis-frame" />
                    </q-item-section>
                    <q-item-section>容器</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
            <q-separator vertical class="!m-0" />
            <!-- 外链输入框 -->
            <q-input
              v-if="isUrl"
              v-model="computedModel.link"
              :placeholder="isUrl ? '请输入外链地址' : ''"
              borderless
              class="rounded-borders flex-1 px-2"
            />
            <!-- 容器选择框 -->
            <vis-select
              v-else
              v-model="computedModel.link"
              :options="linkFrames"
              @update:model-value="changeFrame"
              class="flex-1 no-outline"
            >
              <template #selected>
                {{ linkFrames.find((item) => item.value === computedModel.link)?.label || '' }}
              </template>
              <template #no-option>
                <q-item>暂无容器</q-item>
              </template>
            </vis-select>
          </div>
        </div>
      </div>
    </div>

    <!-- 打开方式 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">打开方式</div>
          <div class="vis-form-field__content">
            <!-- <vis-button-group v-model="computedModel.target" :options="targetOptions" /> -->
            <vis-select v-model="computedModel.target" :options="targetOptions"></vis-select>
          </div>
        </div>
      </div>
    </div>

    <q-btn class="btn-field absolute bottom-0 right-0" :class="{ active: popupShow }" @click.stop="showPopup">
      <ht-icon class="vis-icon" name="vis-control" />
    </q-btn>

    <!-- 弹窗 -->
    <vis-popup ref="popupRef" title="弹窗配置" :target="false" @hide="hidePopup">
      <div class="vis-form-inline">
        <template v-if="isDialog">
          <!-- 弹窗大小 -->
          <div class="vis-form-field">
            <div class="vis-form-field__label">弹窗大小</div>
            <div class="vis-form-field__content">
              <vis-select
                class="w-full"
                v-model="size"
                :options="sizeOptions"
                @update:model-value="handleSize"
              ></vis-select>
            </div>
          </div>
          <!-- 弹窗尺寸 -->
          <div class="vis-form-field" v-if="size === 'custom'">
            <div class="vis-form-field__label">弹窗尺寸</div>
            <div class="vis-form-field__content">
              <vis-number
                v-model="sizeWidth"
                @update:model-value="handleSize"
                :min="0"
                icon="hticon-vis-letter-w"
              ></vis-number>

              <vis-number
                v-model="sizeHeight"
                @update:model-value="handleSize"
                :min="0"
                icon="hticon-vis-letter-h"
              ></vis-number>
            </div>
          </div>
          <!-- 弹窗位置 -->
          <div class="vis-form-field" v-if="size !== 'full'">
            <div class="vis-form-field__label">弹窗位置</div>
            <div class="vis-form-field__content">
              <vis-select v-model="computedModel.targetOption.position" :options="positionOptions" class="w-full" />
            </div>
          </div>
        </template>

        <!-- 弹窗标题 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">弹窗标题</div>
          <div class="vis-form-field__content">
            <q-input
              v-model="computedModel.targetOption.title"
              borderless
              class="vis-input px-2 rounded-borders w-full"
            />
          </div>
        </div>

        <!-- 行为设置 -->
        <template v-if="size !== 'full'">
          <div class="vis-form-field">
            <div class="vis-form-field__content row">
              <q-checkbox v-model="computedModel.targetOption.moveable" label="按住标题框可拖动弹窗" />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content row">
              <q-checkbox v-model="computedModel.targetOption.mask" label="弹窗显示遮罩" />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content row">
              <q-checkbox v-model="computedModel.targetOption.clickHide" label="点击弹窗外区域隐藏" />
            </div>
          </div>
        </template>
      </div>
      <q-separator class="w-full q-my-sm" />
      <div class="vis-form-inline">
        <!-- 链接参数 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">参数列表</div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-link-param v-model="computedModel.params" />
          </div>
        </div>
      </div>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
