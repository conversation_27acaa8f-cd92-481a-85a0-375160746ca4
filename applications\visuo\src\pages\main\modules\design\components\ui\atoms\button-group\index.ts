import { defineComponent } from 'vue';
import { ButtonGroup } from '../../../../models';

/**
 * 按钮组
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-button-group',
  props: {
    modelValue: {
      type: [String, Number],
      required: true
    },
    options: {
      type: Array<ButtonGroup>,
      required: true
    }
  },
  setup(props, { emit }) {
    const handleUpdate = (val: string | number) => {
      emit('update:modelValue', val);
      emit('change', val);
    };

    const handleHover = (val: string | number) => {
      emit('handleHover', val);
    };

    const isIconFont = (name: string) => {
      return name.startsWith('vis') || name.startsWith('hticon-vis');
    };

    const handleLeave = () => {
      emit('handleHover', false);
    };

    return {
      handleUpdate,
      isIconFont,
      handleHover,
      handleLeave
    };
  }
});
