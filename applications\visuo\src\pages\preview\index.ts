import { CacheService } from '@hetu/util';
import { setupGraph, useDocumentStore, Document, Page, Frame } from '@vis/document-core';
import { defineComponent, watch, ref, onBeforeUnmount, onMounted, computed } from 'vue';

/**
 * 设计预览
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-preview',
  props: {
    id: String
  },
  setup(props) {
    setupGraph();

    const docStore = useDocumentStore();
    docStore.mode.value = 'preview';

    const doc = computed(() => docStore.document.value);

    // 主容器
    const mainFrame = computed(() => docStore.mainFrame.value);

    const loadDocument = () => {
      if (props.id) {
        const doc = CacheService.getNone(`doc_${props.id}`) as Document;
        docStore.document.value = doc;

        console.log(doc);
      }
    };

    watch(
      () => props.id,
      () => {
        loadDocument();
      },
      {
        immediate: true
      }
    );

    return {
      mainFrame
    };
  }
});
