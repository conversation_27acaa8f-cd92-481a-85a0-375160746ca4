<template>
  <q-menu
    dense
    :style="{ width: `calc(${rightWidth}px - 56px)` }"
    :offset="[32, 8]"
    class="vis-menu"
    @hide="onClosePopup"
  >
    <q-input borderless v-model="search" :dense="false" class="vis-input mx-3 mt-3 px-2 rounded-borders">
      <template v-slot:prepend>
        <q-icon name="search" />
      </template>
    </q-input>
    <q-scroll-area :style="{ minHeight: '200px', height: `${fieldList.length * 30}px`, maxHeight: '300px' }">
      <q-list dense>
        <q-item
          v-for="element in filteredFieldList"
          :key="element.id"
          :active="selectedIds?.includes(element.id)"
          @click="onToggleSelect(element)"
          dense
          clickable
        >
          <q-item-section class="!pr-2" side>
            <ht-icon
              :name="`vis-${getFieldIcon(element.fieldDatatype)}`"
              class="vis-icon"
              :class="{ 'vis-icon--active': selectedIds?.includes(element.id) }"
            ></ht-icon>
          </q-item-section>
          <q-item-section>
            <q-item-label
              class="text-ellipsis overflow-hidden flex-shrink-1"
              :style="{ width: `calc(${rightWidth}px - 140px)` }"
              :lines="1"
            >
              {{ element.fieldAlias }}
            </q-item-label>
          </q-item-section>
        </q-item>

        <slot></slot>
        <div v-if="!filteredFieldList?.length" class="text-gray text-xs text-center px-4 py-6">暂无数据</div>
      </q-list>
    </q-scroll-area>
  </q-menu>
</template>

<script lang="ts" src="./field-list.ts"></script>
