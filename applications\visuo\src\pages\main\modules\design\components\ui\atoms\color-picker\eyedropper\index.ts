import { computed, defineComponent, onMounted, ref } from 'vue';
import { snapdom } from '@zumer/snapdom';
import { useFill } from '@vis/document-core';

export default defineComponent({
  name: 'vis-fill-picker',
  setup(props, { emit }) {
    const { hexToRgba } = useFill();

    const isEye = computed(() => {
      return 'EyeDropper' in window;
    });

    const pickColor = async (e: Event) => {
      if ('EyeDropper' in window) {
        // 使用原生 API
        const eyeDropper = new (window as any).EyeDropper() as {
          open: () => Promise<{ sRGBHex: string }>;
        };
        eyeDropper.open().then((result) => {
          const emitValue = hexToRgba(result.sRGBHex);
          emit('update:modelValue', emitValue);
        });
      }
    };

    return {
      pickColor,
      isEye
    };
  }
});
