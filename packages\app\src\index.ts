import { useApp } from '@hetu/core';
import type { App } from 'vue';
import { components } from './components';

export * from './hooks';
export * from './models';
export * from './utils';
export * from './services';
export * from './stores';
export * from './components';

function install(app: App) {
  // 全局注册组件
  components.forEach((component) => app.component(component.name, component));
}

export function setupPlatformApp() {
  const app = useApp();
  install(app);
}

export const PlatformAppModule = { install };
