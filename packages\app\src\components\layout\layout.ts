import { computed, defineComponent, watch, type PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { type Menu, NavService, MenuLinkType, MenuOpenTarget } from '@hetu/theme';
import { useQuasar } from 'quasar';
import HtResourcebar from '../resourcebar/resourcebar.vue';
import HtSpace from '../space/space.vue';
import { PageService, SpaceService } from '@hetu/platform-shared';

export default defineComponent({
  name: 'ht-app-layout',
  components: { HtResourcebar, HtSpace },
  props: {
    menus: Object as PropType<Menu[]>
  },
  setup(props) {
    const route = useRoute();
    const router = useRouter();
    const $q = useQuasar();
    const active = computed<string>(() => NavService.activeMenus[0]);
    const mini = computed(() => $q.screen.lt.md);
    const navs = computed(() => {
      const navs: Array<Menu & { isSeparator?: boolean }> = [];
      const groups: Record<string, typeof navs> = {};
      const maps: string[] = [];

      props.menus?.forEach((menu) => {
        let group = 'other';
        if (menu.remark) {
          try {
            const remark = JSON.parse(menu.remark);
            if (remark.group !== undefined) {
              group = remark.group;
            }
            // eslint-disable-next-line no-empty
          } catch (error) {}
        }

        !maps.includes(group) && maps.push(group);
        groups[group] = groups[group] || [];
        groups[group].push(menu);
      });

      maps.forEach((key: string, index: number) => {
        if (index && groups[key].length && navs.length) {
          navs.push({ isSeparator: true } as (typeof navs)[0]);
        }
        navs.push(...groups[key]);
      });

      return navs;
    });

    watch(
      () => route.path,
      () => {
        NavService.setActiveMenu(route);
      },
      { immediate: true }
    );

    /** 选中菜单 */
    function select(menu: Menu) {
      if (!menu.menuLink) {
        return;
      }

      let path = menu.menuLink;
      let isRoute = true;
      if (menu.linkType === MenuLinkType.Url) {
        switch (menu.target) {
          case MenuOpenTarget.Blank:
            window.open(menu.menuLink);
            isRoute = false;
            break;
          case MenuOpenTarget.Self:
            window.location.href = menu.menuLink;
            isRoute = false;
            break;
          case MenuOpenTarget.IFrame:
            path = ('/ipage/' + menu.id) as string;
            break;
          default:
            break;
        }
      }

      if (!isRoute || path === route.path) {
        return;
      }

      // eslint-disable-next-line @typescript-eslint/no-empty-function
      router.push({ path: path }).catch(() => {});
    }

    /**
     * 返回工作台
     */
    const onBackMain = () => {
      PageService.openMain(SpaceService.getDomainCode());
    };

    return {
      active,
      mini,
      navs,
      select,
      onBackMain
    };
  }
});
