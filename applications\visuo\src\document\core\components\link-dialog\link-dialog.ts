import { computed, defineComponent, ref, type PropType, type Ref } from 'vue';
import type { Link } from '../../models';
/**
 * 链接对话框
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-link-dialog',
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    link: {
      type: Object as PropType<Link>,
      required: true
    }
  },
  setup(props, { emit }) {
    const isFull = computed(() => props.link.targetOption?.size === 'full');

    const options: Ref<any> = ref({});
    if (props.link.targetOption) {
      const { size, position, title, mask, moveable, clickHide } = props.link.targetOption;

      if (isFull.value) {
        options.value.width = '100%';
        options.value.height = '100%';

        options.value.iframeWidth = '100%';
        options.value.iframeHeight = '100%';
      } else {
        options.value.width = `${size[0]}px`;
        options.value.height = `${size[1]}px`;

        options.value.iframeWidth = `${size[0]}px`;
        options.value.iframeHeight = `${title ? (size[1] as number) - 45 : size[1]}px`;
      }

      options.value.title = title;
      options.value.mask = mask;
      options.value.moveable = moveable;
      options.value.clickHide = clickHide;
      // 全屏模式、有标题、可移动、不可点击隐藏状态下需展示标题
      options.value.isShowTitle = isFull.value || title || moveable || !clickHide;

      options.value.position = isFull.value || position === 'center' ? 'standard' : position;

      options.value.link = (props.link as Link & { path: string }).path;
    }

    const onClose = () => {
      emit('onClose');
    };

    const handleShow = () => {
      if (isFull.value || !options.value.moveable) return;

      const popup = document.querySelector('.dialog-content') as HTMLElement;
      const title = document.querySelector('.dialog-content .dialog-title') as HTMLElement;

      let isDragging = false;
      let posX: number, posY: number, originX: number, originY: number;

      // 鼠标按下时开始拖动
      title.addEventListener('mousedown', (e) => {
        // 检查是否点击了标题栏（这里整个div都可拖动）
        isDragging = true;
        posX = e.clientX;
        posY = e.clientY;

        originX = popup.style.left ? parseInt(popup.style.left) : 0;
        originY = popup.style.top ? parseInt(popup.style.top) : 0;

        // 防止文本选中
        e.preventDefault();
      });

      // 鼠标移动时更新位置
      document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const left = (e as MouseEvent).clientX - posX + originX;
        const top = (e as MouseEvent).clientY - posY + originY;

        popup.style.left = `${left}px`;
        popup.style.top = `${top}px`;

        // 强制GPU加速
        popup.style.transform = 'translateZ(0)';
      });

      // 鼠标释放时停止拖动
      document.addEventListener('mouseup', () => {
        isDragging = false;
      });
    };

    return {
      isFull,
      options,
      onClose,

      handleShow
    };
  }
});
