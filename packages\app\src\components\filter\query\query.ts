import { defineComponent, ref, type PropType } from 'vue';
import { QueryFilter } from '../../../models';

/**
 * 筛选器-通配符
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-filter-query',
  props: {
    filter: {
      type: Object as PropType<QueryFilter>,
      required: true
    }
  },
  setup(props) {
    const queryFilter = ref(props.filter);

    const options = [
      {
        label: '包含',
        value: 1
      },
      {
        label: '开头为',
        value: 2
      },
      {
        label: '结尾为',
        value: 3
      },
      {
        label: '精确匹配',
        value: 4
      }
    ];

    const illegalVariable = (val: any) => {
      return /#\{.*?\}/.test(val) && !/^#\{[0-9a-zA-Z.]+\}$/.test(val);
    };
    const inputRules = [
      (val: any) => {
        if (illegalVariable(val)) {
          return '变量替换格式错误，只允许数字、字母和.';
        }
        return true;
      }
    ];

    const checkInput = () => {
      if (illegalVariable(queryFilter.value.values[0])) {
        queryFilter.value.values[0] = '';
      }
    };

    return { queryFilter, options, inputRules, checkInput };
  }
});
