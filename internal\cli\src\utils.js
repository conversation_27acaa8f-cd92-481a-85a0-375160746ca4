const args = require('minimist')(process.argv.slice(2));
const path = require('path');
const execa = require('execa');
const fs = require('fs');

exports.args = args;

const run = (bin, args, opts = {}) => execa(bin, args, { stdio: 'inherit', ...opts });
const syncRun = (bin, args, opts = {}) => execa.sync(bin, args, { stdio: 'inherit', ...opts });
exports.run = run;
exports.syncRun = syncRun;

const assetsDir = `./static-next`;
exports.assetsDir = assetsDir;

exports.assetsPath = (_path) => `${assetsDir}/${_path}`;

exports.isProduction = process.env.NODE_ENV === 'production';

exports.getRootPath = () => {
  const current = process.cwd();
  let result;
  const dirs = current.split(current.includes('\\') ? '\\' : '/');
  let len = dirs.length;
  while (dirs.length) {
    const pkgPath = `${dirs.join('/')}/package.json`;
    if (fs.existsSync(pkgPath)) {
      const pkg = require(pkgPath);
      if (pkg.root) {
        result = dirs.join('/');
        break;
      }
    }
    len--;
    dirs.length = len;
  }
  return result;
};

/**
 * 拼接执行命令目录下的路径
 * @param {string} dirs
 */
exports.resolve = function (...dirs) {
  return path.join(process.cwd(), '/', ...dirs);
};

exports.getBranch = (path = './') => {
  const { stdout } = syncRun('git', ['branch', '--show-current'], { cwd: path, stdio: 'pipe' });
  return stdout;
};
