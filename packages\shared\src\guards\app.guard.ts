import { ACLService } from '@hetu/acl';
import { MYSPACE_CODE } from '../models';
import { EXCEPTION_ROUTES } from '../routes';
import {
  AppService,
  PageService,
  PermissionsService,
  PlatformService,
  RouteMenuService,
  SpaceService,
  TeamService
} from '../services';
import type { RouteActivateGuard, RouteLocationNormalized } from 'vue-router';
import { useRouterWithout } from '@hetu/core';
import { SettingsService } from '@hetu/theme';
import { getSpacePath } from '../utils';
import { setIgnoreMySpace } from './space.guard';
import { Notify } from 'quasar';

/**
 * 路由守卫: 验证应用权限
 */
export const useAppGuard: RouteActivateGuard = async (to: RouteLocationNormalized) => {
  if (to.name !== '403') {
    const result = await AppService.can();
    if (!result) {
      return { path: '/403' };
    }
  }

  return true;
};

/**
 * 路由守卫: 应用路由权限控制 - 基于菜单配置
 * <AUTHOR>
 */
export const useAppRouteGuard: RouteActivateGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized
) => {
  if (!RouteMenuService.initialized) {
    // 非空间路由时 code 为 undefined, 个人空间时为空字符串
    const code = SpaceService.getDomainCode(to);
    let spacePath = '';
    if (!PlatformService.domain && code !== undefined) {
      spacePath = getSpacePath('/', code || undefined);
    }
    await RouteMenuService.loadMenus(spacePath);
  }

  const result = RouteMenuService.can(to);
  if (typeof result === 'object' && result.path === from.path) {
    SettingsService.setFetching(false);
    return false;
  }

  return result;
};

/**
 * 应用全局解析守卫, 用于查询信息:
 * - 当前空间状态
 * - 当前团队信息(如果是团队空间, 用于展示)
 * - 应用资源权限配置(用于页面中操作按钮等资源的权限验证)
 */
export const createAppBeforeResolveGuard = () => {
  const router = useRouterWithout();
  router.beforeResolve(async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    if (EXCEPTION_ROUTES.find((r) => r.path === to.path)) {
      return;
    }

    const all = [];
    const domainCode = SpaceService.getDomainCode();
    const user = SettingsService.user;

    // 账号类型是子账号时忽略个人空间: loginType 账号类型:0-平台用户, 1-团队用户
    const isSubAccount = user.loginType === 1;
    if (isSubAccount) {
      setIgnoreMySpace(true);
    }

    const status = await SpaceService.getSpaceStatus();
    // 当租户状态为禁用时
    if (status && !status.tenantStatus) {
      Notify.create({
        type: 'negative',
        message: '当前空间已禁用',
        position: 'top',
        timeout: 0
      });

      // 团队空间/子账号/非当前个人空间所有者, 跳转无权限
      if (status.tenantType || isSubAccount || (domainCode && domainCode !== MYSPACE_CODE)) {
        return from.name === '403' ? undefined : { path: '/403' };
      }

      // 当前个人空间所有者跳转工作台页面选择空间
      if (AppService.appKey !== import.meta.env.VITE_HETU_PLATFORM_KEY) {
        PageService.toMain();
        return false;
      }
    }

    if (domainCode && domainCode !== MYSPACE_CODE) {
      // 查询当前团队信息
      all.push(TeamService.getTeam());
    }

    if (!PermissionsService.initialized) {
      // 查询应用资源权限配置
      const apps = [AppService.appKey];
      if (import.meta.env.VITE_HETU_PLATFORM_KEY !== AppService.appKey) {
        apps.push(import.meta.env.VITE_HETU_PLATFORM_KEY);
      }
      const permissions = PermissionsService.getConfig(...apps);
      permissions.then((data) => {
        PermissionsService.initialized = true;
        data && ACLService.set(data);
      });
      all.push(permissions);
    }

    if (all.length) {
      await Promise.all(all);
    }
  });
};
