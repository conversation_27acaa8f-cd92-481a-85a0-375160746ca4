import { FormRule, Label } from '.';
import { Font } from '../../ui';
import { WidgetName, WidgetType } from '../config/widget-enum';
import { WidgetBlock } from '../widget-block';

export class Input extends WidgetBlock {
  type = WidgetType.Input;

  name = WidgetName.Input;

  options: InputOptions = new InputOptions();
}

export class InputOptions {
  /** 标签 */
  label?: Label;
  /** 类型 */
  type = InputType.Text;
  /** 默认值 */
  defaultValue = '';
  /** 占位符 */
  placeholder = '请输入';
  /** 是否显示清除按钮 */
  clearable = false;
  /** 是否禁用 */
  disabled = false;
  /** 查询器 */
  query?: InputQuery;
  /** 样式 */
  style: {
    /** 输入值字体 */
    font?: Font;
    /** 内边距 */
    padding?: [number, number, number, number];
    /** 前缀 */
    prefix?: FormFix;
    /** 后缀 */
    suffix?: FormFix;
  } = {};
  rules: FormRule[] = [];
}

/** 查询器 */
export class InputQuery {
  /** 查询器类型 */
  type = InputQueryType.Include;
  /** 是否显示 */
  show = true;
  /** 是否锁定 */
  locked = false;
}

/** 前后缀 */
export class FormFix {
  /** 是否显示 */
  show = true;
  /** 文本内容 */
  text: string = '';
  /** 图标 */
  icon: string = '';
  /** 设置前后缀字体大小和颜色等 */
  font = new Font();
}
/**
 * 输入框类型
 */
export enum InputType {
  Text = 'text',
  Url = 'url',
  Email = 'email',
  Tel = 'tel'
}

/**
 * 查询器类型
 */
export enum InputQueryType {
  Include = 'include',
  StartWith = 'startWith',
  EndWith = 'endWith',
  Equal = 'equal',
  Exclude = 'exclude',
  NotStartWith = 'notStartWith',
  NotEndWith = 'notEndWith',
  NotEqual = 'notEqual'
}

export const InputQueryTypeOptions = [
  { label: '包含', value: InputQueryType.Include },
  { label: '开头为', value: InputQueryType.StartWith },
  { label: '结尾为', value: InputQueryType.EndWith },
  { label: '精准匹配', value: InputQueryType.Equal },
  { label: '不包含', value: InputQueryType.Exclude },
  { label: '开头不是', value: InputQueryType.NotStartWith },
  { label: '结尾不是', value: InputQueryType.NotEndWith },
  { label: '不匹配', value: InputQueryType.NotEqual }
];
