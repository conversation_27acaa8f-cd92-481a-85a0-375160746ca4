import { getCssVar, colors, type colorsRgba } from 'quasar';

export const getBrandColors = () => {
  const brands = ['primary', 'secondary', 'accent', 'positive', 'negative', 'info', 'warning'];
  return brands.map((brand) => getCssVar(brand) as string);
};

/**
 * 判断颜色是否是暗色
 * - 当颜色的相对亮度 `<= 0.4` 时为暗色
 * @param color
 */
export const isDarkColor = (color: string | colorsRgba) => {
  const { luminosity } = colors;
  return luminosity(color) <= 0.4;
};
