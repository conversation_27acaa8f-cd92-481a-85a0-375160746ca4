import { ref } from 'vue';
import { defineStore } from '@hetu/core';
import { DesignAction } from '../models';

/**
 * 工具栏操作
 * <AUTHOR>
 */
export const useActionStore = defineStore(() => {
  const actions = ref<Record<string, DesignAction>>(
    Object.assign(
      {},
      new DesignAction('move', '相交选中', [], 'o_near_me', true, 'mouse').record,
      new DesignAction('hand', '平移画布', [], 'o_back_hand', false, 'mouse').record,
      new DesignAction('scale', '等比缩放', [], 'o_photo_size_select_small', false, 'mouse').record,
      new DesignAction('frame', '容器', [], 'hticon-vis-frame ', false, 'mouse').record,
      new DesignAction('textbox', '文本', [], 'hticon-vis-textbox', false, 'mouse').record,
      new DesignAction('design', '设计模式', [], 'hticon-vis-mode-d', true, 'mode').record,
      new DesignAction('dhyana', '禅模式', [], 'hticon-vis-mode-s', false, 'mode').record,
      new DesignAction('node', '节点编程模式', [], 'o_cable', false, 'mode').record,
      new DesignAction('interact', '互动模式', [], 'o_touch_app', false, 'mode').record,
      new DesignAction('ai', 'AI', [], '').record
    )
  );

  // meta + ctrl键是否按下
  const isMetaCtrl = ref(false);

  return {
    actions,

    isMetaCtrl
  };
});
