import { createApp } from '@hetu/core';
import { preloader } from '@hetu/theme';
import { startup } from '@hetu/platform-boot';
import { AppRoot, setupPlatformShared } from '@hetu/platform-shared';
import { setupPlatformApp } from '@hetu/platform-app';
import { setupPlatformCooperation } from '@hetu/platform-cooperation';
import { setupRouter } from './router';

import '@vis/styles/index.scss';

async function bootstrap() {
  preloader();

  const app = createApp(AppRoot);

  setupPlatformShared({ appKey: import.meta.env.VITE_HETU_VISUO_KEY });

  setupPlatformApp();

  setupPlatformCooperation();

  await startup();

  await setupRouter();

  app.mount('#q-app');

  window.appBootstrap();
}

bootstrap();
