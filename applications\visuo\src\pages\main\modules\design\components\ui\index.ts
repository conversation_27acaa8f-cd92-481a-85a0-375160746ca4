import { useApp } from '@hetu/core';
import { type App } from 'vue';
/**
 * ui组件全局注册
 */
let isRegister = false;
function install(app: App) {
  // 全局注册组件
  const components = import.meta.glob('./**/*.vue', { eager: true });
  for (const [path, module] of Object.entries(components)) {
    const component = (module as any).default;

    const componentName = component.name.replace(/(?:^|-)(\w)/g, (_: string, c: string) => c.toUpperCase());
    app.component(componentName, component);
  }
  isRegister = true;
}

export function setupUi() {
  const app = useApp();
  !isRegister && install(app);
}
