// 引入共享变量和样式
@import '../../index.scss';

.#{$vis-prefix}-config-body {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 48px);
  padding: 0px;

  &-control {
    @apply h-10 flex justify-between items-center m-0 border-0 border-b border-gray-200 border-solid;
    padding: 8px 12px;
  }

  &-tabs {
    :deep(.q-tabs__content) {
      gap: $field-gap;
    }

    .q-tab {

      &:hover {
        background-color: rgba($input-bg, 0.8);
      }

      &--active {
        background-color: $input-bg;
        color: rgba($dark, 0.88);
      }
    }

  }
}
