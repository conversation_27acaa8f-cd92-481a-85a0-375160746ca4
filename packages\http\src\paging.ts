/**
 * 查询排序
 */
export interface QueryOrder<T = string> {
  /** 排序字段名称 */
  column: T;

  /** 排序规则 */
  sorting: OrderType;
}

export enum OrderType {
  /** 升序 */
  Asc = 'asc',
  /** 降序 */
  Desc = 'desc'
}

export const OrderTypeText = {
  [OrderType.Asc]: '升序',
  [OrderType.Desc]: '降序'
};

/**
 * 分页查询参数:
 * - `T` is type of filters
 */
export class PagingQuery<T = Record<string, any>> {
  /** 每页条数, 默认 `10` */
  pageSize = 10;

  /** 当前页码: 默认 `1` */
  pageNum = 1;

  /** 排序规则 */
  orders: QueryOrder[] = [];

  /** 过滤条件 */
  filters: T;

  constructor(filters: T = {} as T) {
    this.filters = filters;
  }

  [key: string]: any;
}

/**
 * 分页请求响应数据
 */
export interface PagingData<T = any> {
  /** 总数 */
  total: number;

  /** 每页记录 */
  records: T[];

  [key: string]: any;
}
