import { ACLService } from './acl.service';
import type { NavigationGuardReturn, RouteActivateGuard, RouteLocationNormalized } from 'vue-router';
import { useACLConfig } from './config';

const can = (route: RouteLocationNormalized): NavigationGuardReturn => {
  const can = ACLService.canRoute(route);

  if (can) return;

  const config = useACLConfig();
  return {
    path: config.guardUrl,
    query: {
      redirect: route.fullPath
    }
  };
};

/**
 * 路由权限守卫: 基于导航菜单数据进行验证
 * @description 在单个路由配置信息 `meta` 中添加 `acl: false` 可忽略此守卫
 * <AUTHOR>
 */
export const useACLGuard: RouteActivateGuard = (to: RouteLocationNormalized) => {
  let result = can(to);
  if (typeof result === 'object') {
    // 当 `token` 失效后路由自动跳转至登录页面会携带原路由信息作为登录后的自动跳转地址
    // 此时如果重新登录的用户没有原路由权限则应跳转至默认路由页面
    if (to.query && to.query.redirectedByLogin) {
      result = { path: '/' };
    }
    // result.path === from.path && (this.settingsSrv.isFetching = false);
  }

  return result;
};
