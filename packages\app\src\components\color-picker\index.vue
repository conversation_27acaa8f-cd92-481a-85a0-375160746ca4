<template>
  <div class="ht-color-picker flex items-center">
    <div
      class="ht-color-picker__color rounded"
      :class="`bg-${!isHexOrRgb && modelValue}`"
      :style="isHexOrRgb ? { 'background-color': modelValue } : {}"
    >
      <q-menu v-model="visible" class="ht-color-picker__panel">
        <q-color v-model="value" no-header no-footer format-model="rgba" />
        <div class="row justify-between p-2 pt-0">
          <div class="col-6">
            <q-input v-model="value" outlined class="vis-field--mini" />
          </div>
          <div class="col-6 text-right">
            <q-btn flat size="sm" label="清空" @click="onClear" />
            <q-btn unelevated size="sm" color="primary" label="确定" @click="onSure" />
          </div>
        </div>
      </q-menu>
    </div>
    <slot>
      <span class="ml-2" v-if="label">{{ label }}</span>
    </slot>
  </div>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue';

/**
 * 颜色选择器
 * <AUTHOR>
 */
defineOptions({ name: 'ht-color-picker' });

const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  label: {
    type: String
  }
});
const emit = defineEmits(['update:modelValue']);

const value = ref('');

const visible = ref(false);

const isHexOrRgb = computed(() => {
  const color = props.modelValue?.toLocaleLowerCase();
  return color.startsWith('#') || color.startsWith('rgb');
});

watch(
  () => props.modelValue,
  () => {
    value.value = props.modelValue;
  },
  {
    immediate: true
  }
);

const onSure = () => {
  emit('update:modelValue', value.value);
  visible.value = false;
};

const onClear = () => {
  value.value = '';
};
</script>
<style lang="scss">
.#{$ht-prefix}-color-picker {
  @apply h-7;

  &__color {
    @apply h-4 w-4 border border-solid rounded cursor-pointer;

    border-color: $separator-color;
  }

  &__panel {
    .q-color-picker {
      @apply shadow-none;
    }
  }
}
</style>
