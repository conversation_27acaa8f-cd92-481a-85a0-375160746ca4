import type { LazyResult } from './interface';

/**
 * 懒加载服务类
 * <AUTHOR>
 */
export class LazyService {
  private static list: Record<string, boolean> = {};
  private static cached: Record<string, LazyResult> = {};
  private static waiting: Record<string, Array<(value: LazyResult | PromiseLike<LazyResult>) => void>> = {};

  static has(key: string): boolean | 'loading' {
    let has: boolean | 'loading' = false;
    if (this.list[key]) {
      has = 'loading';
      if (this.cached[key]) {
        has = true;
      }
    }
    return has;
  }

  static clear(): void {
    this.list = {};
    this.cached = {};
  }

  /**
   * 加载资源文件
   * @param paths 路径
   */
  static load(paths: string | string[]): Promise<LazyResult[]> {
    if (!Array.isArray(paths)) {
      paths = [paths];
    }

    const promises: Array<Promise<LazyResult>> = [];
    paths.forEach((path) => {
      if (path.endsWith('.js')) {
        promises.push(this.loadScript(path));
      } else {
        promises.push(this.loadStyle(path));
      }
    });

    return Promise.all(promises).then((res) => {
      return Promise.resolve(res);
    });
  }

  /**
   * 加载脚本文件
   * @param path 路径
   * @param innerContent 脚本
   */
  static loadScript(path: string, innerContent?: string): Promise<LazyResult> {
    return new Promise((resolve) => {
      if (this.list[path] === true) {
        if (this.cached[path]) {
          resolve(this.cached[path]);
        } else {
          // 等待加载完成
          const waitings = (this.waiting[path] = this.waiting[path] || []);
          waitings.push(resolve);
        }
        return;
      }

      this.list[path] = true;
      const onSuccess = (item: LazyResult) => {
        this.cached[path] = item;
        resolve(item);
        if (this.waiting[path]) {
          this.waiting[path].forEach((_resolve) => _resolve(item));
          delete this.waiting[path];
        }
      };

      const node = document.createElement('script') as any;
      node.type = 'text/javascript';
      node.src = path;
      node.charset = 'utf-8';
      if (innerContent) {
        node.innerHTML = innerContent;
      }
      if (node.readyState) {
        // IE
        node.onreadystatechange = () => {
          if (node.readyState === 'loaded' || node.readyState === 'complete') {
            node.onreadystatechange = null;
            onSuccess({
              path,
              loaded: true,
              status: 'ok'
            });
          }
        };
      } else {
        node.onload = () =>
          onSuccess({
            path,
            loaded: true,
            status: 'ok'
          });
      }
      node.onerror = (error: any) =>
        onSuccess({
          path,
          loaded: false,
          status: 'error',
          error
        });
      document.getElementsByTagName('head')[0].appendChild(node);
    });
  }

  /**
   * 加载样式文件
   * @param path 路径
   * @param rel 文档类型
   * @param innerContent 样式
   */
  static loadStyle(path: string, rel = 'stylesheet', innerContent?: string): Promise<LazyResult> {
    return new Promise((resolve) => {
      if (this.list[path] === true) {
        resolve(this.cached[path]);
        return;
      }

      this.list[path] = true;

      const node = document.createElement('link') as HTMLLinkElement;
      node.rel = rel;
      node.type = 'text/css';
      node.href = path;
      if (innerContent) {
        node.innerHTML = innerContent;
      }
      document.getElementsByTagName('head')[0].appendChild(node);
      const item: LazyResult = {
        path,
        loaded: true,
        status: 'ok'
      };
      this.cached[path] = item;
      resolve(item);
    });
  }
}
