import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { Input, Block, Text, TextAdapt, InputQueryTypeOptions, FormFix, FormRuleType } from '../../../../models';
import { useDocumentStore } from '../../../../stores';
import { useUiStyle } from '../../../../hooks';
import { isEmail, isMobile, maxLen, minLen, required, type Records } from '@hetu/util';
import { isNumber } from 'lodash-es';

export default defineComponent({
  name: 'vis-input',
  props: {
    widget: {
      type: Object as PropType<Input>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props, { emit }) {
    const docStore = useDocumentStore();
    const { getTextStyle } = useUiStyle();

    const computedOptions = computed(() => {
      return props.widget.options;
    });

    const isDesign = computed(() => docStore.mode.value === 'design');
    // 标签显示位置在上
    const isRow = computed(() => {
      return computedOptions.value.label?.position === 'top';
    });

    const inputValue = ref(computedOptions.value.defaultValue);
    watch(
      () => computedOptions.value.defaultValue,
      (val) => {
        inputValue.value = val;
      }
    );
    const onValueChange = () => {
      emit('change', inputValue.value, queryValue.value);
    };

    const queryValue = ref(computedOptions.value.query?.type);
    watch(
      () => computedOptions.value.query?.type,
      (val) => {
        queryValue.value = val;
      }
    );

    const handleClear = () => {
      inputValue.value = '';
      emit('clear');
    };

    // 标签样式
    const labelStyle = computed(() => {
      const style: Records = {};

      if (computedOptions.value.label?.show) {
        const { width, padding, font, align } = computedOptions.value.label;
        Object.assign(style, getTextStyle({ ...(font as unknown as Text), alignHorizontal: align }));
        style.width = isRow.value ? '100%' : isNumber(width) ? `${width}px` : width;
        style.padding = `${padding.join('px ')}px`;

        // 解析展示样式
        switch (font.adapt) {
          case TextAdapt.Ellipsis:
            style.display = 'block';
            style.whiteSpace = 'nowrap';
            style.overflow = 'hidden';
            style.textOverflow = 'ellipsis';
            break;
          case TextAdapt.Fixed:
            style.maxHeight = '100%';
            style.overflow = 'hidden';
            break;
          case TextAdapt.Single:
            style.width = 'auto';
            style.whiteSpace = 'nowrap';
            break;

          default:
            break;
        }
      }
      return style;
    });

    // 输入框文本样式
    const inputStyle = computed(() => {
      const style: Records = {};
      if (computedOptions.value.style) {
        const { font } = computedOptions.value.style;
        if (font) {
          Object.assign(style, getTextStyle(font as unknown as Text));
        }
      }

      return style;
    });

    // #region 前后缀
    // 前缀类型
    const prefixType = computed(() => {
      if (!computedOptions.value.style?.prefix) return false;

      const { show, icon } = computedOptions.value.style.prefix as FormFix;
      if (show) {
        return icon ? 'icon' : 'text';
      }
      return false;
    });
    // 前缀图标样式待解析
    // 前缀文本样式
    const prefixTextStyle = computed(() => {
      if (!computedOptions.value.style?.prefix) return {};
      return getTextStyle(computedOptions.value.style.prefix?.font as unknown as Text);
    });

    // 后缀类型
    const suffixType = computed(() => {
      if (!computedOptions.value.style?.suffix) return false;

      const { show, icon, text } = computedOptions.value.style.suffix as FormFix;
      if (show) {
        return icon ? 'icon' : text ? 'text' : false;
      }
      return false;
    });
    // 后缀图标样式待解析
    // 后缀文本样式
    const suffixTextStyle = computed(() => {
      if (!computedOptions.value.style?.suffix) return {};
      return getTextStyle(computedOptions.value.style.suffix?.font as unknown as Text);
    });
    // #endregion

    // #region 校验规则
    const lazyRules = ref<any>([]);
    if (!isDesign.value) {
      if (computedOptions.value.rules?.length > 0) {
        const isUrl = (value: string) => {
          let result = null;
          if (value) {
            result = value.match(/^(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$/)
              ? null
              : { illegalChar: true, message: '网址格式错误' };
          }
          return result;
        };

        lazyRules.value = computedOptions.value.rules.map((rule) => {
          switch (rule.type) {
            case FormRuleType.Required:
              return (val: string) => required(val) || '必填';
            case FormRuleType.Email:
              return (val: string) => isEmail(val)?.message;
            case FormRuleType.Url:
              return (val: string) => isUrl(val)?.message;
            case FormRuleType.Tel:
              return (val: string) => isMobile(val)?.message;
            case FormRuleType.MinLength:
              return (val: string) => minLen(val, rule.value || 0) || '长度不能小于' + rule.value;
            case FormRuleType.MaxLength:
              return (val: string) => maxLen(val, rule.value || 0) || '长度不能大于' + rule.value;
            default:
              break;
          }
        });
      }
    }
    // #endregion

    // 组件方法
    const inputRef = ref();
    const clear = () => {
      inputValue.value = '';
      onValueChange();
    };

    const setValue = (val: any) => {
      if (inputValue.value === val) return;

      inputValue.value = val;
      onValueChange();
    };

    const validate = () => {
      inputRef.value?.validate();
    };
    //

    return {
      computedOptions,
      isDesign,
      inputValue,
      onValueChange,
      queryValue,
      handleClear,
      isRow,

      labelStyle,
      inputStyle,
      InputQueryTypeOptions,

      prefixType,
      prefixTextStyle,
      suffixType,
      suffixTextStyle,

      lazyRules,

      inputRef,
      clear,
      setValue,
      validate
    };
  }
});
