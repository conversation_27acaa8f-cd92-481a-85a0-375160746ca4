import { merge } from 'lodash-es';

/**
 * 服务端请求配置
 */
interface Api {
  /** 服务端请求路径 */
  server?: string;

  /** 服务端请求路径前缀 */
  prefix?: string;

  /** 服务端请求路径后缀 */
  suffix?: string;
}

export interface RootHttpApi extends Api {
  modules?: Record<string, HttpApi>;
}

/**
 * API 配置
 */
export interface HttpApi extends Api {
  [key: string]: any;
}

let apiConfig: undefined | RootHttpApi;
let apiConfigFn: undefined | (() => RootHttpApi);

export function setupApiConfig(config: RootHttpApi | (() => RootHttpApi)) {
  if (typeof config === 'function') {
    apiConfigFn = config;
  } else {
    apiConfig = config;
  }
}

export function setApiConfig(config: RootHttpApi | HttpApi, moduleKey?: string) {
  apiConfig = apiConfig || ({} as RootHttpApi);
  if (moduleKey) {
    apiConfig.modules = apiConfig.modules || ({} as Record<string, HttpApi>);
    apiConfig.modules[moduleKey] = config as HttpApi;
  } else {
    apiConfig = config;
  }
}

export function useApiConfig() {
  if (apiConfigFn && typeof apiConfigFn === 'function') {
    const config = apiConfigFn();

    if (config) {
      apiConfig = apiConfig ? merge(apiConfig, config) : config;
      apiConfigFn = undefined;
    }
  }

  return apiConfig;
}

// 用于存放合并处理后的配置信息
const API_CONFIGS: Map<string, HttpApi> = new Map();

function getRootApiConfig() {
  const key = 'root';
  let root = API_CONFIGS.get(key);
  if (root) {
    return root;
  }

  const apiConfig = useApiConfig();
  let { server, prefix, suffix } = apiConfig ?? {};
  server = server ?? '';
  prefix = prefix ?? '';
  suffix = suffix ?? '';
  root = { server, prefix, suffix };

  if (apiConfig && Object.keys(apiConfig).length) {
    // 有配置
    API_CONFIGS.set(key, root);
  }

  return root;
}

function getApiConfig(moduleKey?: string) {
  if (moduleKey) {
    const moduleConfig = API_CONFIGS.get(moduleKey);
    if (moduleConfig) {
      // 当前模块有配置且已处理
      return moduleConfig;
    }

    const apiConfig = useApiConfig();
    const modules = apiConfig?.modules;
    if (modules && modules[moduleKey] && Object.keys(modules[moduleKey]).length) {
      // 当前模块有配置
      const moduleConfig = Object.assign({}, getRootApiConfig(), modules[moduleKey]);
      API_CONFIGS.set(moduleKey, moduleConfig);
      return moduleConfig;
    }
  }

  return getRootApiConfig();
}

/**
 * 创建 API 配置信息代理对象
 * @param httpApi Api 信息
 * @param moduleKey 所属模块标识
 * @returns
 * @example
 * ```ts
 * const api = createHttpApiProxy({
 *   test: '/get'
 * }, 'test');
 *
 * export function test(id: string) {
 *   return HttpClient.get(api.test, { params: { id } });
 * }
 * ```
 */
export function createHttpApiProxy<T extends HttpApi>(httpApi: T, moduleKey?: string) {
  return new Proxy(httpApi, {
    get(target, key: string) {
      if (key === '__ob__') {
        return target[key];
      }

      const config = getApiConfig(moduleKey);
      if (config[key] && !config[key].startsWith('/')) {
        return config[key];
      }

      const { server, prefix, suffix } = config;
      return `${server}${prefix}${target[key]}${suffix}`;
    }
  });
}
