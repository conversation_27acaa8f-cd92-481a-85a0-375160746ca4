import { BaseModel } from '@hetu/http';

/**
 * 发布信息
 * <AUTHOR>
 */
export class ReleaseModel extends BaseModel {
  /** 访问链接 */
  releaseUrl = '';

  /** 发布 ID */
  releaseCode = '';

  /**
   * 访问限制
   * - `0`: 停用
   * - `1`: 启用
   */
  accessRestriction: 0 | 1 = 0;

  /**
   * 登录限制
   * - `0`: 停用
   * - `1`: 启用
   */
  loginRestriction: 0 | 1 = 0;

  /** 成员 ID */
  memberIds?: string[];

  /** 用户组 ID */
  userGroupIds?: string[];

  /** 访问密码 */
  accessPassword = '';

  /** Token 验证 */
  accessToken = '';

  /**
   * 参数验证
   * - `0`: 停用
   * - `1`: 启用
   */
  paramCheck: 0 | 1 = 0;

  /** 验证时效: 最小 0, 单位天 */
  accessTerm?: number;

  /** 备注 */
  remark = '';
}

/**
 * 发布历史
 */
export interface ReleaseHistory {
  id: string;

  /** 版本 */
  versionNumber: number;

  /** 发布人 */
  fullName: string;

  /** 是否为当前发布版本 */
  currentRelease: 0 | 1;

  /** 发布时间 */
  createdTime: string;

  /** 备注 */
  remark: string;
}
