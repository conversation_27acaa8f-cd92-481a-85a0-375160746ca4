<template>
  <q-btn flat dense @click="showPopup">
    <q-icon name="settings" class="vis-icon" size="12px" />
    <vis-popup title="字体设置" ref="popupRef" :target="false" @hide="popupShow = false">
      <div class="vis-form-inline">
        <div class="vis-form-field">
          <div class="vis-form-field__label">颜色</div>
          <div class="vis-form-field__content">
            <vis-fill v-model="computedOptions.font.color" :showEyes="false" :minusWidth="0" />
          </div>
        </div>
        <!-- 字体 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">字体</div>
          <div class="vis-form-field__content">
            <vis-select
              v-model="computedOptions.font.fontFamily"
              class="w-full"
              :options="fontFamilyOptions"
              :popHeight="240"
            >
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: computedOptions.font.fontWeight, fontFamily: opt }">{{ opt }}</span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>

        <!-- 粗细 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">粗细</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedOptions.font.fontWeight" :options="fontWeightOptions" class="w-full">
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: opt.value, fontFamily: computedOptions.font.fontFamily }">
                      {{ opt.label }}
                    </span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>

        <!-- 字号 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">字号</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedOptions.font.fontSize" :options="fontSizeOptions" editable />
          </div>
        </div>
      </div>
    </vis-popup>
  </q-btn>
  <div class="vis-form-field">
    <div class="vis-fill__content flex" :class="{ disabled: !computedOptions.show }">
      <q-btn flat dense>
        <ht-icon :name="isIcon ? 'hticon-vis-navigation-pic' : 'hticon-vis-textbox'" class="vis-icon" />
        <q-menu v-model="showMenuType" style="width: 120px" class="vis-menu" dense>
          <q-list dense>
            <q-item :active="isIcon" @click="handleType('icon')" clickable v-close-popup>
              <q-item-section avatar>
                <ht-icon class="vis-icon" name="hticon-vis-navigation-pic" />
              </q-item-section>
              <q-item-section>图标</q-item-section>
            </q-item>
            <q-item :active="!isIcon" @click="handleType('text')" clickable v-close-popup>
              <q-item-section avatar>
                <ht-icon class="vis-icon" name="hticon-vis-textbox" />
              </q-item-section>
              <q-item-section>文本</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
      <q-separator vertical class="!m-0" />
      <div v-if="isIcon" class="flex-1">
        <ht-icon-picker v-model="computedOptions.icon" class="h-full flex items-center px-2">
          <q-icon v-if="computedOptions.icon" :name="computedOptions.icon" size="xs" class="cursor-pointer" />
          <span class="cursor-pointer text-font-regular text-3" v-else>选择图标</span>
        </ht-icon-picker>
      </div>
      <q-input
        v-else
        v-model="computedOptions.text"
        borderless
        class="rounded-borders flex-1 px-2"
        placeholder="请输入"
      />
    </div>
  </div>
  <q-btn @click="handleVisible">
    <ht-icon class="vis-icon" :name="computedOptions.show ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
  </q-btn>
</template>
<script lang="ts" src="./fix.ts"></script>
