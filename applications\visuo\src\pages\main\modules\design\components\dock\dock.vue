<template>
  <div class="vis-design-dock">
    <div role="group" v-for="(group, index) in toolbars" :key="index">
      <template v-for="(action, index) in group" :key="index">
        <div
          v-if="typeof action === 'string'"
          role="toolbar"
          :class="{ active: typeof action === 'string' && actions[action].active }"
          @click="onHandler(action)"
        >
          <q-icon v-if="!actions[action].icon.startsWith('hticon')" :name="actions[action].icon" />
          <ht-icon class="vis-icon" v-else :name="actions[action].icon" />
        </div>
        <div role="toolbar-group" v-else>
          <div
            role="toolbar"
            :class="{ active: groupActive(action).active }"
            @click="onHandler(groupActive(action).name)"
          >
            <q-icon :name="groupActive(action).icon"> </q-icon>
          </div>
          <q-icon role="more" name="expand_more">
            <q-menu class="vis-design-dock-menu vis-menu" :offset="[42, 8]">
              <q-list dense>
                <q-item
                  v-for="(key, index) in action"
                  :key="index"
                  :active="actions[key].active"
                  @click="onHandler(key)"
                  clickable
                  v-close-popup
                  dense
                >
                  <q-item-section avatar>
                    <q-icon :name="actions[key].icon" />
                  </q-item-section>
                  <q-item-section class="!max-w-12">{{ actions[key].title }}</q-item-section>
                  <q-item-section side> V </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-icon>
        </div>
      </template>
    </div>
  </div>
</template>
<script lang="ts" src="./dock.ts"></script>
<style lang="scss" src="./dock.scss"></style>
