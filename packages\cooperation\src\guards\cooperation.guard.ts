import { CooperationACLService, CooperationService } from '../services';
import { AppService, EXCEPTION_ROUTES } from '@hetu/platform-shared';
import { useRouterWithout } from '@hetu/core';
import type { RouteLocationNormalized } from 'vue-router';

let initialized = false;

/**
 * 应用全局解析守卫: 用于获取协作权限组配置信息
 * <AUTHOR>
 */
export async function createCoBeforeResolveGuard() {
  const router = useRouterWithout();
  router.beforeResolve(async (to: RouteLocationNormalized) => {
    if (!initialized && !EXCEPTION_ROUTES.find((r) => r.path === to.path)) {
      const permissions = await CooperationService.getConfig(AppService.appKey);
      initialized = true;
      CooperationACLService.setPermissions(permissions);
    }
  });
}
