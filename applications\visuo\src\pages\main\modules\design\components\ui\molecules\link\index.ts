import {
  Block,
  DialogPosition,
  Frame,
  GraphType,
  Link,
  LinkTarget,
  LinkType,
  Page,
  TextBox,
  useDocumentStore
} from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import VisLinkParam from './param/index.vue';
/**
 * 链接配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-link',
  props: {
    modelValue: {
      type: Object as PropType<Link>,
      required: true
    }
  },
  components: { VisLinkParam },
  setup(props, { emit }) {
    const { document } = useDocumentStore();
    const computedModel = computed({
      get() {
        return props.modelValue;
      },

      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    // 链接类型
    const typeOptions = [
      { label: '外链', value: LinkType.Url },
      { label: '容器', value: LinkType.Frame }
    ];

    const urlValue = ref('');
    const frameValue = ref('');

    const changeType = (val: LinkType) => {
      if (val === LinkType.Url) {
        frameValue.value = computedModel.value.link;
      } else {
        urlValue.value = computedModel.value.link;
      }

      computedModel.value.type = val;
      computedModel.value.link = val === LinkType.Url ? urlValue.value : frameValue.value;
    };

    const isUrl = computed(() => {
      return computedModel.value.type === LinkType.Url;
    });

    // 所有页面内的一级容器
    const frames = computed(() => {
      const temp: Frame[] = [];
      const pages: Page[] = document.value.children.filter((item) => item.type === GraphType.Page);
      pages.forEach((page) => {
        temp.push(
          ...(page.children.filter((item: Frame | Block | TextBox) => item.type === GraphType.Frame) as Frame[])
        );
      });
      return temp;
    });

    // 容器列表
    const linkFrames = computed(() => {
      return frames.value.map((frame) => {
        return {
          label: frame.name,
          value: frame.id
        };
      });
    });

    const selectedFrame = ref<Frame>();
    // 选择容器
    const changeFrame = (val: string) => {
      const frame = frames.value.filter((f) => f.id === val)[0];
      selectedFrame.value = frame;
    };

    // 打开方式
    const targetOptions = [
      { label: '新窗口', value: LinkTarget.Blank },
      { label: '当前窗口', value: LinkTarget.Self },
      { label: '弹窗', value: LinkTarget.Dialog }
    ];

    const isDialog = computed(() => {
      return computedModel.value.target === LinkTarget.Dialog;
    });

    // 弹窗
    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    const hidePopup = () => {
      popupShow.value = false;
      computedModel.value.params = computedModel.value.params.filter((p) => p.name || p.value);
    };

    // 弹窗设置
    const positionOptions = [
      { label: '顶部', value: DialogPosition.Top },
      { label: '底部', value: DialogPosition.Bottom },
      { label: '左边', value: DialogPosition.Left },
      { label: '右边', value: DialogPosition.Right },
      { label: '居中', value: DialogPosition.Center }
    ];

    /**
     * 根据选择的容器（固定尺寸或自适应布局）自动判定为自适应或自定义
     */
    // const sizeOptions = computed(() => {
    //   const options = [
    //     { label: '全屏', value: 'full' },
    //     { label: '自定义', value: 'custom' },
    //     { label: '自适应', value: 'auto' }
    //   ];
    //   // 外链或自适应的容器
    //   if (isUrl.value) {
    //     options.push({ label: '自定义', value: 'custom' });
    //   } else {
    //     const isFlex = selectedFrame.value?.autoLayout.type === 'responsive';
    //     const option = isFlex ? { label: '自定义', value: 'custom' } : { label: '自适应', value: 'auto' };
    //     options.push(option);
    //   }
    //   return options;
    // });

    // watch(
    //   () => sizeOptions.value,
    //   (newVal, oldVal) => {
    //     if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return;
    //     size.value = size.value === 'full' ? 'full' : newVal[1].value;
    //   }
    // );

    const sizeOptions = ref([
      { label: '全屏', value: 'full' },
      { label: '自定义', value: 'custom' },
      { label: '自适应', value: 'auto' }
    ]);

    const size = ref(
      Array.isArray(computedModel.value.targetOption.size) ? 'custom' : computedModel.value.targetOption.size
    );
    const sizeWidth = ref(
      Array.isArray(computedModel.value.targetOption.size) ? computedModel.value.targetOption.size[0] : 800
    );
    const sizeHeight = ref(
      Array.isArray(computedModel.value.targetOption.size) ? computedModel.value.targetOption.size[1] : 600
    );

    const handleSize = () => {
      if (size.value === 'full') {
        computedModel.value.targetOption.size = 'full';
      } else if (size.value === 'custom') {
        computedModel.value.targetOption.size = [sizeWidth.value || 800, sizeHeight.value || 600];
      }
    };

    const showMenuLink = ref(false);

    return {
      computedModel,
      typeOptions,
      LinkType,
      changeType,
      isUrl,
      linkFrames,
      changeFrame,

      targetOptions,
      isDialog,

      popupRef,
      popupShow,
      showPopup,
      hidePopup,

      positionOptions,
      sizeOptions,
      size,
      sizeWidth,
      sizeHeight,
      handleSize,

      showMenuLink
    };
  }
});
