import { computed, ref, defineComponent, type PropType, watch } from 'vue';
import { useQuasar, useDialogPluginComponent } from 'quasar';
import type { FileInfo } from '../../models';
import { FileTree, type FileTreeNode } from '../../utils';
import { CatalogFileService, CatalogService, SpaceMoveService } from '../../services';
import { AttachmentService, SpaceService, Team, TeamService, type Teams } from '@hetu/platform-shared';
import { SettingsService } from '@hetu/theme';

export default defineComponent({
  name: 'ht-app-move-space',
  emits: [...useDialogPluginComponent.emits],
  props: {
    title: String,
    // 当前上级
    parentId: {
      type: String,
      default: ''
    },
    // 类别
    category: {
      type: String,
      required: true
    },
    // 待移动
    sources: {
      type: Array<FileInfo>,
      required: true
    },
    // 可选范围
    targets: {
      type: Array<FileInfo>,
      default: []
    },
    /** 是否跨空间移动 */
    isCrossSpace: {
      type: Boolean,
      default: false
    },
    /** 是否显示其他团队 */
    isOtherSpace: {
      type: Boolean,
      default: false
    },
    /** 当前空间下是否可以移动 */
    move: {
      type: Boolean,
      default: false
    },
    // 过滤移动目录规则
    filterMethod: {
      type: Function as PropType<(node: FileTreeNode<FileInfo>, filter: string) => boolean>
    }
  },
  setup(props) {
    const $q = useQuasar();
    const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

    const loading = ref(false);

    const targetId = ref(props.parentId);
    const isChange = computed(() => targetId.value !== props.parentId || !targetId.value);

    const group = computed(() => {
      const folders: string[] = [];
      const files: string[] = [];
      props.sources?.forEach((item) => {
        (item.dataType ? folders : files).push(item.id);
      });

      return { folders, files };
    });

    /** 获取标题名称 */
    const titleName = computed(() => {
      return '移动' + (props.title ? ` - ${props.title}` : `(${props.sources.length})`);
    });

    //#region 团队空间
    // 当前空间的域名标识
    const domain = computed(() => SpaceService.getDomainCode() || 'me');
    // 用户信息
    const user = SettingsService.user;
    // 当前团队信息
    const activeTeam = computed(() => (domain.value ? TeamService.current : undefined));
    // 头像地址
    const avatar = (id: string) => AttachmentService.downloadFileUrl(id);

    // 跨空间移动搜索字段
    const search = ref({
      team: '',
      folder: ''
    });
    // 跨空间移动数据结构
    const moveModel = ref({
      /** 团队空间 */
      targetDomain: '',
      /** 目标文件夹id */
      targetCatalogId: '',
      /** 是否创建副本并移动 */
      createCopy: 0
    });

    // 团队列表
    const teamList = ref<Teams>([]);
    const teams = computed(() => {
      const tempTeamList: Team[] = [];
      const meTeam = new Team();
      meTeam.name = '个人空间';
      meTeam.domain = 'me';
      meTeam.avatar = user.avatar || '';
      // 添加非当前空间下的团队列表
      if (props.isOtherSpace) {
        const teams = teamList.value?.filter((team) => !(team.owner && !team.teamType)) || [];
        tempTeamList.push(...teams.filter((team) => team.domain !== domain.value));
        'me' !== domain.value && tempTeamList.unshift(meTeam);
      }
      if (props.move) {
        // 添加当前所处团队-->置顶
        tempTeamList.unshift(activeTeam.value && 'me' !== domain.value ? activeTeam.value : meTeam);
      }
      // 过滤搜索条件后的团队数据并返回
      return tempTeamList.filter((team) => team.name.indexOf(search.value.team) !== -1);
    });
    /** 加载团队列表 */
    const loadTeams = async () => {
      try {
        const data = await TeamService.getTeams();
        teamList.value = data || [];
        // eslint-disable-next-line no-empty
      } catch (error) {}
    };

    const folders: any = ref([]);
    /** 加载指定团队下的文件夹目录结构 */
    const loadFolders = () => {
      const domain = moveModel.value.targetDomain === 'me' ? '' : moveModel.value.targetDomain;
      CatalogService.moveList(domain, props.category).then((data) => {
        // const ids = data.filter((item) => item.parentId).map((item) => item.parentId);
        const tree = new FileTree<FileInfo>({ directorys: data, files: [] });
        folders.value = tree?.getNode()?.directorys || [];
      });
    };

    const getTargets = computed(() => {
      return folders.value;
    });

    /** 选择要移动到的团队 */
    const selectTeam = (domain: string) => {
      moveModel.value.targetDomain = domain;
      loadFolders();
    };

    /** 监听是否加载团队列表数据 */
    watch(
      () => props.isCrossSpace,
      (val) => {
        if (val) {
          props.isOtherSpace && !teamList.value.length && loadTeams();
          moveModel.value.targetDomain = domain.value;
        }
        folders.value = props.targets;
      },
      { immediate: true }
    );

    /** 跨空间移动 */
    const moveSpace = async () => {
      // 未改变选中的文件夹结构并且，域也没做切换，认为页面没做任何操作，确定即直接关闭
      if (!isChange.value && moveModel.value.targetDomain === domain.value) {
        return onDialogCancel();
      }
      const type = props.category.includes('_') ? props.category.split('_')[1] : props.category;
      const { files } = group.value;
      const id = files && files.length ? files[0] : '';
      const { targetCatalogId, createCopy } = moveModel.value;
      const targetDomain = moveModel.value.targetDomain === 'me' ? '' : moveModel.value.targetDomain;
      const spaceMove = await SpaceMoveService.spaceMove(id, targetDomain, targetCatalogId, createCopy, type);
      if (spaceMove && spaceMove.status === 'success') {
        $q.notify({ position: 'top', type: 'positive', message: '移动成功' });

        onDialogOK({
          successIds: group.value.folders.concat(group.value.files),
          createCopy: createCopy
        });
      }
    };
    //#endregion 团队空间

    /** 当前空间下移动 */
    const move = async () => {
      if (!isChange.value) {
        return onDialogCancel();
      }

      loading.value = true;

      const { folders, files } = group.value;
      const folderLen = folders.length;
      const fileLen = files.length;

      // 发送请求
      const all = [];
      folderLen && all.push(CatalogService.move(props.category as string, folders, targetId.value));
      fileLen && all.push(CatalogFileService.move(props.category as string, files, targetId.value));
      const result = await Promise.all(all);

      const isFolderSuccess = folderLen ? result[0]?.status === 'success' : true;
      const isFileSuccess = fileLen ? result[folderLen ? 1 : 0]?.status === 'success' : true;

      let successIds;
      if (isFolderSuccess && isFileSuccess) {
        successIds = group.value.folders.concat(group.value.files);
        $q.notify({ position: 'top', type: 'positive', message: '移动成功' });
      } else if (isFolderSuccess && folderLen) {
        successIds = group.value.folders;
        $q.notify({ position: 'top', type: 'warning', message: '文件夹移动成功, 文件移动失败!!!' });
      } else if (isFileSuccess && fileLen) {
        successIds = group.value.files;
        $q.notify({ position: 'top', type: 'warning', message: '文件移动成功, 文件夹移动失败!!!' });
      }

      loading.value = false;
      onDialogOK({
        targetId: targetId.value,
        successIds
      });
    };

    /** 确定 */
    const save = () => {
      if (props.isCrossSpace) {
        //开启跨空间移动
        if (domain.value === moveModel.value.targetDomain) {
          // 当前选择移动到的域与当前域相同，此时应该认为是非跨空间移动
          move();
        } else {
          moveSpace();
        }
      } else {
        move();
      }
    };

    /**过滤后可点击的节点 */
    const selected = (node: FileTreeNode<FileInfo>) => {
      targetId.value = targetId.value === node.id ? '' : node.id;
      moveModel.value.targetCatalogId = moveModel.value.targetCatalogId === node.id ? '' : node.id;
    };
    /**根据文件夹节点，判断当前节点文件夹是否有创建的权限， filterMethod返回false，则无创建能力*/
    const checkDisable = (node: FileTreeNode<FileInfo>) => {
      return props.filterMethod ? !props.filterMethod(node, '') : false;
    };

    return {
      titleName,
      search,
      teams,
      domain,
      moveModel,
      getTargets,
      avatar,
      selectTeam,

      dialogRef,
      loading,
      targetId,
      isChange,
      onDialogCancel,
      onDialogHide,

      save,
      selected,
      checkDisable
    };
  }
});
