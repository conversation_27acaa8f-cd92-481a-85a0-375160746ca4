import type { NormalFilter, QueryFilter, RangeFilter, ScopeFilter } from './expression';

/**
 * 数据资源字段
 * <AUTHOR>
 */
export class AxisField {
  id = '';
  /** 字段名 */
  fieldName = '';

  /** 字段别名 */
  fieldAlias = '';

  /** 字段类型 */
  fieldType: FieldType = FieldType.Dim;

  /** 字段的实际类型 */
  dataType = '';

  sourceFieldName = '';

  dataTransformation?: {
    aggregator: string;
    transformationConfig?: TransformationConfig;
  };

  filterRule?: 'and' | 'or';

  // 是否包含null值 0 - 否, 1 - 是
  includeNull?: number;

  /** 筛选器参数 */
  filterExpression?: Array<NormalFilter | QueryFilter | RangeFilter | ScopeFilter>;

  constructor(
    id?: string,
    fieldName?: string,
    fieldAlias?: string,
    fieldType?: FieldType,
    dataType?: string,
    sourceFieldName?: string
  ) {
    id && (this.id = id);
    fieldName && (this.fieldName = fieldName);
    fieldAlias && (this.fieldAlias = fieldAlias);
    fieldType && (this.fieldType = fieldType);
    dataType && (this.dataType = dataType);
    sourceFieldName && (this.sourceFieldName = sourceFieldName);
  }
}

export enum FieldType {
  Dim = 'dim',
  Measure = 'measure'
}

/**
 * 日期格式配置
 */
export type TransformationConfig = {
  type: string; //格式转换类型 num-数字 percent-百分比 date-日期
  dateType?: string; // 日期类型 yearQuarter-年季度 yearMonth-年月 yearWeek-年周 yearMonthDay-年月日 yearMonthDayHourMinute-年月日时分 monthDay-月日 hourMinute-时分 year-年 quarter-季度 month-月 week-周 day-日 hour-时 minute-分
  dateSeparator: number; // 日期分隔符 1-默认(-) 2-中文  3-斜杠(/)
  timeSeparator: number; // 时间分隔符 1-默认(:) 2-中文
  timeType: number; // 小时格式 24-24小时制 12-12小时制
  paddedByZero: number; // 是否自动补0 0-否，1-是
  separator: number; // 禁用分隔符 0-否，1-是
  expression?: string; // 格式化表达式
  unit?: string; // 单位值
};
