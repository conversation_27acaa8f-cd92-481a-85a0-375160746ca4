import { ACLResource } from '@hetu/acl';
import { BaseModel } from '@hetu/http';
import type { HttpApi } from '@hetu/http';
import type { FileInfo } from '@hetu/platform-shared';

/**
 * 数据源对象
 * <AUTHOR>
 */
export class Datasource extends BaseModel {
  /** 数据源名称 */
  datasourceName = '';
  /** 数据源标识 */
  identifier = '';
}

/**
 * 数据源文件
 */
export interface DatasourceFile extends FileInfo {
  /** 数据源类型 */
  datasourceType: string;
  /** 数据库类型 */
  databaseType: string;
  /** 数据库分组类型 */
  databaseGroupType: string;
}

/**
 * 数据源类型
 */
export enum DatasourceType {
  /** 数据库数据源 */
  Jdbc = 'jdbc',
  /** 文件数据源 */
  File = 'file',
  /** api接口数据源 */
  Api = 'api'
}

/**
 * 数据源类型名称
 */
export enum DatasourceTypeName {
  jdbc = '数据库',
  file = '文件数据源',
  api = 'API数据源'
}

export enum DatasourceACLResourceCode {
  /** 新建 */
  Add = 'create',
  /** 删除 */
  Recycle = 'recycle',
  /** 修改 */
  Edit = 'edit',
  /** 移动到空间 */
  SpaceMove = 'spaceMove'
}

export class DatasourceACLResource extends ACLResource<typeof DatasourceACLResourceCode> {
  readonly group: string = 'datasource';

  get more() {
    const code = this.resourceCode as typeof DatasourceACLResourceCode;
    return {
      resource: {
        [this.group]: [code.Recycle, code.SpaceMove]
      }
    };
  }

  constructor() {
    super();
    this.resourceCode = DatasourceACLResourceCode;
  }
}

export const DATASOURCE_API_PREFIX = '/metadata';

/**
 * 数据源API
 */
export class DatasourceApi implements HttpApi {
  list: string = DATASOURCE_API_PREFIX + '/datasource/list';
  selectionList: string = DATASOURCE_API_PREFIX + '/datasource/selection/list';

  [key: string]: any;
}
