/**
 * 系统服务信息
 * <AUTHOR>
 */
export interface System {
  /**
   * 产品模式
   */
  product: ProductMode;

  /**
   * 状态
   */
  state: SystemState;

  /**
   * License申请码
   * @see state为1和3时有值
   */
  serverId: string;

  // 以下为初始化完成后需提供参数

  /**
   * 到期日期
   * @see 状态为2和3时有值
   */
  licenseExpiryDate: string;
}

/**
 * 产品模式
 */
export enum ProductMode {
  /** 可视化平台/智能分析与应用平台 */
  Hetu = 'HETU',
  /** 数据开发平台 */
  Trusted = 'TRUSTED'
}

/**
 * 系统状态
 */
export enum SystemState {
  /** 未初始化数据库 */
  Uninitialized,
  /** 未上传授权码 */
  Unauthorized,
  /** 正常 */
  Normalized,
  /** 授权超期 */
  Overdue,
  /** 未创建默认用户 */
  NoUser
}

/**
 * 系统数据库对象
 * <AUTHOR>
 */
export class SystemDatabase {
  /**
   * 版本
   */
  level: 'trial' | 'server' | '' = '';

  /**
   * 数据库类型
   */
  databaseType?: string;

  /**
   * 主机IP
   */
  ip?: string;

  /**
   * 端口号
   */
  port?: string;

  /**
   * 数据库名称
   */
  databaseName?: string;

  /**
   * 数据库用户名
   */
  userName?: string;

  /**
   * 数据库用户密码
   */
  password?: string;
}
