import type { HttpApi } from '@hetu/http';
import { SYSTEM_API_PREFIX } from '.';

export class PassportApi implements HttpApi {
  /** 查询账号配置 */
  accountConfig = `${SYSTEM_API_PREFIX}/login/config`;
  /** 退出登录 */
  logout = `${SYSTEM_API_PREFIX}/logout`;
  /** 获取验证码图片 */
  validatorCode = `${SYSTEM_API_PREFIX}/login/verify/code`;
  /** 验证图片验证码 */
  loginCodeCheck = `${SYSTEM_API_PREFIX}/login/code/check`;
}

export interface LoginResult {
  token: string;

  [key: string]: any;
}

/**
 * 验证码
 */
export interface ValidatorCode {
  code?: string;
  codeId?: string;
}
