import { UUID } from '@hetu/platform-shared';
import type { Effects, FillPaints, Stroke, Text } from './ui';
import type { Constraints, DirectionType, GridItem } from './frame';

/**
 * 图形基础属性
 * <AUTHOR>
 */
export class GraphBasic {
  /** 唯一标识 */
  id = UUID();

  /** 名称 */
  name = '';

  /** 类型 */
  type = GraphType.Block;

  /** 父节点id */
  parent = '';

  /** 顺序号 */
  order = 0;

  /** 文档状态 */
  phase = '';

  /** 编辑信息 */
  editInfo = {};
}

export enum GraphType {
  /** 文档 */
  Document = 'DOCUMENT',

  /** 页面 */
  Page = 'PAGE',

  /** 容器 */
  Frame = 'FRAME',

  /** 遍组 */
  Group = 'GROUP',

  /** 公共组件 */
  Symbol = 'SYMBOL',

  /** 公共组件实例 */
  Instance = 'INSTANCE',

  /** 块，承载元素最小单元 */
  Block = 'BLOCK',

  /** 文本 */
  TextBox = 'TEXTBOX'
}

export enum ResizeType {
  /** 固定尺寸 */
  Fixed = 'fixed',

  /** 充满容器 */
  Fill = 'fill',

  /** 适应内容 */
  Adapt = 'adapt'
}

export class Graph extends GraphBasic {
  /** 锁定 */
  locked = false;

  /** 显示 */
  visible = true;

  /** 隐藏时销毁 */
  destroy = false;

  /** 宽度 */
  width = 0;

  /** 高度 */
  height = 0;

  /** 纵横比 */
  aspectRatio = false;

  /** 透明度 */
  opacity = 100;

  /** 圆角 */
  radius = [0, 0, 0, 0];

  /** 变换 */
  transform = new Transform();

  /** 文本样式 */
  text?: Text;

  /** 填充 */
  fillPaints?: FillPaints[];

  /** 描边 */
  stroke?: Stroke;

  /** 效果 */
  effects?: Effects;

  /** 约束 */
  constraints?: Constraints;

  children?: Array<Graph>;

  /** 忽略自动布局 */
  ignoreAutoLayout = false;

  /** 尺寸限制 */
  limitSize: {
    width: {
      min: '' | number;
      max: '' | number;
      resize: ResizeType;
    };
    height: {
      min: '' | number;
      max: '' | number;
      resize: ResizeType;
    };
  } = {
    width: {
      min: '',
      max: '',
      resize: ResizeType.Fixed
    },
    height: {
      min: '',
      max: '',
      resize: ResizeType.Fixed
    }
  };

  /** 图形在grid中的位置 */
  gridItem: GridItem | undefined = undefined;
}

export class Transform {
  /** 平移 */
  translate: number[] = [0, 0];

  /** 3d缩放 */
  matrix3d?: number[];

  /** 旋转 */
  rotate: number = 0;

  /** 旋转原点 */
  origin: string = 'center center';
}

/** 图形尺寸最小值 */
export const GRAPH_SIZE_MIN = 20;

export class GraphConfig {
  type!: GraphType | DirectionType;
  name!: string;
  icon!: string;

  get record(): Record<string, GraphConfig> {
    const record: Record<string, GraphConfig> = {};
    record[this.type] = this;
    return record;
  }

  constructor(type: GraphType | DirectionType, name: string, icon: string) {
    this.type = type;
    this.name = name;
    this.icon = icon;
  }
}
