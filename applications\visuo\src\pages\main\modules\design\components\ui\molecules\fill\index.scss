@import '../../../index.scss';

// Fill组件样式
.#{$vis-prefix}-fill {
  width: 100%;

  &__content {
    width: 100%;
    background: $input-bg;
    border-radius: $border-radius;
    @include form-field-outline;
  }

  &__types {
    background: $input-bg;

    .grayscale {
      filter: grayscale(100%);
    }
  }

  .#{$vis-prefix}-invisible .q-input {
    opacity: 0.6;
  }

  &__icon {
    font-size: 10px !important;
    border: 1px solid rgba(0, 0, 0, 0.21);
    border-radius: 2px;
  }

  &-color-input {
    display: flex;

    .#{$vis-prefix}-form-inline {
      gap: 1px !important;
    }

    .#{$vis-prefix}-fill__content {
      gap: 1px;
      background: #ffffff;
      overflow: hidden;
    }

    .#{$vis-prefix}-number {
      outline: none !important;
      border-radius: 0;
      padding: 0 6px;
    }
  }
}