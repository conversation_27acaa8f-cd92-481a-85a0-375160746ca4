import { createSingleClass } from '@hetu/core';
import {
  HttpApiService,
  responseData,
  responseReject,
  responseResult,
  type HttpRequestConfig,
  type HttpApi
} from '@hetu/http';
import { SYSTEM_API_PREFIX } from '../models';

const API_HOST = `${SYSTEM_API_PREFIX}/attachment`;

class AttachmentApi implements HttpApi {
  uploadImage = `${API_HOST}/upload/image`;
  uploadVideo = `${API_HOST}/upload/video`;
  uploadFile = `${API_HOST}/upload/file`;
  uploadFiles = `${API_HOST}/upload/files`;
  uploadBase64 = `${API_HOST}/upload/image/base64`;
  uploadString = `${API_HOST}/upload/string`;
  downloadString = `${API_HOST}/download/string`;
  // 下载文件
  downloadFile = `${API_HOST}/download/file`;
  // 下载图片
  downloadImage = `${API_HOST}/download/image`;
  // 下载缩略图
  downloadScale = `${API_HOST}/download/image/scale`;
}

/**
 * 文件服务类
 * <AUTHOR>
 */
class AttachmentServiceCtor extends HttpApiService<AttachmentApi> {
  httpApi = new AttachmentApi();

  httpModuleKey = 'attachment';

  /** 文件下载接口地址 */
  get downloadFile(): string {
    const url = this.api.downloadFile;
    return url.startsWith('/') ? `.${url}` : url;
  }

  /**
   * 文件下载全路径
   * @param id 文件 id
   */
  downloadFileUrl(id: string, type?: string) {
    let downloadUrl = this.api.downloadImage;
    switch (type) {
      case 'file':
        downloadUrl = this.api.downloadFile;
        break;
      case 'scale':
        downloadUrl = this.api.downloadScale;
        break;
    }
    downloadUrl = downloadUrl.startsWith('/') ? `.${downloadUrl}` : downloadUrl;
    return id && id.startsWith('TJ') && id.length === 32 ? downloadUrl + '?id=' + id : id;
  }

  /**
   * 通过文件流上传文件
   * @param file FormData
   *    - `files`: 文件流 [必填]
   *    - `moduleCode`: 文件所属业务模块编码 [必填]
   *    - `fileName`: 原始文件名称
   *    - `fileType`: 文件类型 [非必填]，若上传的文件实际类型不符合，会上传失败（可选类型： `image`，`video`）
   *    - `id`: 该文件的id,用于保存文件版本记录
   *    - `isScale`: 是否对图片进行压缩存储 0 否 1 是。仅支持 jpg, bmp, gif, png, wbmp, jpeg格式。
   *        当对图片进行压缩存储时，会同时保存原始图片及压缩后的图片
   *    - `widget`: 文件宽度
   *    - `height`: 文件高度
   *    - `keepAspectRatio`: 是否按照比例缩放 (1 按照比例缩放; 0 不按照比例缩放,将导致图片变形)
   * @param config HttpRequestConfig
   */
  uploadImage(file: FormData, config?: HttpRequestConfig) {
    config = Object.assign(config || {}, { timeout: 300000 });
    return this.http.post(this.api.uploadImage, file, config).then(responseResult, responseReject);
  }

  /**
   * 通过文件流上传文件
   * @param file FormData
   *    - `file`: 文件流 [必填]
   *    - `moduleCode`: 文件所属业务模块编码 [必填]
   *    - `fileName`: 原始文件名称
   *    - `fileType`: 文件类型 [非必填]，若上传的文件实际类型不符合，会上传失败（可选类型： `image`，`video`）
   *    - `id`: 该文件的id,用于保存文件版本记录
   *    - `isScale`: 是否对图片进行压缩存储 0 否 1 是。仅支持 jpg, bmp, gif, png, wbmp, jpeg格式。
   *        当对图片进行压缩存储时，会同时保存原始图片及压缩后的图片
   *    - `widget`: 文件宽度
   *    - `height`: 文件高度
   *    - `keepAspectRatio`: 是否按照比例缩放 (1 按照比例缩放; 0 不按照比例缩放,将导致图片变形)
   * @param config HttpRequestConfig
   */
  uploadFile(file: FormData, config?: HttpRequestConfig) {
    config = Object.assign(config || {}, { timeout: 300000 });
    return this.http.post(this.api.uploadFile, file, config).then(responseResult, responseReject);
  }

  /**
   * 通过文件流上传文件
   * @param file FormData
   *    - `files`: 文件流集合list<file> [必填]
   *    - `moduleCode`: 文件所属业务模块编码 [必填]
   *    - `id`: 该文件的id,用于保存文件版本记录
   *    - `isScale`: 是否对图片进行压缩存储 0 否 1 是。仅支持 jpg, bmp, gif, png, wbmp, jpeg格式。
   *        当对图片进行压缩存储时，会同时保存原始图片及压缩后的图片
   *    - `widget`: 文件宽度
   *    - `height`: 文件高度
   *    - `keepAspectRatio`: 是否按照比例缩放(1 按照 比例缩放 0不按照比例缩 放,将导致图片变形)
   * @param config HttpRequestConfig
   */
  uploadFiles(file: FormData, config?: HttpRequestConfig) {
    config = Object.assign(config || {}, { timeout: 300000 });
    return this.http.post(this.api.uploadFiles, file, config).then(responseResult, responseReject);
  }

  /**
   * base64 图片文件上传
   * @param base64 base64 图片
   * @param moduleCode 文件所属业务模块编码
   * @param id  该文件的id,用于保存文件版本记录
   * @param fileName 原始文件名称
   */
  uploadBase64(base64: string, moduleCode: string, id?: string, fileName?: string) {
    return this.http
      .post(this.api.uploadBase64, {
        base64,
        moduleCode,
        id,
        fileName
      })
      .then(responseResult, responseReject);
  }

  /**
   * 字符串上传为文件
   * @param text
   * @param fileName
   * @param id
   */
  uploadString(text: string, fileName?: string, id?: string) {
    return this.http
      .post(this.api.uploadString, {
        text,
        fileName,
        id
      })
      .then(responseResult, responseReject);
  }

  /**
   * 文本文件下载
   * @param id 文件id
   */
  downloadString(id: string) {
    return this.http
      .get(this.api.downloadString, {
        params: {
          id
        }
      })
      .then(responseData, responseReject);
  }

  /**
   * 上传视频
   * @param file
   * @param config
   * @returns
   */
  uploadVideo(file: FormData, config?: HttpRequestConfig) {
    config = Object.assign(config || {}, { timeout: 300000 });
    return this.http.post(this.api.uploadVideo, file, config).then(responseResult, responseReject);
  }
}

export const AttachmentService = createSingleClass(AttachmentServiceCtor);
