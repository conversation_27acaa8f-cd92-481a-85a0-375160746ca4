import { computed, defineComponent, ref, type PropType, watch, nextTick } from 'vue';
import { NormalFilter, Limit } from '../../../models';
import { useQuasar } from 'quasar';

/**
 * 筛选器-选定值
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-filter-normal',
  props: {
    filter: {
      type: Object as PropType<NormalFilter>,
      required: true
    },
    fieldValues: {
      type: Object as PropType<Promise<{ [key: string]: any }[]>>,
      required: true
    },
    fieldName: {
      type: String,
      required: true
    }
  },
  emits: ['changeLimit'],
  setup(props, { emit }) {
    const $q = useQuasar();

    const search = ref('');

    const normalfilter = ref(props.filter);

    const fieldList = ref<{ columnValue: any }[]>([]);

    const loading = ref(false);

    const isPromise = <T>(obj: any): obj is Promise<T> => {
      return !!obj && typeof obj.then === 'function';
    };

    /** 分页对象 */
    const limit = ref(new Limit(0, 20));

    /** 是否查询数据，数据大于等于20条时允许继续查询 */
    const isQuery = ref(false);

    /**
     * 获取数据
     */
    const getFieldList = () => {
      loading.value = true;
      props.fieldValues
        .then((res: any) => {
          if (res.code === 1020) {
            $q.notify({ message: res.errorMsg, position: 'top', type: 'negative' });
            loading.value = false;
            return;
          }
          const records = res.records ? res.records : res.data.records;
          records.length >= 20 ? (isQuery.value = true) : (isQuery.value = false);

          records.length &&
            (fieldList.value = [
              ...fieldList.value,
              ...records.map((i: any) => {
                return { columnValue: i[props.fieldName] };
              })
            ]);
          loading.value = false;
        })
        .catch(() => {
          loading.value = false;
        });
    };

    if (isPromise(props.fieldValues)) {
      getFieldList();
    }

    const options = computed(() => {
      return (isPromise(props.fieldValues) ? fieldList.value : props.fieldValues).filter(
        (opt) =>
          opt.columnValue &&
          opt.columnValue.toString().indexOf(search.value) !== -1 &&
          !normalfilter.value.values.includes(opt.columnValue)
      );
    });

    const onAddValue = () => {
      if (illegalVariable(search.value)) return;
      if (search.value && !normalfilter.value.values.includes(search.value)) {
        normalfilter.value.values.push(search.value);
        search.value = '';
      }
    };

    const onClearAll = () => {
      normalfilter.value.values = [];
    };

    const onCheckALl = () => {
      normalfilter.value.values = (isPromise(props.fieldValues) ? fieldList.value : props.fieldValues).map(
        (o) => o.columnValue
      );
    };

    /** 前台滚动展示分页数据 */
    const handleScroll = (info: any) => {
      if (info) {
        const { verticalContainerSize, verticalPosition, verticalSize } = info;
        if (verticalContainerSize + verticalPosition + 50 > verticalSize) {
          limit.value.startRow += 1;
          if (limit.value.startRow > 1 && isQuery.value) {
            emit('changeLimit', limit.value);
            nextTick(() => {
              getFieldList();
            });
          }
        }
      }
    };

    const illegalVariable = (val: any) => {
      return /#\{.*?\}/.test(val) && !/^#\{[0-9a-zA-Z.]+\}$/.test(val);
    };
    const inputRules = [
      (val: any) => {
        if (illegalVariable(val)) {
          return '变量替换格式错误，只允许数字、字母和.';
        }
        return true;
      }
    ];

    return { loading, normalfilter, search, options, onAddValue, onClearAll, onCheckALl, handleScroll, inputRules };
  }
});
