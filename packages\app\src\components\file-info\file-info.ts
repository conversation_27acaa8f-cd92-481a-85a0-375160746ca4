import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { ACLResource, ACLService } from '@hetu/acl';
import { OrgService } from '@hetu/platform-shared';
import { useQuasar } from 'quasar';
import type { FavoriteChoose, FileInfo } from '../../models';
import { useFilePageStore } from '../../stores';
import type { FileTreeNode } from '../../utils';
import { FavoriteService } from '../../services';
import { useDomain } from '../../hooks';

/**
 * 文件信息组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-app-file-info',
  props: {
    /** 文件 */
    file: Object as PropType<FileInfo>,
    /** 图标 */
    icon: String,
    /** 图标颜色 */
    iconColor: String,
    /** 文件类型名称 */
    typeName: String,
    /** 资源权限配置 */
    acl: Object as PropType<ACLResource<any>>
  },
  setup(props, { emit }) {
    const $q = useQuasar();
    const store = useFilePageStore();
    const domain = useDomain();

    //#region 文件信息
    /** 文件信息 */
    const fileInfo = computed(() => {
      if (props.file) return props.file;

      return store.current.value;
    });

    const fileIcon = computed(() => {
      if (!fileInfo.value) return 'hticon-folder-add-fill';

      if (fileInfo.value.dataType) {
        return fileInfo.value.id === store.open.value?.id ? 'hticon-folder-open-fill' : 'hticon-folder-fill';
      }

      return props.icon;
    });
    //#endregion 文件信息

    //#region 用户名称
    const userNames = ref<Array<string | null>>([null, null]);

    const getUserNames = async () => {
      if (fileInfo.value) {
        userNames.value = [null, null];

        const creatorId = fileInfo.value?.creatorId as string;
        const updateId = fileInfo.value?.updateId as string;
        userNames.value = await OrgService.getName([creatorId, updateId]);
      }
    };

    watch(
      () => fileInfo.value?.id,
      () => getUserNames(),
      { immediate: true }
    );
    //#endregion 用户名称

    //#region 所在位置
    const parent = computed(() => {
      if (fileInfo.value?.id === store.open.value?.id) {
        return undefined;
      }

      const node = fileInfo.value as FileTreeNode<FileInfo>;
      const parentNode = node.getParent && node.getParent(false);
      if (parentNode && parentNode.id === store.open.value?.id) {
        return undefined;
      }

      return parentNode;
    });

    const openParent = () => {
      store.setAction('setNode', [parent.value]);
    };
    //#endregion 所在位置

    //#region 星标文件动态消息

    const favoriteMsg = ref<FavoriteChoose>();

    /** 动态消息是否可设置权限 */
    const isCanSetupMsg = computed(() =>
      ACLService.can(fileInfo.value?.dataType ? props.acl?.setupNotifyMsgDir : props.acl?.setupNotifyMsg)
    );

    const isEmptyMsg = computed(
      () =>
        favoriteMsg.value && !favoriteMsg.value.sendSms && !favoriteMsg.value.sendMail && !favoriteMsg.value.sendNotice
    );

    const msgLoading = ref(false);
    const loadFavoriteMsg = async () => {
      if (fileInfo.value && fileInfo.value.favorite) {
        msgLoading.value = true;
        favoriteMsg.value = await FavoriteService.chooseInfo(fileInfo.value.id);
        msgLoading.value = false;
      } else {
        favoriteMsg.value = undefined;
      }
    };
    watch(fileInfo, () => loadFavoriteMsg(), { deep: true });

    /**
     * 修改动态消息
     * @param key
     */
    const handleMsgSend = (key: string) => {
      if (!favoriteMsg.value) return;

      const favoriteChoose = JSON.parse(JSON.stringify(favoriteMsg.value)) as FavoriteChoose;
      favoriteChoose[key] = favoriteChoose[key] ? 0 : 1;
      FavoriteService.chooseSend(favoriteChoose).then((res) => {
        if (res && res.status === 'success') {
          if (favoriteMsg.value && favoriteMsg.value.dataId === favoriteChoose.dataId) {
            favoriteMsg.value[key] = favoriteMsg.value[key] ? 0 : 1;
          }

          $q.notify({
            position: 'top',
            type: 'positive',
            message: res.message
          });
        }
      });
    };

    //#endregion 星标文件动态消息

    return {
      domain,

      fileInfo,
      fileIcon,

      userNames,

      parent,
      openParent,

      favoriteMsg,
      isCanSetupMsg,
      isEmptyMsg,
      handleMsgSend
    };
  }
});
