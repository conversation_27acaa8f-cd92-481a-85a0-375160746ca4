import type { ComponentPublicInstance } from 'vue';
import 'vue-router';

declare module 'vue-router' {
  type NavigationGuardNextCallback = (vm: ComponentPublicInstance) => any;

  type NavigationGuardReturn = void | Error | RouteLocationRaw | boolean | NavigationGuardNextCallback;

  interface RouteActivateGuard {
    (to: RouteLocationNormalized, from: RouteLocationNormalized):
      | NavigationGuardReturn
      | Promise<NavigationGuardReturn>;
  }

  interface RouteMeta {
    /** 页面标题 */
    title?: string;

    /** 当前路由守卫 */
    canActivate?: RouteActivateGuard[];

    /** 下级路由守卫 */
    canActivateChild?: RouteActivateGuard[];
  }
}
