import { defineStore } from '@hetu/core';
import { computed, ref } from 'vue';
import { Document, Frame, Page } from '../models';

/**
 * 文档存储store
 * <AUTHOR>
 */
export const useDocumentStore = defineStore(() => {
  const document = ref<Document>(new Document());

  const staticData = computed(() => document.value.staticData);

  const staticDataSheets = computed(() => Object.keys(staticData.value));

  // 主容器
  const mainFrame = computed(() => {
    const homePage = document.value.children.find((p) => p.id === document.value.home) as Page;

    return homePage.children.find((f) => f.id === homePage.main) as Frame;
  });

  const fontFamilys = ref<string[] | { name: string }[]>([]);

  const mode = ref<'design' | 'preview' | 'share'>('design');

  const isDesignMode = computed(() => mode.value === 'design');
  const isPreviewMode = computed(() => mode.value === 'preview');
  const isShareMode = computed(() => mode.value === 'share');

  const reset = () => {
    document.value = new Document();
    mode.value = 'design';
  };

  return {
    document,
    staticData,
    staticDataSheets,
    mainFrame,

    /** 字体配置 */
    fontFamilys,

    mode,
    isDesignMode,
    isPreviewMode,
    isShareMode,

    reset
  };
});
