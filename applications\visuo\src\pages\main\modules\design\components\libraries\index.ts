import { useApp } from '@hetu/core';
import type { App } from 'vue';
import Frame from './frame/frame.vue';
import Entrance from './entrance/entrance.vue';
import Text from './text/text.vue';

export { Frame, Entrance, Text };

export const components = [Frame, Entrance, Text];

let isRegister = false;

function install(app: App) {
  // 全局注册组件
  components.forEach((component) => app.component(component.name as string, component));
  isRegister = true;
}

export function registerDesignLibraries() {
  const app = useApp();
  !isRegister && install(app);
}
