.ht-filter {
  .q-btn-group > .q-btn-item:not(:first-child) {
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
  }

  .q-btn-group > .q-btn-item:not(:last-child) {
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
  }

  .q-tab {
    &__label {
      font-weight: 400;
    }
  }

  .q-field--with-bottom {
    padding-bottom: 0;

    .q-field__bottom {
      min-height: auto;
      padding: 2px 12px;
      font-size: 10px;
    }
  }
}

.ht-analysis-list.q-list {
  @apply py-2;

  &--dense {
    .q-item {
      &__section--avatar {
        @apply pr-2;

        min-width: 1.5rem;
      }

      &--danger {
        .q-item__section {
          color: $negative;

          &--avatar {
            color: $negative;
          }
        }

        &.q-hoverable:hover {
          .q-focus-helper {
            @apply opacity-100;

            background: $negative;
          }

          .q-item__section {
            @apply color-white z-1;

            &--avatar {
              @apply color-white;
            }
          }
        }
      }
    }
  }
}
