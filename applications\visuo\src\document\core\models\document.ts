import type { WidgetBlock } from './block';
import { GraphBasic, GraphType } from './graph-basic';
import { Page } from './page';
import staticData from './staticData.json';

/**
 * 设计文件
 * <AUTHOR>
 */
export class Document extends GraphBasic {
  type = GraphType.Document;

  /** 封面 */
  thumbnail = '';

  /** 主题 */
  theme = {};

  /** 主页id */
  home = '';

  /** 页面 */
  children: Page[] = [new Page()];

  /** 变量池 */
  variables = [];

  /** 静态数据 */
  staticData = staticData;

  /** 组件配置列表 */
  blocks: WidgetBlock[] = [];

  /** 交互配置 */
  program = {};

  /** 全局样式 */
  styles = [];

  /** 动画 */
  animation = {};

  /** 版本 */
  edition = '2025';

  /** 文件版本 */
  version = '1.0.0';
}

import type { HttpApi } from '@hetu/http';

export class DocumentApi implements HttpApi {
  info = 'hetu/document/info';
}
