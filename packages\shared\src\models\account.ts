/**
 * 账号模式
 */
export enum AccountMode {
  Password = 1,
  Sms,
  Email
}

/**
 * 账号配置信息
 */
export interface AccountConfig {
  /** 手机号登录 */
  phoneEnabled: 0 | 1;
  /** 邮箱登录 */
  emailEnabled: 0 | 1;
  /** 账号登录 */
  accountEnabled: 0 | 1;
  /** 注册 */
  registerEnabled: 0 | 1;
  /** 图形验证码 */
  requireAccountVerified: 0 | 1;
  /** 短信验证码 */
  requirePhoneVerified: 0 | 1;
  /** 电子邮箱验证码 */
  requireEmailVerified: 0 | 1;
  /** 第三方配置信息 */
  authingConfigs: AuthingConfig[];
}

/**
 * 第三方配置信息
 */
export interface AuthingConfig {
  socialName: string;
  authorizationUrl: string;
  qrcodeUrl: string;
  authingType: string;
  scanCode: 0 | 1;
  linkType: 'open' | 'iframe';
  openType: string;
}
