import { createSingleClass } from '@hetu/core';
import { HttpApiService, responseData, type HttpApi, responseReject, type ResponseResult } from '@hetu/http';
import { SYSTEM_API_PREFIX } from '../models';
import type { ACLDataType } from '@hetu/acl';

class PermissionsApi implements HttpApi {
  permissions = `${SYSTEM_API_PREFIX}/api/v1/permissions/`;
}

class PermissionsServiceCtor extends HttpApiService<PermissionsApi> {
  httpApi = new PermissionsApi();

  httpModuleKey = 'permissions';

  initialized = false;

  /**
   * 获取应用权限配置
   * @param app 应用标识
   */
  getConfig(...app: string[]) {
    this.httpApi.permissions;
    return this.http
      .get<ResponseResult<ACLDataType>>(`${this.api.permissions}${app.join(',')}`)
      .then(responseData, responseReject);
  }
}

/**
 * 权限服务类
 * <AUTHOR>
 */
export const PermissionsService = createSingleClass(PermissionsServiceCtor);
