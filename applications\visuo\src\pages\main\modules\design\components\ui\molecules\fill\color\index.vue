<template>
  <div class="vis-fill-color">
    <vis-color-picker v-model="colorValue" @update:modelValue="colorUpdate" />

    <vis-fill-color-input v-model="computedModel" />

    <template v-if="palette.length">
      <q-separator class="q-mt-sm q-mb-md" />
      <q-color
        class="vis-color-picker"
        v-model="colorValue"
        default-view="palette"
        :palette="palette"
        format-model="rgba"
        no-header
        no-footer
        square
        flat
        @update:modelValue="colorUpdate"
      />
    </template>

    <q-separator class="q-my-sm" />
    <div class="text-xs mb-2">系统</div>
    <q-color
      class="vis-color-picker"
      v-model="colorValue"
      default-view="palette"
      :palette="SystemPalette"
      format-model="rgba"
      no-header
      no-footer
      square
      flat
      @update:modelValue="colorUpdate"
    />
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
