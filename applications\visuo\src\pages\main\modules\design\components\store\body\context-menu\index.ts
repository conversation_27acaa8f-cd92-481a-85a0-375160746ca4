import { defineComponent, computed, ref } from 'vue';
import type { PropType } from 'vue';
import { QMenu } from 'quasar';

/**
 * 右键菜单组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-context-menu',
  components: {},
  props: {
    menuItems: {
      type: Array as PropType<
        {
          key: string;
          label: string;
          icon?: string;
          action?: () => void;
          disabled?: boolean;
          separator?: boolean;
        }[]
      >,
      required: true,
      default: () => []
    }
  },
  emits: ['menu-action', 'show', 'hide'],
  expose: ['show', 'hide'],
  setup(props, { emit }) {
    const showMenu = ref(false);
    const contextMenu = ref<QMenu | null>(null);
    const currentContext = ref<any>(null);

    const hasSeparators = computed(() => {
      return props.menuItems.some((item) => item.separator);
    });

    const groupedItems = computed(() => {
      return props.menuItems.filter((item) => !item.separator);
    });

    const show = (event: MouseEvent, contextData?: any) => {
      currentContext.value = contextData;
      showMenu.value = true;
      emit('show', event);

      // 阻止默认右键菜单
      event.preventDefault();
    };

    const hide = () => {
      showMenu.value = false;
      emit('hide');
    };

    const handleMenuItemClick = (item: any) => {
      if (item.action) {
        item.action();
      }
      emit('menu-action', item.key, currentContext.value);
    };

    const onBeforeShow = () => {
      // 可以在这里做一些准备工作
    };

    const onHide = () => {
      currentContext.value = null;
    };

    return {
      showMenu,
      contextMenu,
      currentContext,
      hasSeparators,
      groupedItems,
      show,
      hide,
      handleMenuItemClick,
      onBeforeShow,
      onHide
    };
  }
});
