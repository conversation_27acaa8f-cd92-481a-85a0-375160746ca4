import { SettingsService, type Config } from '@hetu/theme';
import { HttpClient } from '@hetu/http';
import { setupSystem } from '../system';

/**
 * 应用启动服务
 * <AUTHOR>
 */
export async function startup(ready?: (config: Config) => void) {
  const url = `./static/config/config.json`;
  const config = await HttpClient.get<Config>(url).then((response) => response.data);
  SettingsService.setConfig(config);

  try {
    config && (await setupSystem(config.api));
    // eslint-disable-next-line no-empty
  } catch (e) {}

  ready && ready(config);

  return config;
}
