export interface SingleClass<T> {
  /** 当前类实例 */
  readonly INSTANCE: T;

  new (...args: any[]): T;

  /**
   * 获取类实例
   * @param args 构造参数, 仅在第一次调用时有效
   */
  getInstance(...args: any[]): T;
}

/**
 * 定义单例类: 第一次调用返回方法或 `[类名].INSTANCE` 时创建当前类实例
 * - 可在第一次调用进行实例化时传参
 * ```ts
 * // test.ts
 * class Test {
 *   a = 123;
 *   constructor(a?: number) {
 *     this.a = a ?? this.a;
 *   }
 * }
 * export const useTest = defineSingleClass(Test);
 *
 * // use.ts
 * import { useTest } from './test';
 * const test = useTest();
 * // const test = useTest(111); // 第一次调用时可传参
 * console.log(test.a); // -> 123
 * test.a = 234;
 *
 * // use2.ts
 * import { useTest } from './test';
 * const test = useTest();
 * console.log(test.a); // -> 234
 * ```
 * @param constructor 构造类
 */
export function defineSingleClass<T>(constructor: { new (...args: any[]): T }) {
  let instance: T;

  const getInstance = (...args: any[]) => {
    if (!instance) {
      instance = new constructor(...args);
    }

    return instance;
  };

  Object.defineProperties(constructor, {
    INSTANCE: {
      get: () => getInstance()
    },
    getInstance: {
      value: (...args: any[]) => getInstance(...args)
    }
  });

  return (...args: any[]) => getInstance(...args);
}

/**
 * 单例类装饰器: 暂不使用
 * ```ts
 * @Single
 * export class Test {
 *   a = 123;
 * }
 *
 * const test = useSingleClass(Test);
 * console.log(test.a); // -> 123
 * ```
 * @deprecated
 */
export const Single = defineSingleClass;

/**
 * 创建单例类
 * - 可在第一次调用进行实例化时传参
 * ```ts
 * // test.ts
 * export const Test = createSingleClass(class Test {
 *   a = 123;
 *   constructor(a?: number) {
 *     this.a = a ?? this.a;
 *   }
 * });
 *
 * // use.ts
 * import { Test } from './test';
 * // const test = Test.getInstance(111); // 第一次调用时可传参
 * console.log(Test.a); // -> 123
 * Test.a = 234;
 *
 * // use2.ts
 * import { Test } from './test';
 * console.log(Test.a); // -> 234
 * ```
 * @param constructor 构造类
 */
export function createSingleClass<T>(constructor: { new (...args: any[]): T }) {
  const clazz = typedSC(constructor);
  defineSingleClass(constructor);

  const proxy = new Proxy(clazz, {
    get(target: { new (...args: any[]): T }, prop: string | symbol) {
      if (prop === '__ob__') {
        return (target as any)[prop];
      }

      if (prop === 'INSTANCE') {
        return clazz.INSTANCE;
      }

      if (prop === 'getInstance') {
        return clazz.getInstance;
      }

      return clazz.INSTANCE[prop as keyof T];
    },
    set(target: { new (...args: any[]): T }, prop: string | symbol, value: any) {
      clazz.INSTANCE[prop as keyof T] = value;

      return true;
    }
  });

  return proxy as typeof clazz & T;
}

/**
 * 使用单例类
 * ```ts
 * // test.ts
 * export class Test {
 *   a = 123;
 * }
 * defineSingleClass(Test);
 *
 * // use.ts
 * const test = useSingleClass(Test);
 * console.log(test.a); // -> 123
 * ```
 * @param constructor 构造类
 */
export function useSingleClass<T>(constructor: { new (...args: any[]): T }): T {
  const clazz = typedSC(constructor);
  if (!(clazz.INSTANCE instanceof constructor)) {
    throw new Error(`${constructor.name} 未提供单例实例`);
  }

  return clazz.INSTANCE;
}

/**
 * 类型转换为 `SingleClass`
 * ```ts
 * const Test = typedSC(class Test {
 *   a = 123;
 * });
 * console.log(Test.INSTANCE); // -> undefined
 *
 * const useTest = defineSingleClass(Test);
 * const test = useTest();
 * console.log(Test.INSTANCE === test); // -> true
 * ```
 * @param constructor 构造类
 */
export function typedSC<T>(constructor: { new (...args: any[]): T }) {
  return constructor as unknown as SingleClass<T>;
}
