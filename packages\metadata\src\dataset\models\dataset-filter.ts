import type { DataFilter } from '../../filter';

/**
 * 数据集数据过滤
 * <AUTHOR>
 */
export class DatasetDataFilter {
  /** 相对日期 */
  filterDates: DataFilter[] = [];

  /** 值/日期 范围 */
  filterScopes: DataFilter[] = [];

  /** Null 值 */
  filterNulls: DataFilter[] = [];

  /** 通配符 */
  filterWildcards: DataFilter[] = [];

  /** 选定值 */
  filterChoices: DataFilter[] = [];
}

/**
 * 数据集筛选过滤配置
 * <AUTHOR>
 */
export class DatasetFilter {
  /** 数据集筛选过滤配置值汇总 */
  dataFilter = new DatasetDataFilter();
}

/**
 * 数据字段值信息
 * <AUTHOR>
 */
export interface DatasetFieldValueInfo {
  /** 值 */
  columnValue: any;

  /** 值个数 */
  columnCount: number;

  /** 值占比 */
  columnRate: number;
}
