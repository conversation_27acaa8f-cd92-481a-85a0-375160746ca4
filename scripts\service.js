const fs = require('fs');
const { args, choiceExistsApps, run, resolve } = require('@hetu/cli');

const service = async () => {
  const command = args.command ?? 'serve';

  // 当前已拉取的全部应用: true, 默认值
  // 选择应用: choices | c
  // 指定应用: 应用标识, 多个应用使用 `,` 拼接
  let target = args.target;

  let apps = '';

  if (['choices', 'c'].includes(target)) {
    // 选择应用, 选择范围为当前工程下已拉取的子应用
    apps = (await choiceExistsApps()).join(',');
  } else {
    apps = typeof target === 'string' ? target.split(' ').join(',') : target;
  }

  if (apps) {
    const analyze = args.analyze;
    await run('cross-env', [
      `VITE_TARGET_APPS=${apps}`,
      ...(analyze ? ['cross-env', 'VITE_APP_ANALYZE=on'] : []),
      'pnpm',
      command
    ]);

    if (command === 'build') {
      fs.rmSync(`${resolve('./public')}/versions.json`);
    }
  }
};

service();
