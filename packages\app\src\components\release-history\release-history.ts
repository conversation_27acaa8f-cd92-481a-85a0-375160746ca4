import { ref, defineComponent, type PropType, computed } from 'vue';
import { ACLResource } from '@hetu/acl';
import { useDialogPluginComponent, type QTableColumn, useQuasar } from 'quasar';
import type { ReleaseHistory } from '../../models';
import type { ResponseResult } from '@hetu/http';

export default defineComponent({
  name: 'ht-app-release-history',
  emits: [...useDialogPluginComponent.emits],
  props: {
    name: String,
    value: {
      type: Array as PropType<ReleaseHistory[]>,
      required: true
    },
    acl: {
      type: Object as () => ACLResource<any>,
      required: true
    },
    rollback: {
      type: Function as PropType<(row: ReleaseHistory) => Promise<ResponseResult | null>>,
      required: true
    },
    delete: {
      type: Function as PropType<(row: ReleaseHistory) => Promise<ResponseResult | null>>,
      required: true
    }
  },
  setup(props) {
    const $q = useQuasar();
    const { dialogRef, onDialogHide, onDialogCancel } = useDialogPluginComponent();

    const rows = ref(props.value);
    const columns: QTableColumn<ReleaseHistory & { tools: string }>[] = [
      {
        name: 'versionNumber',
        required: true,
        label: '版本',
        field: 'versionNumber',
        align: 'center',
        style: 'width:50px'
      },
      {
        name: 'fullName',
        label: '发布人',
        field: 'fullName',
        align: 'center',
        style: 'width:120px'
      },
      {
        name: 'createdTime',
        label: '发布时间',
        field: 'createdTime',
        align: 'center',
        style: 'width:120px'
      },
      {
        name: 'remark',
        label: '备注',
        align: 'left',
        field: 'remark'
      },
      {
        name: 'tools',
        required: true,
        label: '操作',
        field: 'tools',
        align: 'center',
        style: 'width:100px'
      }
    ];

    const pagination = ref({
      rowsPerPage: 0
    });

    const onRollback = (row: ReleaseHistory) => {
      $q.dialog({
        title: '提示',
        message: `确定恢复至版本 [ ${[row.versionNumber || '']} ] ?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(async () => {
        const result = await props.rollback(row);
        if (result?.status === 'success') {
          $q.notify({ position: 'top', type: 'positive', message: result.message });
          onDialogCancel();
        }
      });
    };

    const onDelete = (row: ReleaseHistory, index: number) => {
      if (row.currentRelease) return;

      $q.dialog({
        title: '提示',
        message: `确定删除版本 [ ${[row.versionNumber || '']} ] ?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(async () => {
        const result = await props.delete(row);
        if (result?.status === 'success') {
          rows.value.splice(index, 1);
          $q.notify({ position: 'top', type: 'positive', message: result.message });
        }
      });
    };

    return {
      dialogRef,
      onDialogHide,
      onDialogCancel,

      rows,
      columns,
      pagination,

      onRollback,
      onDelete
    };
  }
});
