@import '../../../../../index';

.#{$vis-prefix}-store-page {
  @apply p-0;

  &-node,
  &-node__parent {
    @apply p-0;
  }

  &-node.drag-target-parent {
    @apply bg-#efefef rounded;
  }

  &-node__parent {
    &.selected {
      @apply bg-#f2f9ff rounded;

      > .#{$vis-prefix}-store-page-header {
        @apply bg-#e5f4ff rounded;
      }
    }

    > .#{$vis-prefix}-store-page-header:hover {
      @apply bg-#efefef rounded;
    }
  }

  &-node__child {
    @apply pl-12px;

    &:hover {
      @apply bg-#efefef rounded;
    }

    &.selected {
      @apply bg-#e5f4ff rounded;
    }
  }

  &-header {
    @apply flex flex-nowrap items-center mt-0 p-1px cursor-pointer;

    &-arrow {
      @apply text-12px transition-transform transition-duration-300 ease;
    }

    &-arrow.rotate {
      @apply rotate-90;
    }

    &-content {
      @apply flex justify-between w-full;

      &-title {
        @apply flex items-center text-12px lh-7 h-7;

        &-icon {
          @apply flex items-center text-16px mr-2px;
        }

        &-input {
          @apply pl-3px mr-1px bg-#fff rounded;
        }

        &-text {
          @apply flex-1 flex-grow-1 lh-7 h-7 max-w-160px pl-3px;
        }
      }
    }
  }

  &-collapsible {
    @apply grid transition-property-all transition-duration-300 ease overflow-hidden;

    grid-template-rows: 1fr;
  }

  &-collapsible.folder {
    grid-template-rows: 0fr;
  }

  &-children {
    @apply pl-6px min-h-0px;
  }

  &-ghost {
    @apply opacity-50 rounded;
  }

  &-drag {
    @apply opacity-0;
  }
}
