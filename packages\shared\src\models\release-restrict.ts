/**
 * 已发布数据查询验证参数
 * <AUTHOR>
 */
export type ReleaseRestrictParams = ReleaseRestrictCodeParams | ReleaseRestrictIdParams;
export interface ReleaseRestrictCodeParams extends RestrictParams {
  /** 发布 ID */
  releaseCode: string;
}

export interface ReleaseRestrictIdParams extends RestrictParams {
  /** 数据 ID */
  dataId: string;
  /** 起始数据 ID: 用于访问限制验证 */
  sourceDataId: string;
}

interface RestrictParams {
  /** 访问密码 */
  accessPassword?: string;

  /** 访问参数 */
  accessParams?: string;

  /** 验证码 ID */
  captchaCodeId?: string;

  /** 验证码内容 */
  captchaCode?: string;
}

/**
 * 已发布数据查询响应结果
 * <AUTHOR>
 */
export type ReleaseResponseData<T> = {
  /** 验证信息 */
  validate: {
    /** 是否校验图片验证码 */
    validateCaptcha: 0 | 1;
    /** 是否校验密码 */
    validatePassword: 0 | 1;
  };

  /** 域名标识 */
  domain: string;

  /** 租户 ID */
  tenantId: string;

  /** 已发布数据名称 */
  dataName: string;
} & {
  /** 已发布数据 */
  [key: string]: T;
};
