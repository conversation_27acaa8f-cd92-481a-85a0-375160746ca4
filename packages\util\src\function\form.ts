import { max, max<PERSON>en, min, minLen, required } from './validator';

/** 表单验证规则集合 */
export type FormRules<T> = {
  [P in keyof T]: FormRule[];
};

/** `Quasar` 表单验证规则集合 */
export type QFormRules<T> = {
  [P in keyof T]: Array<(v: any) => string | boolean | Promise<string | boolean>>;
};

/** 表单验证规则模型 */
export interface FormRule {
  /** 是否必填 */
  required?: boolean;

  /**
   * 最大
   * - `type` 是 `string` 时表示为最大输入长度
   * - `type` 是 `number` 时表示为最大值
   */
  max?: number;

  /**
   * 最小
   * - `type` 是 `string` 时表示为最小输入长度
   * - `type` 是 `number` 时表示为最小值
   */
  min?: number;

  /** 值类型 */
  type?: 'string' | 'number';

  /** 错误提示消息 */
  message?: string;

  /** 自定义校验函数 */
  validator?: (v: any) => string | boolean | Promise<string | boolean>;

  [key: string]: any;
}

/** 表单字段值验证方法 */
export class FormValidator {
  static required(value: string, rule?: FormRule): string | true {
    return required(value) || rule?.message || '必填项';
  }

  static maxLen(value: string, rule: FormRule): string | true {
    return maxLen(value, rule.max as number) || rule.message || `最大输入长度 ${rule.max}`;
  }

  static minLen(value: string, rule: FormRule): string | true {
    return minLen(value, rule.min as number) || rule.message || `最小输入长度 ${rule.min}`;
  }

  static max(value: string | number, rule: FormRule): string | true {
    return max(value, rule.max as number) || rule.message || `最大值 ${rule.max}`;
  }

  static min(value: string | number, rule: FormRule): string | true {
    return min(value, rule.min as number) || rule.message || `最小值 ${rule.min}`;
  }

  static check(value: any, rule: FormRule): string | boolean | Promise<string | boolean> {
    let vaild: string | boolean | Promise<string | boolean> = true;

    if (rule.required) {
      vaild = this.required(value, rule);
    }

    if (vaild === true && rule.min !== undefined) {
      vaild = rule.type === 'number' ? this.min(value, rule) : this.minLen(value, rule);
    }

    if (vaild === true && rule.max !== undefined) {
      vaild = rule.type === 'number' ? this.max(value, rule) : this.maxLen(value, rule);
    }

    if (vaild === true && rule.validator) {
      vaild = rule.validator(value);

      if (vaild instanceof Promise) {
        vaild = new Promise((resolve) => {
          (vaild as Promise<string | boolean>).then((result) => {
            if (result === false) {
              result = rule.message || '验证失败';
            }
            resolve(result);
          });
        });
      } else if (vaild === false) {
        vaild = rule.message || '验证失败';
      }
    }

    return vaild;
  }
}

/**
 * 创建 `Quasar` 表单验证规则
 * @param rules 表单验证规则集合
 * @returns
 */
export function createQFormRules<T>(rules: FormRules<Partial<T>>) {
  const qRules = {} as QFormRules<T>;

  Object.keys(rules).forEach((key) => {
    const arr: any[] = [];
    rules[key as keyof FormRules<T>].forEach((rule) => {
      arr.push((v: any) => FormValidator.check(v, rule));
    });
    qRules[key as keyof QFormRules<T>] = arr;
  });

  return qRules;
}
