<template>
  <q-popup-proxy
    v-model="popShow"
    class="vis-popup hide-scrollbar"
    :class="[className, { 'opacity-0': !visible }]"
    :transition-duration="0"
    breakpoint="0"
  >
    <q-card :style="{ width: width, height: height }">
      <!-- 标题 -->
      <q-card-section v-if="title" class="no-padding">
        <q-toolbar class="vis-popup__title cursor-move">
          {{ title }}
          <q-space />
          <q-btn icon="close" size="sm" @click="handleHide" round dense flat />
        </q-toolbar>
        <q-separator />
      </q-card-section>
      <!-- 内容 -->
      <q-card-section class="vis-popup__content">
        <slot name="default"></slot>
      </q-card-section>
    </q-card>
  </q-popup-proxy>
</template>
<script lang="ts" src="./index.ts"></script>
