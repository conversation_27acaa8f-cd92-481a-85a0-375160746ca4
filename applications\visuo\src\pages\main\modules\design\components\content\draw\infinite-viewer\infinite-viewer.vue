<template>
  <vue-infinite-viewer
    id="vis-design-infinite-canvas"
    ref="infiniteCanvasRef"
    :usePinch="true"
    :useAutoZoom="true"
    :useWheelScroll="true"
    :useForceWheel="true"
    :useMouseDrag="handAction.active"
    :useResizeObserver="true"
    wheelContainer=".vis-design-center"
    :pinchThreshold="50"
    :maxPinchWheel="3"
    :margin="0"
    :class="'vis-design-infinite-canvas ' + className"
    @click="onClick"
    @scroll="onScroll"
    @pinch="onPinch"
    @dragStart="onDragStart"
  >
    <slot></slot>
  </vue-infinite-viewer>
</template>
<script lang="ts" src="./infinite-viewer.ts"></script>
<style lang="scss" src="./infinite-viewer.scss"></style>
