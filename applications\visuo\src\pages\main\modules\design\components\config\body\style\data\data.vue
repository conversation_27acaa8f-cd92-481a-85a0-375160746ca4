<template>
  <div v-if="activeBlock">
    <div class="vis-config-data">
      <div class="vis-config-data__tree">
        <vis-dataset-tree v-model="activeBlock.datasetId" :name="datasetName" @onSelect="selectDataset" />
      </div>
      <div class="vis-config-data__btn">
        <q-btn class="!w-6" @click="openDataSheetDialog">
          <ht-icon name="vis-chart" class="vis-icon"></ht-icon>
        </q-btn>
      </div>
    </div>
    <q-separator></q-separator>
    <div v-for="(match, key) in widgetDataConfig" :key="key">
      <div class="vis-config-data__match">
        <div class="vis-config-data__match-name">
          {{ match.name }}
        </div>
        <div class="vis-config-data__match-field">
          <vis-field-item :mapKey="key" :fieldMapping="fieldMapping" />
        </div>
      </div>
      <q-separator></q-separator>
    </div>
    <div class="vis-config-data__match">
      <div class="vis-config-data__match-name">
        <span>筛选</span>
        <q-btn>
          <ht-icon class="vis-icon" name="vis-add" />
          <vis-field-list
            :fieldList="fieldMapping?.datasetField"
            :activeIds="filterActiveIds"
            :offset="[-24, 8]"
            @addField="addFilterField"
          />
        </q-btn>
      </div>
      <div class="vis-config-data__match-field" v-if="dataMapping.filters?.length > 0">
        <div v-for="(filter, index) in dataMapping.filters" :key="index" class="container">
          <div class="field-item">
            <q-btn @click="openFilterDialog(Number(index))">
              <ht-icon :name="`vis-${getFieldIcon(filter?.fieldDatatype)}`" class="vis-icon w-6"></ht-icon>
            </q-btn>
            <q-btn :ripple="false" class="field-btn">
              <span class="w-[calc(100%-24px)] text-left px-2">
                {{ filter?.fieldName || filter?.fieldAlias || '字段' }}
              </span>
            </q-btn>
            <q-btn :ripple="false" class="remove-btn !w-6" @click="removeFilterField(Number(index))">
              <ht-icon name="vis-remove" class="vis-icon"></ht-icon>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
    <q-separator></q-separator>
    <div class="p-3">
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-select v-model="activeBlock.refreshType" :options="refreshTypeOptions"> </vis-select>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-number v-model="activeBlock.refreshSecond" :min="0" :step="1" icon="o_av_timer">
                <template #append>
                  <span>秒/次</span>
                </template>
              </vis-number>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./data.ts"></script>
<style lang="scss" src="./data.scss"></style>
