import { computed, nextTick, ref } from 'vue';
import { VIS_DESIGN_INFINITE_CANVAS } from '../models';
import { useDesignStore } from '../stores';
import {
  Block,
  Constraints,
  DirectionType,
  Frame,
  Graph,
  GraphType,
  GridItem,
  ResizeType,
  TextAlign,
  TextBox,
  useDocumentStore,
  VerticalAlign
} from '@vis/document-core';
import { isString } from 'lodash-es';
import type { Records } from '@hetu/util';

/**
 * 图形查找
 * <AUTHOR>
 */
export const useGraph = () => {
  const designStore = useDesignStore();
  const documentStore = useDocumentStore();

  const doc = computed(() => documentStore.document.value);

  const pageGraphs = computed(() => {
    return designStore.active.value.page?.children as Graph[];
  });

  const counter = computed(() => ++designStore.counter.value);

  const activeGraphIds = computed(() => designStore.active.value.graphIds);

  const canvasState = computed(() => designStore.canvasState.value);

  /**
   * 根据id查找图形
   * @param id
   * @returns
   */
  const findGraph = (id: string): Graph | undefined => {
    const search = (items: Graph[]): Graph | undefined => {
      for (const item of items) {
        if (item.id === id) return item;
        if (item.children?.length) {
          const found = search(item.children);
          if (found) return found;
        }
      }
    };
    return search(pageGraphs.value);
  };

  /**
   * 根据ids过滤Graph
   * @param ids
   * @returns
   */
  const filterGraph = (ids: string[]): Graph[] => {
    const idSet = new Set(ids);
    const result: Graph[] = [];

    const search = (items: Graph[]) => {
      for (const item of items) {
        if (idSet.has(item.id)) result.push(item);
        if (item.children?.length) search(item.children);
      }
    };

    search(pageGraphs.value);
    return result;
  };

  /**
   * 过滤图形
   * @param callback
   * @returns
   */
  const filterGraphs = (callback: (w: Graph) => boolean) => {
    const result: Graph[] = [];

    const search = (items: Graph[]) => {
      for (const item of items) {
        if (callback(item)) result.push(item);
        if (item.children?.length) search(item.children);
      }
    };

    search(pageGraphs.value);
    return result;
  };

  /**
   * 返回所有的父级图形, 从外向内
   * @param id
   * @returns
   */
  const findGraphParents = (id: string): Graph[] => {
    const result: Graph[] = [];

    const findInTree = (items: Graph[], targetId: string): boolean => {
      for (const item of items) {
        if (item.id === targetId) return true;
        if (item.children?.length && findInTree(item.children, targetId)) {
          result.unshift(item); // 将父级插入数组头部
          return true;
        }
      }
      return false;
    };

    findInTree(pageGraphs.value, id);
    return result;
  };

  /**
   * 返回当前图形的兄弟图形
   * @param id
   * @param self 是否包含自身，默认不包含
   */
  const graphBrothers = (id: string, self = false) => {
    const widget = findGraph(id);
    if (!widget) {
      return [];
    }
    const parent = findGraph(widget.parent);
    let brothers = pageGraphs.value;
    if (parent) {
      brothers = parent.children || [];
    }

    if (self) {
      return brothers;
    } else {
      return brothers.filter((w) => w.id !== id);
    }
  };

  const isFreeform = (frame: Frame) => frame.autoLayout.direction === DirectionType.Freeform;

  const isFlex = (frame: Frame) =>
    [DirectionType.Horizontal, DirectionType.Vertical].includes(frame.autoLayout.direction);

  const isGrid = (frame: Frame) => frame.autoLayout.direction === DirectionType.Grid;

  /**
   * 将sources移动到targetId下
   * @param sources
   * @param targetId
   * @param insertIndex 插入位置
   */
  const moveGraphs = (sources: Graph[] | string[], targetId = VIS_DESIGN_INFINITE_CANVAS, insertIndex?: number) => {
    let target: Graph;
    sources.forEach((source) => {
      source = (typeof source === 'string' ? findGraph(source) : source) as Graph;
      const sourceParents = findGraphParents(source.id);
      let sourceParent = pageGraphs.value;
      // 如果有父级，将图形的位置计算到最外层
      if (sourceParents.length) {
        sourceParents.forEach((p) => {
          const px = p.transform.translate[0];
          const py = p.transform.translate[1];
          source.transform.translate[0] += px;
          source.transform.translate[1] += py;
        });
        sourceParent = sourceParents[sourceParents.length - 1].children || [];
      }
      // 在位置中删除图形
      const index = sourceParent.findIndex((item) => item.id === (<Graph>source).id);
      if (index !== -1) {
        sourceParent.splice(index, 1);
      }

      // 计算组件在target的位置
      if (targetId === VIS_DESIGN_INFINITE_CANVAS) {
        // 移动到根
        source.parent = '';
        source.gridItem = undefined;
        source.constraints = undefined;
        // 还原图形的适应方式为固定宽高
        source.limitSize.width.resize = ResizeType.Fixed;
        source.limitSize.height.resize = ResizeType.Fixed;
        if (typeof insertIndex !== 'undefined') {
          pageGraphs.value.splice(insertIndex, 0, source);
          pageGraphs.value.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        } else {
          source.order = pageGraphs.value.length;
          pageGraphs.value.push(source);
        }
      } else {
        target = findGraph(targetId) as Graph;
        const targetParents = findGraphParents(targetId);

        const sourceX = source.transform.translate[0];
        const sourceY = source.transform.translate[1];

        // 计算新位置：将图形移动到目标位置
        [...targetParents, target].forEach((ele) => {
          const eleX = ele.transform.translate[0];
          const eleY = ele.transform.translate[1];

          source.transform.translate[0] -= eleX;
          source.transform.translate[1] -= eleY;
        });

        // 如果target的容器类型
        if (target.type === GraphType.Frame) {
          const targetFrame = target as Frame;
          // 自由布局添加约束
          source.constraints = isFreeform(targetFrame) ? new Constraints() : undefined;
          if (isFlex(targetFrame)) {
            // 还原图形的适应方式为固定宽高
            source.limitSize.width.resize = ResizeType.Fixed;
            source.limitSize.height.resize = ResizeType.Fixed;
          }
          // 如果网格布局添加到格子里,
          if (isGrid(targetFrame)) {
            // 如果是固定高度和充满容器保留，适应内容改为固定高度
            if (source.limitSize.width.resize === ResizeType.Adapt) {
              source.limitSize.width.resize = ResizeType.Fixed;
            }
            if (source.limitSize.height.resize === ResizeType.Adapt) {
              source.limitSize.height.resize = ResizeType.Fixed;
            }

            let gridRowCol = canvasState.value.gridRowCol;
            if (!gridRowCol) {
              // 计算每个格子的位置及大小
              handGridItemPosition(targetFrame);

              gridRowCol = getRowColByPositions(targetFrame, sourceX, sourceY);
            }

            // 将source放置到目标gridRowCol的位置
            if (gridRowCol) {
              moveGraphToGrid(source, gridRowCol);
            } else {
              moveGraphToGrid(source, [1, 1]);
            }
          } else {
            // 非网格布局清空grid
            source.gridItem = undefined;
          }
        }

        // 添加图形
        source.parent = targetId;

        if (typeof insertIndex !== 'undefined') {
          target.children?.splice(insertIndex, 0, source);
          target.children?.forEach((graph, index, _) => (graph.order = _.length - 1 - index));
        } else {
          source.order = target.children?.length || 0;
          target.children?.push(source);
        }
      }
    });

    nextTick(() => {
      // 移动到flex布局，容器内的子图形都需要重新计算位置
      if (target && target.type === GraphType.Frame) {
        // flex布局时子元素都需要计算位置
        if (isFlex(target as Frame)) {
          resetGraphXYByDom(target.children as Graph[]);
        }
        // grid布局时只需要计算当前选中的图形
        else if (isGrid(target as Frame)) {
          // 父容器变了，位置尺寸都需要计算
          resetGraphXYSizeByDom(sources as Graph[]);
        }
      }

      activeGraphs(sources);
    });
  };

  /**
   * 根据Dom的位置重新计算图形的x,y
   * @param graphs
   */
  const resetGraphXYByDom = (graphs: Graph[]) => {
    graphs.forEach((g) => {
      const dom = document.querySelector(`[id="${g.id}"]`) as HTMLElement;
      if (dom) {
        g.transform.translate = [Math.round(dom.offsetLeft), Math.round(dom.offsetTop)];
      }
    });
  };

  /**
   * 根据Dom的位置重新计算图形的位置和尺寸
   * @param graphs
   */
  const resetGraphXYSizeByDom = (graphs: Graph[]) => {
    graphs.forEach((g) => {
      const dom = document.querySelector(`[id="${g.id}"]`) as HTMLElement;
      if (dom) {
        g.transform.translate = [Math.round(dom.offsetLeft), Math.round(dom.offsetTop)];
        g.width = Math.round(dom.clientWidth);
        g.height = Math.round(dom.clientHeight);
      }
    });
  };

  /**
   * 计算图形添加到画布时的位置及大小
   */
  const getCanvasPosSize = (left: number, top: number, w?: number, h?: number) => {
    const zoom = designStore.rulerState.value.zoom;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;

    const center = document.querySelector('.vis-design-center') as HTMLElement;
    const canvas = document.querySelector(`#${VIS_DESIGN_INFINITE_CANVAS}`) as HTMLElement;

    const offsetLeft = center.offsetLeft + canvas.offsetLeft;
    const offsetTop = center.offsetTop + canvas.offsetTop;

    const translateX = Math.round(infiniteCanvasRef.value?.getScrollLeft() || 0) * zoom;
    const translateY = Math.round(infiniteCanvasRef.value?.getScrollTop() || 0) * zoom;

    const rect: {
      x: number;
      y: number;
      width?: number;
      height?: number;
    } = {
      x: Math.round((left - offsetLeft + translateX) / zoom),
      y: Math.round((top - offsetTop + translateY) / zoom)
    };
    if (w && h) {
      rect.width = Math.round(w / zoom);
      rect.height = Math.round(h / zoom);
    }
    return rect;
  };

  /**
   * 返回画布距离窗口的距离
   * @returns
   */
  const getCanvasXY = () => {
    const center = document.querySelector('.vis-design-center') as HTMLElement;
    const canvas = document.querySelector(`#${VIS_DESIGN_INFINITE_CANVAS}`) as HTMLElement;

    const offsetLeft = center.offsetLeft + canvas.offsetLeft;
    const offsetTop = center.offsetTop + canvas.offsetTop;
    return { offsetLeft, offsetTop };
  };

  /**
   * 选中图形
   * @returns
   */
  const activeGraphs = (actives?: string[] | Graph[] | HTMLElement[]) => {
    let ids: string[] = [];
    let graphs: Graph[] = [];
    let ele: HTMLElement[] = [];

    if (actives && actives.length) {
      if (isString(actives[0])) {
        ids = actives as string[];
        graphs = filterGraph(ids);
        ele = ids.map((id) => document.querySelector(`[id="${id}"]`) as HTMLElement);
      } else if (actives[0] instanceof HTMLElement) {
        ids = (actives as HTMLElement[]).map((el: HTMLElement) => el.id);
        graphs = filterGraph(ids);
        ele = actives as HTMLElement[];
      } else {
        ids = (actives as Graph[]).map((g) => g.id);
        graphs = actives as Graph[];
        ele = ids.map((id) => document.querySelector(`[id="${id}"]`) as HTMLElement);
      }
    }
    // 画布中选中图形
    designStore.moveableTargets.value = ele;
    designStore.active.value.graphs = graphs;
    designStore.active.value.graphIds = ids;

    // 赋farme
    const len = graphs.length;
    let cFrame = undefined;
    if (len === 1) {
      if (graphs[0].parent) {
        const parentId = graphs[0].parent;
        if (parentId) {
          const parent = findGraph(parentId);
          if (parent && parent.type === GraphType.Frame) {
            cFrame = parent as Frame;
          }
        }
      } else {
        if (graphs[0].type === GraphType.Frame) {
          cFrame = graphs[0] as Frame;
        }
      }
    } else if (len > 1) {
      const parentId = graphs[0].parent;
      if (parentId) {
        const parent = findGraph(parentId);
        if (parent && parent.type === GraphType.Frame) {
          cFrame = parent as Frame;
        }
      }
    }
    designStore.active.value.frame = cFrame;
    console.log('active:', designStore.active.value, 'frame:', designStore.active.value.frame?.id);
  };

  /**
   * 工具栏拖拽添加容器
   * @param left 鼠标离画布左侧的距离
   * @param top 鼠标离画布上侧的距离
   * @param w 选框宽度
   * @param h 选框高度
   */
  const addFrame = (left: number, top: number, w: number, h: number) => {
    const frame = new Frame();
    frame.name = `容器 ${counter.value}`;
    const { x, y, width, height } = getCanvasPosSize(left, top, w, h);
    frame.width = width || 100;
    frame.height = height || 100;
    frame.transform.translate = [x, y];

    addGraphToPage(frame);
  };

  /**
   * 根据尺寸添加容器
   * @param size
   */
  const addFrameBySize = (size: { width: number; height: number }) => {
    const frame = new Frame();
    frame.name = `容器 ${counter.value}`;
    frame.width = size.width;
    frame.height = size.height;
    frame.transform.translate = [0, 0];

    designStore.active.value.page.children.push(frame);

    nextTick(() => {
      activeGraphs([frame]);
    });
  };

  /**
   * 工具栏拖拽添加文本
   * @param left 鼠标离画布左侧的距离
   * @param top 鼠标离画布上侧的距离
   * @param w 选框宽度
   * @param h 选框高度
   */
  const addTextBox = (left: number, top: number, w: number, h: number) => {
    const textBox = new TextBox();
    textBox.name = `文本 ${counter.value}`;
    const { x, y, width, height } = getCanvasPosSize(left, top, w, h);

    textBox.width = width || 100;
    textBox.height = height || 100;
    textBox.transform.translate = [x, y];

    textBox.text.alignHorizontal = TextAlign.Center;
    textBox.text.alignVertical = VerticalAlign.Center;
    addGraphToPage(textBox);
  };

  /**
   * 添加block到页面中,
   * @param graph
   */
  const addBlockToPage = (graph: Graph) => {
    // 计算添加到的容器
    flattenPageFrames();
    activeGraphs();
    // 添加到的容器
    const hitFrame = designStore.canvasState.value.frame;

    if (hitFrame) {
      // 添加到其他容器内
      moveGraphs([graph], hitFrame.id, 0);
    } else {
      // 添加到根级
      graph.order = pageGraphs.value.length;
      pageGraphs.value.unshift(graph);
    }

    nextTick(() => {
      activeGraphs([graph]);
    });
  };

  /**
   * 拖拽画框添加到页面中，容器和文本
   * @param graph
   */
  const addGraphToPage = (graph: Graph) => {
    const x = graph.transform.translate[0];
    const y = graph.transform.translate[1];

    // 计算添加到的容器
    flattenPageFrames();
    activeGraphs();
    // 计算拖动时要放置的容器
    const hitFrame = getFrameByPosition(x, y);
    designStore.canvasState.value.frame = hitFrame;

    if (hitFrame) {
      // 添加到其他容器内
      moveGraphs([graph], hitFrame.id, 0);
    } else {
      // 添加到根级
      graph.order = pageGraphs.value.length;
      pageGraphs.value.unshift(graph);
    }

    nextTick(() => {
      activeGraphs([graph]);
    });
  };

  // 存储平级的容器,用于计算当前鼠标在哪个容器内
  const flattenFrames = ref();

  const flattenPageFrames = (excludeIds: string[] = []) => {
    const frames = JSON.parse(JSON.stringify(designStore.active.value.page.children));
    if (!frames) {
      return [];
    }

    const flattenedArray: Frame[] = [];
    const stack = frames;

    while (stack.length > 0) {
      // 取出栈顶的节点进行处理
      const currentNode = stack.pop() as Frame;

      const { children, ...rest } = currentNode;
      // frame类型，并且不包括选中的图形
      if (rest.type === GraphType.Frame && !excludeIds.includes(rest.id)) {
        flattenedArray.push(rest as Frame);

        // 将子节点倒序放入栈中
        // （这样可以保证先处理左边的子节点）
        if (children) {
          for (let i = children.length - 1; i >= 0; i--) {
            const child = children[i];

            child.transform.translate[0] += rest.transform.translate[0];
            child.transform.translate[1] += rest.transform.translate[1];

            stack.push(child);
          }
        }
      }
    }

    flattenFrames.value = flattenedArray as Frame[];
  };

  /**
   * 返回该坐标所在的容器
   * @param x
   * @param y
   * @returns
   */
  const getFrameByPosition = (x: number, y: number) => {
    const frames = flattenFrames.value;

    let result = undefined;
    const len = frames.length - 1;
    for (let i = len; i >= 0; i--) {
      const f = frames[i];
      const fw = f.width;
      const fh = f.height;
      const fx = f.transform.translate[0];
      const fy = f.transform.translate[1];

      if (x > fx && y > fy && x < fx + fw && y < fy + fh) {
        result = f;
        break;
      }
    }
    return result;
  };

  /**
   * 格式化旋转角度，将角度格式化为-180～180之间
   * @param value
   * @returns
   */
  const formatRotate = (value: number) => {
    let rotate = Math.round(value) % 360;

    if (rotate > 180) {
      rotate -= 360;
    } else if (rotate <= -180) {
      rotate += 360;
    }
    return rotate;
  };

  /**
   * 格式化圆角
   * @param value
   * @returns
   */
  const foramtRadius = (value: number[]) => {
    let radius: number[] = [];
    const len = value.length;
    if (len == 1) {
      radius = new Array(4).fill(value[0]);
    } else if (len == 2) {
      radius = new Array(value[0], value[1], value[0], value[1]);
    } else if (len == 3) {
      radius = new Array(value[0], value[1], value[2], value[1]);
    } else {
      radius = value;
    }
    return radius;
  };

  /**
   * 删除图形
   * @param graphs
   * @returns
   */
  const delGraphs = (graphs: string[] | Graph[]) => {
    if (!graphs.length) {
      return;
    }
    graphs.forEach((g) => {
      let graph = g as Graph;
      if (isString(g)) {
        graph = findGraph(g) as Graph;
      }
      const parentId = graph.parent;
      let parentChildren: Graph[] = [];
      if (parentId) {
        parentChildren = findGraph(parentId)?.children || [];
      } else {
        parentChildren = designStore.active.value.page.children;
      }
      const index = parentChildren.findIndex((gr) => gr.id === graph.id);
      parentChildren.splice(index, 1);
      if ((graph as Block).decoration) {
        const decIndex = doc.value.blocks.findIndex((b) => b.id === (graph as Block).decoration);
        doc.value.blocks.splice(decIndex, 1);
      }
    });
  };

  //#region grid布局

  // 当前activeFrame为grid布局时，每个格子的位置及大小
  const frameGridItemPositions: Records<{ w: number; h: number; x: number; y: number }> = {};

  /**
   * 计算grid布局时每个格子的位置和大小
   * @param frame
   * @return {'row-col': {w: number, h: number, x: number, y: number}}}
   */
  const handGridItemPosition = (frame: Frame) => {
    const gridItemDoms = document.querySelectorAll(`[id="${frame.id}"] > .vis-frame-grid-ghost > div`);
    gridItemDoms.forEach((item) => {
      const dom = item as HTMLElement;
      const w = dom.offsetWidth;
      const h = dom.offsetHeight;
      const x = dom.offsetLeft;
      const y = dom.offsetTop;
      const row = dom.getAttribute('data-row');
      const col = dom.getAttribute('data-col');
      frameGridItemPositions[`${row}-${col}`] = { w, h, x, y };
    });
    return frameGridItemPositions;
  };

  /**
   * 返回鼠标位置所在的格子的行号列号
   * @param frame 移动到的容器
   * @param mouseX 坐标是相对于画布的坐标
   * @param mouseY
   */
  const getRowColByPosition = (frame: Frame, mouseX: number, mouseY: number) => {
    let rowCol;
    const af = flattenFrames.value.find((f: Frame) => f.id === frame.id);
    if (af) {
      // 计算frame在画布里的位置
      const targetFrameX = af.transform.translate[0];
      const targetFrameY = af.transform.translate[1];

      // 计算鼠标在frame中的位置
      const offsetX = mouseX - targetFrameX;
      const offsetY = mouseY - targetFrameY;

      const keys = Object.keys(frameGridItemPositions);
      for (let i = 0; i < keys.length; i++) {
        const grid = keys[i];
        const { x, y, w, h } = frameGridItemPositions[grid];
        if (offsetX > x && offsetY > y && offsetX < x + w && offsetY < y + h) {
          const row = parseInt(grid.split('-')[0]);
          const col = parseInt(grid.split('-')[1]);
          rowCol = [row, col];
          break;
        }
      }
    }

    return rowCol;
  };

  /**
   * 返回鼠标位置所在的格子的行号列号
   * @param frame 移动到的容器
   * @param mouseX 坐标是相对于画布的坐标
   * @param mouseY
   * @param graph 正在移动的图形
   */
  const getRowColByPositions = (frame: Frame, mouseX: number, mouseY: number, graph?: Graph) => {
    // 鼠标所在的行号列号
    const rowCols: number[][] = [];
    let moseRowCol: number[] = [];
    const af = flattenFrames.value.find((f: Frame) => f.id === frame.id);
    if (af) {
      const { gridSize } = frame.autoLayout;
      // 计算frame在画布里的位置
      const targetFrameX = af.transform.translate[0];
      const targetFrameY = af.transform.translate[1];

      // 计算鼠标在frame中的位置
      const offsetX = mouseX - targetFrameX;
      const offsetY = mouseY - targetFrameY;

      const keys = Object.keys(frameGridItemPositions);
      for (let i = 0; i < keys.length; i++) {
        const grid = keys[i];
        const { x, y, w, h } = frameGridItemPositions[grid];
        if (offsetX > x && offsetY > y && offsetX < x + w && offsetY < y + h) {
          const row = parseInt(grid.split('-')[0]);
          const col = parseInt(grid.split('-')[1]);
          moseRowCol = [row, col];
          [3, 1];
          rowCols.push(moseRowCol);
          break;
        }
      }

      if (graph && graph.gridItem) {
        const { rows, columns } = graph.gridItem;
        const rowSpan = rows[1] - rows[0];
        const colSpan = columns[1] - columns[0];
        if (colSpan > 1) {
          for (let i = 1; i < colSpan; i++) {
            // 列的后面有位置加的后面，否则加到前面
            if (moseRowCol[1] + i <= gridSize[1]) {
              rowCols.push([moseRowCol[0], moseRowCol[1] + i]);
            } else if (moseRowCol[1] - i > 0) {
              rowCols.unshift([moseRowCol[0], moseRowCol[1] - i]);
            }
          }
        }
        if (rowSpan > 1) {
          for (let i = 1; i < rowSpan; i++) {
            // 行的后面有位置加的下面，否则加到上面
            if (moseRowCol[0] + i <= gridSize[0]) {
              rowCols.push([moseRowCol[0] + i, moseRowCol[1]]);
            } else if (moseRowCol[0] - i > 0) {
              rowCols.unshift([moseRowCol[0] - i, moseRowCol[1]]);
            }
          }
        }
      }
    }
    return rowCols[0];
  };

  /**
   * 移动图形到grid的格子里
   * @param graph
   * @param row
   * @param col
   */
  const moveGraphToGrid = (graph: Graph, rowCol: number[]) => {
    const rowStart = rowCol[0];
    const colStart = rowCol[1];
    let rowEnd;
    let colEnd;

    if (graph.gridItem) {
      // 存在格子的，按格子占的格子数计算
      const rowSpan = graph.gridItem.rows[1] - graph.gridItem.rows[0];
      const colSpan = graph.gridItem.columns[1] - graph.gridItem.columns[0];

      rowEnd = rowStart + rowSpan;
      colEnd = colStart + colSpan;

      graph.gridItem.rows = [rowStart, rowEnd];
      graph.gridItem.columns = [colStart, colEnd];
    } else {
      // 从容器外部拖入grid布局的，默认就占一个
      rowEnd = rowCol[0] + 1;
      colEnd = rowCol[1] + 1;
      graph.gridItem = new GridItem([rowStart, rowEnd], [colStart, colEnd]);
    }

    nextTick(() => {
      resetGraphXYByDom([graph]);
    });
  };

  /**
   * 根据图形的大小计算图形columns占据格子位置
   * @param graph
   * @param frame
   * @param gridItemWidth 每个格子的宽度
   * @param position 1:end  -1: start
   */
  const gridItemColBySize = (graph: Graph, frame: Frame, gridItemWidth: number, position: number) => {
    const { width } = graph;
    const horizontalGap = frame.autoLayout.horizontalGap as number;
    let spanCol = (width + horizontalGap) / (gridItemWidth + horizontalGap);

    // 是否充满：1. spanCol为整数的时候，则占满格子
    //         2. spanCol为小数，小数的前两位为99,也算占满格子(由于计算有误差，不可能账号整除, n.99算误差)
    let colFill = false;
    if (!Number.isInteger(spanCol)) {
      const rem = spanCol.toString().split('.')[1].substring(0, 1);
      if (rem === '9') {
        spanCol = Math.round(spanCol);
        colFill = true;
      } else {
        spanCol = Math.floor(spanCol) === 0 ? 1 : Math.floor(spanCol);
      }
    } else {
      colFill = true;
    }

    if (graph.gridItem) {
      if (Number.isInteger(spanCol)) {
        if (position === 1) {
          graph.gridItem.columns[1] = graph.gridItem.columns[0] + spanCol;
        } else if (position === -1) {
          graph.gridItem.columns[0] = graph.gridItem.columns[1] - spanCol;
        }

        colFill && (graph.limitSize.width.resize = ResizeType.Fill);
      }
    }
  };

  /**
   * 根据图形的大小计算图形rows占据格子位置
   * @param graph
   * @param frame
   * @param gridItemHeight 每个格子的高度
   * @param position 1:end  -1: start
   */
  const gridItemRowBySize = (graph: Graph, frame: Frame, gridItemHeight: number, position: number) => {
    const { height } = graph;
    const verticalGap = frame.autoLayout.verticalGap as number;
    let spanRow = (height + verticalGap) / (gridItemHeight + verticalGap);

    // 是否充满：1.spanRow为整数的时候，则占满格子
    //          2.spanRow为小数，小数的前两位为99,也算占满格子(由于计算有误差，不可能账号整除, n.99算误差)
    let rowFill = false;

    if (!Number.isInteger(spanRow)) {
      const rem = spanRow.toString().split('.')[1].substring(0, 2);
      if (rem === '99') {
        spanRow = Math.round(spanRow);
        rowFill = true;
      } else {
        spanRow = Math.floor(spanRow) === 0 ? 1 : Math.floor(spanRow);
      }
    } else {
      rowFill = true;
    }

    if (graph.gridItem) {
      if (Number.isInteger(spanRow)) {
        if (position === 1) {
          graph.gridItem.rows[1] = graph.gridItem.rows[0] + spanRow;
        } else if (position === -1) {
          graph.gridItem.rows[0] = graph.gridItem.rows[1] - spanRow;
        }
        rowFill && (graph.limitSize.height.resize = ResizeType.Fill);
      }
    }
  };

  /**
   * 获取容器格子的宽高
   * @param frame
   * @returns
   */
  const getGridItemSize = (frame: Frame) => {
    const dom = document.querySelector(`[id="${frame.id}"] > .vis-frame-grid-ghost > div`) as HTMLElement;
    if (dom) {
      return [dom.offsetWidth, dom.offsetHeight];
    }
  };

  //#endregion

  return {
    findGraph,
    filterGraph,
    filterGraphs,
    findGraphParents,
    graphBrothers,
    moveGraphs,

    getCanvasXY,
    getCanvasPosSize,

    addFrame,
    addFrameBySize,
    addTextBox,
    activeGraphs,

    flattenFrames,
    flattenPageFrames,
    getFrameByPosition,
    resetGraphXYByDom,

    addBlockToPage,
    addGraphToPage,

    formatRotate,
    foramtRadius,

    delGraphs,

    handGridItemPosition,
    getRowColByPosition,
    getRowColByPositions,
    moveGraphToGrid,
    // gridItemBySize,
    gridItemRowBySize,
    gridItemColBySize,
    getGridItemSize
  };
};
