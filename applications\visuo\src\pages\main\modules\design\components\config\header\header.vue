<template>
  <div class="vis-config-header flex items-center justify-between">
    <div class="vis-config-header-avatar">
      <q-avatar size="24px" text-color="primary" class="bg-[#DAE1FF]">
        <q-icon name="o_person" size="16px" />
      </q-avatar>
      <q-icon name="o_expand_more" size="14px" />
    </div>
    <div class="vis-config-header-actions gap-1">
      <q-btn :ripple="false" flat class="!text-16px mr-2">
        <ht-icon class="vis-icon" name="hticon-vis-play" />
      </q-btn>
      <q-btn color="primary" class="!w-10"> 发布 </q-btn>
    </div>
  </div>
</template>

<script lang="ts" src="./header.ts"></script>
<style lang="scss" src="./header.scss" scoped></style>
