<template>
  <div class="ht-filter-query px-5 py-4">
    <div class="row q-gutter-x-md">
      <div class="col">
        <div class="q-field__label text-body2 q-mb-sm text-dark">最小值：</div>
        <q-input
          outlined
          dense
          v-model="rangeFilter.values[0]"
          :rules="numberRules"
          no-error-icon
          @blur="formatNumber(0)"
          v-if="isNumber"
        >
          <template v-slot:append>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[0] ? rangeFilter.rangeOperator[0] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(0, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
        <q-input v-if="isDateTime || isDate" outlined dense v-model="rangeFilter.values[0]">
          <template v-slot:prepend>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="rangeFilter.values[0]" :mask="format"> </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
          <template v-slot:append>
            <q-icon name="access_time" class="cursor-pointer" v-if="isDateTime">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-time v-model="values[0]" :mask="format" format24h> </q-time>
              </q-popup-proxy>
            </q-icon>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[0] ? rangeFilter.rangeOperator[0] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(0, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
        <q-input v-if="isTime" outlined dense v-model="rangeFilter.values[0]">
          <template v-slot:prepend>
            <q-icon name="access_time" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-time v-model="values[0]" :mask="format" format24h> </q-time>
              </q-popup-proxy>
            </q-icon>
          </template>
          <template v-slot:append>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[0] ? rangeFilter.rangeOperator[0] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(0, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
      </div>
      <div class="col">
        <div class="q-field__label text-body2 q-mb-sm text-dark">最大值：</div>
        <q-input
          outlined
          dense
          v-model="rangeFilter.values[1]"
          :rules="numberRules"
          @blur="formatNumber(1)"
          v-if="isNumber"
        >
          <template v-slot:append>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[1] ? rangeFilter.rangeOperator[1] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(1, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
        <q-input v-if="isDateTime || isDate" outlined dense v-model="rangeFilter.values[1]">
          <template v-slot:prepend>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="rangeFilter.values[1]" :mask="format"> </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
          <template v-slot:append>
            <q-icon name="access_time" class="cursor-pointer" v-if="isDateTime">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-time v-model="values[1]" :mask="format" format24h> </q-time>
              </q-popup-proxy>
            </q-icon>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[1] ? rangeFilter.rangeOperator[1] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(1, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
        <q-input v-if="isTime" outlined dense v-model="rangeFilter.values[1]">
          <template v-slot:prepend>
            <q-icon name="access_time" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-time v-model="values[1]" :mask="format" format24h> </q-time>
              </q-popup-proxy>
            </q-icon>
          </template>
          <template v-slot:append>
            <q-btn round dense flat :label="rangeFilter.rangeOperator[1] ? rangeFilter.rangeOperator[1] : '选择'">
              <q-menu>
                <q-list dense class="ht-analysis-list">
                  <q-item
                    clickable
                    v-close-popup
                    v-for="operator in operators"
                    :key="operator.value"
                    @click="onOperator(1, operator.value)"
                  >
                    <q-item-section>{{ operator.label }}</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-btn>
          </template>
        </q-input>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./range"></script>
