import { Effects, EffectsType } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';

export default defineComponent({
  name: 'vis-effects',
  props: {
    modelValue: {
      type: Object as PropType<Effects>,
      required: true
    },
    minusWidth: {
      type: Number,
      default: 28
    },
    isText: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const computedEffects = computed({
      get() {
        if (!props.modelValue) {
          return new Effects();
        }
        return props.modelValue;
      },
      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    const effectOptions = [
      { label: '外阴影', value: EffectsType.Outset, icon: 'shadow-out' },
      { label: '内阴影', value: EffectsType.Inset, icon: 'shadow-in' },
      { label: '高斯模糊', value: EffectsType.Blur, icon: 'blur' },
      { label: '背景模糊', value: EffectsType.BgBlur, icon: 'blur-bg' }
    ];

    const isOffset = computed(() => {
      return [EffectsType.Outset, EffectsType.Inset].includes(computedEffects.value.type);
    });
    const isBlur = computed(() => {
      return [EffectsType.Blur, EffectsType.BgBlur].includes(computedEffects.value.type);
    });

    const effectIcon = computed(() => {
      switch (computedEffects.value.type) {
        case EffectsType.Outset:
          return 'shadow-out';
        case EffectsType.Inset:
          return 'shadow-in';
        case EffectsType.Blur:
          return 'blur';
        case EffectsType.BgBlur:
          return 'blur-bg';
        default:
          return 'shadow-out';
      }
    });

    const visible = ref(props.modelValue?.visible);
    const handleVisible = () => {
      visible.value = !visible.value;
      handleUpdate(Object.assign({}, computedEffects.value, { visible: visible.value }));
      emit('toggle', visible.value);
    };

    const handleUpdate = (val: Effects) => {
      emit('update:modelValue', val);
    };

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    return {
      computedEffects,
      effectOptions,

      isOffset,
      isBlur,

      effectIcon,

      visible,
      handleVisible,

      popupRef,
      popupShow,
      showPopup
    };
  }
});
