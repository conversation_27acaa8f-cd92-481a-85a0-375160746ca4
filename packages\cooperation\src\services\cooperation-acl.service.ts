import { type ACLCanType, ACLService, type ACLType } from '@hetu/acl';
import type { CooperationACLDataType, CooperationACLType } from '../models';

/**
 * 协作权限服务类
 */
export class CooperationACLService {
  private static full = false;

  /** 协作权限组 */
  private static permissions: Record<string, CooperationACLDataType>;

  /** 按分类的权限组集合 */
  private static groups: Record<string, CooperationACLDataType[]>;

  static setPermissions(permissions: CooperationACLDataType[]) {
    this.permissions = {};
    this.groups = {};
    permissions.forEach((data) => {
      this.permissions[data.id] = data;
      const { privilegeGroup } = data;
      const group = this.groups[privilegeGroup] || [];
      group.push(data);
      this.groups[privilegeGroup] = group;
    });
  }

  /**
   * 获取协作权限组
   * @param id 权限组ID: 即协作身份ID
   */
  static get(id: string) {
    return this.permissions[id];
  }

  /**
   * 获取分类下的权限组集合
   * @param group 分类
   */
  static getByGroup(group: string) {
    return this.groups[group];
  }

  /**
   * 验证权限点
   * @param point 权限点
   */
  static allow(acl: CooperationACLType) {
    // 验证应用资源权限
    let auth = ACLService.can(acl);

    // 以协作身份验证资源权限
    const group = acl.group;
    if ((auth || acl.except) && group) {
      if (this.permissions) {
        auth = this.can(acl);
      } else {
        throw new Error('协作权限组未初始化');
      }
    }
    return auth;
  }

  /**
   * 当前用户是否有对应角色或资源
   *
   * - 当 `full: true` 或参数 `null` 时返回 `true`
   * - 若使用 `ACLType` 参数，可以指定 `mode` 校验模式
   */
  private static can(acl?: CooperationACLType | null): boolean {
    const t = this.parseACLType(acl);
    let result = false;
    if (this.full === true || !acl) {
      result = true;
    } else {
      const groupResources = this.permissions[acl.group]?.resources;

      if (groupResources && t.resource && Object.keys(t.resource).length) {
        const resources = t.resource;
        const groups = Object.keys(resources);
        const fn = (group: string) => {
          const resource = resources[group];
          if (typeof resource === 'string') {
            return groupResources[group]?.includes(resource);
          } else {
            if (t.mode === 'allOf') {
              return resource.every((v) => groupResources[group]?.includes(v));
            } else {
              return resource.some((v) => groupResources[group]?.includes(v));
            }
          }
        };
        if (t.mode === 'allOf') {
          result = groups.every(fn);
        } else {
          result = groups.some(fn);
        }
      }
    }

    return t.except === true ? !result : result;
  }

  private static parseACLType(val?: ACLCanType | null): CooperationACLType {
    let t: ACLType;
    if (typeof val === 'object' && !Array.isArray(val)) {
      t = { ...val };
    } else {
      t = { resource: {}, role: [] };
    }

    return { group: '', except: false, ...t };
  }
}
