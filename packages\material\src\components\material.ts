import { ref, defineComponent, type PropType, computed } from 'vue';
import { type Menu } from '@hetu/theme';
import { MaterialType } from '../models';
import HtMaterialMedia from './media/media.vue';
import HtMaterialGeo from './geo/geo.vue';
import { useDialogPluginComponent } from 'quasar';

export default defineComponent({
  name: 'ht-material',
  props: {
    modelValue: String,
    type: String as PropType<MaterialType>
  },
  emits: ['update:modelValue'],
  components: {
    HtMaterialMedia,
    HtMaterialGeo
  },
  setup(props, { emit }) {
    const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();
    const value = ref(props.modelValue ?? '');
    const onSure = () => {
      emit('update:modelValue', value.value);
      onDialogOK(value.value);
    };

    const materialType = MaterialType;
    const menus = [
      {
        id: MaterialType.Image,
        menuCode: MaterialType.Image,
        menuName: '图片',
        menuIcon: 'crop_original'
      },
      {
        id: MaterialType.Video,
        menuCode: MaterialType.Video,
        menuName: '视频',
        menuIcon: 'video_camera_back'
      },
      {
        id: MaterialType.Geo,
        menuCode: MaterialType.Geo,
        menuName: 'GeoJson',
        menuIcon: 'hticon-map-china'
      }
    ];

    const activeMenuId = ref(props.type || MaterialType.Image);
    /** 选中的菜单 */
    const activeMenu = computed(() => {
      return menus.find((menu) => menu.id === activeMenuId.value);
    });

    /**
     * 选中菜单事件
     * @param menu
     */
    const selectMenu = (menu: Menu) => {
      activeMenuId.value = menu.id as MaterialType;
    };

    return {
      dialogRef,
      onDialogCancel,
      onSure,

      value,

      materialType,
      menus,
      activeMenuId,
      activeMenu,
      selectMenu
    };
  }
});
