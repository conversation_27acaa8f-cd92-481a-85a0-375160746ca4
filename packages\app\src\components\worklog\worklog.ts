import { computed, defineComponent, reactive, ref, watch } from 'vue';
import { type Menu } from '@hetu/theme';
import { OrderType } from '@hetu/http';
import { AppService } from '@hetu/platform-shared';

import { OperateService } from '../../services';
import { type OperateLog, type OrderColumn } from '../../models';
import { useDomain } from '../../hooks';

export default defineComponent({
  name: 'ht-app-worklog',
  setup() {
    //#region 子菜单相关
    const allMenu: Menu[] = [
      {
        id: 'me',
        menuName: '我最近的修改',
        menuCode: 'me',
        menuIcon: 'hticon-appstore-o'
      },
      {
        id: 'team',
        menuName: '空间动态',
        menuCode: 'team',
        menuIcon: 'hticon-adduser'
      }
    ];
    const domain = useDomain();
    const actMenu: Menu = reactive({ ...allMenu[0] });
    watch(domain, () => {
      actMenu.menuCode = allMenu[0].menuCode;
      actMenu.menuName = allMenu[0].menuName;
      getOperates();
    });
    function selectMenu(menu: Menu) {
      actMenu.menuCode = menu.menuCode;
      actMenu.menuName = menu.menuName;
      getOperates();
    }
    //#endregion

    //#region 获取操作记录
    const loading = ref(false);
    let data: OperateLog[] = [];
    /** 获取接口记录并排序 */
    async function getOperates() {
      loading.value = true;
      try {
        const { appKey } = AppService;
        data =
          actMenu.menuCode === allMenu[0].menuCode
            ? await OperateService.operateUser(appKey)
            : await OperateService.operateTeam(appKey);
      } catch (error) {
        data = [];
      }
      // 排序data
      data.sort((a, b) => {
        const aT = dateToNumber(a.createdTime);
        const bT = dateToNumber(b.createdTime);
        return order.sorting === OrderType.Asc ? aT - bT : bT - aT;
      });
      formatOperates(data);
      loading.value = false;
    }
    function dateToNumber(date: string) {
      return Number(new Date(date).getTime() / 1000);
    }
    //#endregion

    //#region 搜索相关
    const search = ref('');
    /** 搜索方法 */
    function handleSearch() {
      const keyword = search.value.trim();
      if (keyword) {
        const newData = data.filter((item) => (item.objectName || '').toLowerCase().includes(keyword.toLowerCase()));
        formatOperates(newData);
      } else {
        formatOperates(data);
      }
    }
    //#endregion

    //#region 处理排序
    const order: OrderColumn = reactive({
      column: 'createdTime',
      sorting: OrderType.Desc
    });
    const sortColumns = {
      createdTime: '修改时间'
    };
    const sorting = ref(order.sorting);
    /** 排序方法 */
    function handleSort(order: OrderColumn) {
      if (order.sorting != sorting.value) {
        data.reverse();
        formatOperates(data);
      }
      sorting.value = order.sorting;
    }
    //#endregion

    //#region 处理操作记录（分组格式化）
    const empty = ref(true);
    const operates: Record<string, OperateLog[]> = reactive({});
    /** 格式化操作记录 */
    function formatOperates(ops: OperateLog[]) {
      empty.value = true;
      for (const key in operates) {
        delete operates[key];
      }
      ops.length &&
        ops.forEach((op) => {
          empty.value = false;
          const [date, time] = op.createdTime.split(' ');
          if (!operates[date]) {
            operates[date] = [];
          }
          op.time = time;
          operates[date].push(op);
        });
    }
    //#endregion

    getOperates();

    return {
      search,
      handleSearch,

      allMenu,
      actMenu,
      selectMenu,

      loading,
      empty,
      operates,

      order,
      sortColumns,
      handleSort
    };
  }
});
