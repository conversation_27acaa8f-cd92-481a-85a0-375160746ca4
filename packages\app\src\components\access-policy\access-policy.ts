import type { QTableColumn } from 'quasar';
import { ref, defineComponent } from 'vue';
import { useQuasar } from 'quasar';
import { AccessPolicyService } from '../../services';
import { type WhiteIP } from '../../models';

export default defineComponent({
  name: 'ht-access-policy',
  props: {
    /**主键id */
    dataId: {
      type: String,
      required: true
    },
    /**模块 */
    category: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    }
  },
  setup(props: any, { emit }: any) {
    const isModelOpen = ref<boolean>(true);
    const $q = useQuasar();
    const formatAuthorizationType = (number: number) => {
      let authorizationType = '';
      switch (number) {
        case 0:
          authorizationType = 'IPv4';
          break;
        case 1:
          authorizationType = 'IPv6';
          break;
        case 2:
          authorizationType = 'IPv4 网段';
          break;
        case 3:
          authorizationType = 'IPv6 网段';
          break;
      }
      return authorizationType;
    };

    const rows = ref<WhiteIP[]>([
      {
        id: '',
        authorizationType: 0,
        active: 1,
        dataId: '',
        dataType: props.category,
        authorizationContent: '',
        orderNo: 0
      }
    ]);

    const getRows = () => {
      AccessPolicyService.list(props.dataId, props.category).then((res) => {
        if (res && res.status === 'success') {
          rows.value = res.data;
        }
        if (!rows.value.length) {
          add();
        }
      });
    };

    getRows();

    const saveLoading = ref<boolean>(false);

    /**确认保存 */
    const onOKClick = () => {
      if (rows.value.filter((v) => !v.authorizationContent && v.active).length > 0) {
        $q.notify({
          message: '请输入地址！',
          position: 'top',
          type: 'negative'
        });
        return;
      }
      saveLoading.value = true;
      const data = rows.value.map((item) => ({ ...item, dataId: props.dataId }));
      AccessPolicyService.save(data, props.category)
        .then((res) => {
          if (res && res.status === 'success') {
            $q.notify({
              message: '操作成功',
              type: 'success'
            });
            onCancelClick();
          }
        })
        .finally(() => (saveLoading.value = false));
    };

    /**关闭弹窗 */
    const onCancelClick = () => {
      isModelOpen.value = false;
    };

    /**切换IP类型 */
    const onDropdownItemClick = (row: WhiteIP, number: number) => {
      row.authorizationType = number;
    };

    /**添加一行 */
    const add = () => {
      const item = {
        id: '',
        authorizationType: 0,
        active: 1,
        dataId: '',
        dataType: props.category,
        authorizationContent: '',
        orderNo: 0
      };
      rows.value.push(item);
    };

    /**删除一行 */
    const deleteRow = (index: number) => {
      if (rows.value.length <= 1) return;
      rows.value.splice(index, 1);
    };

    /**授权内容鼠标失去焦点，有内容输入且没有一条空模版数据，默认添加一行 */
    const authorizationContentBlur = (row: WhiteIP) => {
      // 有一条空的模版数据
      const hasTemplate = rows.value.find((el) => !el.authorizationContent);
      if (row.authorizationContent && !hasTemplate) {
        add();
      }
    };

    const columns = [
      { name: 'index', align: 'center', label: '', style: 'width: 30px' },
      {
        name: 'authorizationType',
        align: 'center',
        label: '授权类型',
        style: 'width: 80px'
      },
      { name: 'authorizationContent', label: 'IP', align: 'center', style: 'width: 450px' },
      { name: 'active', label: '启用', align: 'center', style: 'width: 80px' },
      {
        name: 'add',
        label: '+',
        align: 'center',
        style: 'width: 60px',
        type: 'button'
      }
    ] as QTableColumn[];

    return {
      rows,
      columns,
      onOKClick,
      isModelOpen,
      onDropdownItemClick,
      add,
      onCancelClick,
      deleteRow,
      formatAuthorizationType,
      authorizationContentBlur,

      saveLoading
    };
  }
});
