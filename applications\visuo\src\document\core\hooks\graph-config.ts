import type { Records } from '@hetu/util';
import { GraphConfig, GraphType, DirectionType } from '../models';

/**
 * 图形配置
 * @returns
 */
export const useGraphConfig = () => {
  const graphConfig: Records<GraphConfig> = Object.assign(
    {},
    new GraphConfig(GraphType.Frame, '容器', 'hticon-vis-frame').record,
    new GraphConfig(GraphType.TextBox, '文本', 'hticon-vis-textbox').record,
    new GraphConfig(DirectionType.Freeform, '容器', 'hticon-vis-frame').record,
    new GraphConfig(DirectionType.Horizontal, '水平布局容器', 'hticon-vis-layout-horizontal').record,
    new GraphConfig(DirectionType.Vertical, '垂直布局容器', 'hticon-vis-layout-vertical').record,
    new GraphConfig(DirectionType.Grid, '网格布局容器', 'hticon-vis-layout-grid').record
  );

  return { graphConfig };
};
