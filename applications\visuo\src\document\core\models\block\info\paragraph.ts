import { Text, Font } from '../../ui';
import { Link } from '../../ui/link';
import { WidgetName, WidgetType } from '../config/widget-enum';
import { WidgetBlock } from '../widget-block';

/**
 * 段落
 * <AUTHOR>
 */
export class Paragraph extends WidgetBlock {
  type = WidgetType.Paragraph;

  name = WidgetName.Paragraph;

  options: ParagraphOptions = new ParagraphOptions();
}

export class ParagraphOptions {
  /**
   * 文本内容
   */
  content: string =
    '太极河图是一个集设计、分析、运行于一体的数据可视化设计平台，该产品支持采用拖拽式自定义编排方式，实现各类交互式数据看板、数据大屏等多元化、炫酷的数据可视化页面设计，可以很好地满足政府、能源、水利、医疗、制造等各行业数据可视化业务开发需求。';

  /**
   * 链接
   */
  link: Link = new Link();

  /**
   * 滚动动画
   */
  animation: Animation = new Animation();
}

/**
 * 段落滚动动画
 */
export class Animation {
  /**
   * 是否启用动画
   */
  enable: boolean = false;

  /**
   * 是否循环滚动
   */
  loop: boolean = false;

  /**
   * 滚动速度
   */
  speed: number = 0;

  /**
   * 动画结束等待时间
   */
  wait: number = 0;
}
