<template>
  <q-dialog class="ht-app-access-policy" ref="dialogRef" v-model="isModelOpen">
    <q-card class="w-188 !max-w-full">
      <q-card-section class="flex items-center justify-between text-lg no-wrap">
        <span class="ellipsis">访问策略-{{ $props.title }}</span>
        <q-icon name="close" class="ht-link" @click="onCancelClick" />
      </q-card-section>
      <div class="q-ml-md q-mr-md">
        <q-table
          class="ht-app-access-policy-table"
          table-class="border-1 border-solid border-grey-extra-light h-300px"
          :rows="rows"
          :columns="columns"
          :rows-per-page-options="[0]"
          flat
          row-key="id"
          dense
          hide-bottom
        >
          <template v-slot:header-cell-add="props">
            <q-th :props="props">
              <label class="ht-link">
                <q-icon name="add" color="primary" size="xs" @click="add"></q-icon>
              </label>
            </q-th>
          </template>
          <template v-slot:body="props">
            <q-tr :props="props" no-hover bordered>
              <q-td key="index" :props="props">
                {{ props.pageIndex + 1 }}
              </q-td>
              <q-td key="authorizationType" :props="props">
                <q-btn-dropdown
                  color="transparent"
                  text-color="dark"
                  flat
                  :label="formatAuthorizationType(props.row.authorizationType)"
                >
                  <q-list>
                    <q-item clickable v-close-popup @click="onDropdownItemClick(props.row, 0)">
                      <q-item-section>
                        <q-item-label>IPv4</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item clickable v-close-popup @click="onDropdownItemClick(props.row, 1)">
                      <q-item-section>
                        <q-item-label>IPv6</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-btn-dropdown>
              </q-td>
              <q-td key="authorizationContent" :props="props">
                <q-input
                  outlined
                  v-model="props.row.authorizationContent"
                  class="ht-field--small"
                  placeholder="例如: 2001:0DB8::1428:57ab/128,添加地址时支持多组对象，用“;”隔开"
                  @blur="authorizationContentBlur(props.row)"
                />
              </q-td>
              <q-td key="active" :props="props">
                <q-toggle size="xs" v-model="props.row.active" :true-value="1" :false-value="0" val="start" />
              </q-td>
              <q-td key="add" :props="props">
                <label class="ht-link">
                  <q-icon
                    class="text-negative"
                    name="delete_outline"
                    size="xs"
                    :class="{ disabled: rows.length <= 1 }"
                    @click="deleteRow(props.pageIndex)"
                  />
                </label>
              </q-td>
            </q-tr>
          </template>
        </q-table>
      </div>
      <q-card-section class="q-gutter-x-md text-right">
        <q-btn class="ht-btn--outline" unelevated @click="onCancelClick">取 消</q-btn>
        <q-btn color="primary" unelevated :loading="saveLoading" @click="onOKClick">确 定</q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script src="./access-policy.ts" lang="ts"></script>
<style src="./access-policy.scss" lang="scss"></style>
