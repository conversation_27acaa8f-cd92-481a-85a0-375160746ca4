import { Material, MaterialApi } from '../models';
import { HttpApiService, responseData, responseReject, responseResult, type ResponseResult } from '@hetu/http';
import { ImageACLResource, VideoACLResource, type MaterialFile } from '../models';
import { type FileData } from '@hetu/platform-app';
import { createSingleClass } from '@hetu/core';

/**
 * 素材库服务类
 */
export class MaterialServiceCtor extends HttpApiService<MaterialApi> {
  httpApi = new MaterialApi();

  httpModuleKey = 'material';

  imageAcl = new ImageACLResource();

  videoAcl = new VideoACLResource();

  /**
   * 获取列表
   * @param materialType 主题类型，图片：image，视频 video
   */
  getList(materialType: string): Promise<FileData<MaterialFile>> {
    return this.http.get(`${this.api.list}/${materialType}/list`).then(responseData, responseReject);
  }

  /**
   * 保存
   * @param Material
   */
  save(material: Material): Promise<ResponseResult | null> {
    return this.http.post(this.api.save, material).then(responseResult, responseReject);
  }
}

export const MaterialService = createSingleClass(MaterialServiceCtor);
