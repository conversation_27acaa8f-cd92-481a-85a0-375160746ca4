import type { HttpRequestInterceptor, HttpRequestConfig } from '@hetu/http';
import { APP_CODE_PLATFORM } from '../models';
import { useAppContext } from '@hetu/core';
import { inject } from 'vue';

/** 请求中的应用标识参数名 */
const HTTP_APP_CODE_KEY = 'appCode';

/** 请求中的应用标识 */
export const TOKEN_HTTP_APP_CODE = Symbol('AppCode');

/**
 * `Http`请求拦截器: 用于添加应用标识
 * <AUTHOR>
 */
export class HttpAppCodeInterceptor implements HttpRequestInterceptor {
  private get appCode() {
    return useAppContext(() => inject<string | undefined>(TOKEN_HTTP_APP_CODE));
  }

  intercept(config: HttpRequestConfig) {
    const url = config.url as string;
    if (!url.startsWith('./static')) {
      config.headers[HTTP_APP_CODE_KEY] = this.appCode ?? APP_CODE_PLATFORM;
    }
    return config;
  }
}
