<template>
  <vue-selecto
    ref="selectoRef"
    dragContainer=".vis-design-infinite-canvas"
    :className="className"
    :selectableTargets="['.vis-graph', '.vis-grid']"
    :hitRate="0"
    :selectByClick="true"
    :selectFromInside="isSelectFromInside"
    :checkInput="true"
    :ratio="0"
    :dragCondition="dragCondition"
    :scrollOptions="selectoScrollOptions"
    :preventDefault="true"
    @scroll="onSelectoScroll"
    @dragStart="onSelectoDragStart"
    @dragEnd="onSelectoDragEnd"
    @selectStart="onSelectToStart"
    @selectEnd="onSelectToEnd"
  ></vue-selecto>
</template>

<script lang="ts" src="./selecto.ts"></script>
<style lang="scss" src="./selecto.scss"></style>
