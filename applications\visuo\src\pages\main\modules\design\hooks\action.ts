import { computed } from 'vue';
import { useActionStore, useDesignStore } from '../stores';
import type { RectInfo } from 'vue3-moveable';
import { useGraph } from './graph';
import { Block, useDocumentStore, type Graph } from '@vis/document-core';

/**
 * 设计器画布操作方法
 * <AUTHOR>
 */
export const useAction = () => {
  const actionStore = useActionStore();
  const docStore = useDocumentStore();
  const designStore = useDesignStore();

  const actions = computed(() => actionStore.actions.value);

  const doc = computed(() => docStore.document.value);

  const { findGraph, findGraphParents, activeGraphs } = useGraph();

  const move = () => {
    resetGroupActive();
    actions.value.move.active = true;
  };
  const hand = () => {
    resetGroupActive();
    actions.value.hand.active = true;
  };

  const frame = () => {
    resetGroupActive();
    actions.value.frame.active = true;
  };

  const textbox = () => {
    resetGroupActive();
    actions.value.textbox.active = true;
  };

  const dhyana = () => {
    //
    designStore.moveableRef.value?.request('originDraggable', { origin: [100, 0] }, true);
  };

  /**
   * 还原同组操作状态
   * @param group
   */
  const resetGroupActive = (group: string = 'mouse') => {
    Object.values(actions.value).forEach((ele) => {
      if (ele.group === group) {
        ele.active = false;
      }
    });
  };

  /**
   * 对齐
   * @param type
   */
  const align = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const rect = moveableRef.getRect();
      const moveables = moveableRef.getMoveables();
      if (moveables.length <= 1 || activeGraphs.length <= 1) {
        alignGraph(type);
        return;
      }

      const leftArray: Array<number> = [];
      const rightArray: Array<number> = [];
      const topArray: Array<number> = [];
      const bottomArray: Array<number> = [];

      let allWidth = 0;
      let allHeight = 0;
      let activeLength = 0;
      rect.children?.forEach(({ left, top, width, height }: RectInfo) => {
        leftArray.push(left);
        rightArray.push(left + width);
        topArray.push(left);
        bottomArray.push(top + height);
        allWidth += width;
        allHeight += height;
        activeLength++;
      });

      switch (type) {
        case 'left':
          moveables.forEach((children) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const x = Math.round(rect.left);
              graph.transform.translate[0] = x;
            }
          });
          break;
        case 'right':
          moveables.forEach((children, i) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const x = Math.round(Math.max(...rightArray) - rect.children![i].width);
              graph.transform.translate[0] = x;
            }
          });
          break;
        case 'top':
          moveables.forEach((children) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const y = Math.round(rect.top);
              graph.transform.translate[1] = y;
            }
          });
          break;
        case 'bottom':
          moveables.forEach((children, i) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const y = Math.round(Math.max(...bottomArray) - rect.children![i].height);
              graph.transform.translate[1] = y;
            }
          });
          break;
        case 'center':
          moveables.forEach((children, i) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const x = Math.round(rect.left + rect.width / 2 - rect.children![i].width / 2);
              graph.transform.translate[0] = x;
            }
          });
          break;
        case 'middle':
          moveables.forEach((children, i) => {
            const id = children.props.target?.id;
            const graph = activeGraphs.find((g) => g.id === id);
            if (graph) {
              const y = Math.round(rect.top + rect.height / 2 - rect.children![i].height / 2);
              graph.transform.translate[1] = y;
            }
          });
          break;
        case 'horizontalSpace':
          {
            let left = rect.left;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.width -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.width;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.left - b.state.left;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pLeft = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pLeft = parents.reduce((sum, p) => {
                return sum + p.transform.translate[0];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const x = Math.round(isParent ? left - pLeft : left);
                graph.transform.translate[0] = x;
              }
              left += rect.width + gap;
            });
          }
          break;
        case 'verticalSpace':
          {
            let top = rect.top;

            if (moveables.length <= 1) {
              return;
            }
            const gap =
              (rect.height -
                rect.children!.reduce((prev, cur) => {
                  return prev + cur.height;
                }, 0)) /
              (moveables.length - 1);

            moveables.sort((a, b) => {
              return a.state.top - b.state.top;
            });

            // rect.left是基于最外层的位置，如果在容器内，需要减去容器的x
            const isParent = activeGraphs[0].parent;
            let pTop = 0;
            if (isParent) {
              const parents = findGraphParents(activeGraphs[0].id);
              pTop = parents.reduce((sum, p) => {
                return sum + p.transform.translate[1];
              }, 0);
            }

            moveables.forEach((children) => {
              const rect = children.getRect();
              const id = children.props.target?.id;
              const graph = activeGraphs.find((g) => g.id === id);
              if (graph) {
                const y = Math.round(isParent ? top - pTop : top);
                graph.transform.translate[1] = y;
              }

              top += rect.height + gap;
            });
          }
          break;
      }
    }
  };

  /**
   * 单图形对齐
   * @param type 对齐方式
   * @param graph 图形
   * @param {
   * pw: 父组件宽度
   * ph: 父组件高度
   * }
   */
  const singleAlign = (type: string, graph: Graph, { pw, ph }: { pw: number; ph: number }) => {
    switch (type) {
      case 'left':
        graph.transform.translate[0] = 0;
        break;
      case 'right':
        graph.transform.translate[0] = pw - graph.width;
        break;
      case 'top':
        graph.transform.translate[1] = 0;
        break;
      case 'bottom':
        graph.transform.translate[1] = ph - graph.height;
        break;
      case 'center':
        graph.transform.translate[0] = Math.round((pw - graph.width) / 2);
        break;
      case 'middle':
        graph.transform.translate[1] = Math.round((ph - graph.height) / 2);
        break;
    }
  };

  const multipleAlign = (type: string, graphs: Graph[]) => {
    const leftArray: Array<number> = [];
    const rightArray: Array<number> = [];
    const topArray: Array<number> = [];
    const bottomArray: Array<number> = [];

    let allWidth = 0;
    let allHeight = 0;
    let activeLength = 0;
    graphs.forEach((g) => {
      const left = g.transform.translate[0];
      const top = g.transform.translate[1];
      leftArray.push(left);
      rightArray.push(left + g.width);
      topArray.push(left);
      bottomArray.push(top + g.height);
      allWidth += g.width;
      allHeight += g.height;
      activeLength++;
    });

    switch (type) {
      case 'left':
        graphs.forEach((g) => {
          const x = Math.min(...leftArray);
          g.transform.translate[0] = x;
        });
        break;
      case 'right':
        graphs.forEach((g, i) => {
          const x = Math.max(...rightArray) - g.width;
          g.transform.translate[0] = x;
        });
        break;
      case 'top':
        graphs.forEach((g) => {
          const y = Math.min(...topArray);
          g.transform.translate[1] = y;
        });
        break;
      case 'bottom':
        graphs.forEach((g) => {
          const y = Math.max(...bottomArray) - g.height;
          g.transform.translate[1] = y;
        });
        break;
      case 'center':
        {
          const centerX = (Math.max(...rightArray) - Math.min(...leftArray)) / 2 + Math.min(...leftArray);
          graphs.forEach((g) => {
            const x = Math.round(centerX - g.width / 2);
            g.transform.translate[0] = x;
          });
        }
        break;
      case 'middle':
        {
          const centerY = (Math.max(...bottomArray) - Math.min(...topArray)) / 2 + Math.min(...topArray);
          graphs.forEach((g) => {
            const y = Math.round(centerY - (g.height as number) / 2);
            g.transform.translate[1] = y;
          });
        }
        break;
    }
  };

  /**
   * 选中单个图形对齐
   * 规则：1. 选中图形有父元素，对齐是基于父元素的
   *      2. 选中图形无父元素，对齐的是图形的子元素
   *      3. 选中图形无父元素、无子元素，不对齐
   */
  const alignGraph = (type: string) => {
    const moveableRef = designStore.moveableRef.value;
    const activeGraphs = designStore.active.value.graphs;
    if (moveableRef) {
      const moveables = moveableRef.getMoveables();
      if (moveables.length == 1 && activeGraphs.length == 1) {
        const graph = activeGraphs[0];
        if (graph.parent) {
          // 选中图形有父元素，对齐是基于父元素的
          const parent = findGraph(graph.parent);
          if (parent) {
            const pw = parent?.width || 0;
            const ph = parent?.height || 0;
            singleAlign(type, graph, { pw, ph });
          }
        } else {
          // 选中图形无父元素，对齐的是图形的子元素
          const len = graph.children?.length || 0;
          if (graph.children?.length) {
            if (len === 1) {
              // 只有一个图形时，基于选中图形对齐
              singleAlign(type, graph.children[0], { pw: graph.width, ph: graph.height });
            } else {
              // 多个图形时，多图形对齐
              multipleAlign(type, graph.children);
            }
          }
        }

        //moveableRef.updateRect();
      }
    }
  };

  /**
   * 删除组件
   */
  const del = () => {
    const graphs = designStore.active.value.graphs;
    if (!graphs.length) {
      return;
    }
    let parent: any;
    let parentChildren: Graph[] = [];

    if (!graphs[0].parent) {
      parentChildren = designStore.active.value.page.children;
    } else {
      parent = findGraph(graphs[0].parent);
      parentChildren = parent.children;
    }
    graphs.forEach((graph) => {
      if ((graph as Block).decoration) {
        const cIndex = doc.value.blocks.findIndex((b) => b.id === (graph as Block).decoration);
        doc.value.blocks.splice(cIndex, 1);
      }
      const index = parentChildren.findIndex((g) => g.id === graph.id);
      parentChildren.splice(index, 1);
    });

    activeGraphs();
  };

  return {
    move,
    hand,
    frame,
    textbox,

    dhyana,

    align,

    del
  };
};
