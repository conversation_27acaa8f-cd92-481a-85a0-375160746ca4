/**
 * 容器
 * <AUTHOR>
 */
export const useFrame = () => {
  // 预制尺寸
  const prepareSize = {
    web: {
      text: '大屏',
      icon: 'desktop_windows',
      size: [
        {
          text: 'FHD 16:9',
          width: 1920,
          height: 1080
        },
        {
          text: '4K 16:9',
          width: 3840,
          height: 2160
        },
        {
          text: '2K 16:9',
          width: 2560,
          height: 1440
        },
        {
          text: '超宽屏 21:9',
          width: 2560,
          height: 1080
        }
      ]
    },
    desktop: {
      text: '桌面',
      icon: 'laptop_mac',
      size: [
        {
          text: 'Desktop',
          width: 1440,
          height: 1024
        },
        {
          text: 'MacBook Air 13',
          width: 1280,
          height: 800
        },
        {
          text: 'MacBook Air 14',
          width: 1512,
          height: 982
        },
        {
          text: 'MacBook Air 16',
          width: 1728,
          height: 1117
        },
        {
          text: 'iMac',
          width: 1280,
          height: 720
        }
      ]
    },
    tablet: {
      text: '平板',
      icon: 'tablet_mac',
      size: [
        {
          text: 'Android Expanded"',
          width: 1280,
          height: 800
        },
        {
          text: 'iPad 10.2"',
          width: 810,
          height: 1080
        },
        {
          text: 'iPad mini 8.3"',
          width: 744,
          height: 1133
        },
        {
          text: 'iPad Air 10.9"',
          width: 820,
          height: 1180
        },
        {
          text: 'iPad Pro 11"',
          width: 834,
          height: 1194
        },
        {
          text: 'iPad Pro 12.9"',
          width: 1024,
          height: 1366
        }
      ]
    },
    mobile: {
      text: '手机',
      icon: 'phone_iphone',
      size: [
        {
          text: 'Android Compact',
          width: 412,
          height: 917
        },
        {
          text: 'Android Medium',
          width: 700,
          height: 840
        },
        {
          text: 'iPhone 16',
          width: 393,
          height: 852
        },
        {
          text: 'iPhone 16 Pro',
          width: 402,
          height: 874
        },
        {
          text: 'iPhone 16 Pro Max',
          width: 440,
          height: 956
        },
        {
          text: 'iPhone 16 Plus',
          width: 430,
          height: 932
        },
        {
          text: 'iPhone 14 & 15 Pro Max',
          width: 430,
          height: 932
        },
        {
          text: 'iPhone 14 Plus',
          width: 428,
          height: 926
        },
        {
          text: 'iPhone 14 & 15 Pro',
          width: 393,
          height: 852
        },
        {
          text: 'iPhone 13 & 14',
          width: 390,
          height: 844
        },
        {
          text: 'iPhone 13 mini',
          width: 375,
          height: 812
        },
        {
          text: 'iPhone SE',
          width: 320,
          height: 568
        },
        {
          text: 'iPhone 8 Plus',
          width: 414,
          height: 736
        },
        {
          text: 'iPhone 8',
          width: 375,
          height: 667
        }
      ]
    }
  };
  return {
    prepareSize
  };
};
