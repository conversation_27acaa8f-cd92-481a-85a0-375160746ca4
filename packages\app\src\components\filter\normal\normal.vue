<template>
  <div class="ht-filter-normal px-5 py-4">
    <div class="row items-center q-pb-md">
      <div class="col col-9">
        <q-input
          outlined
          dense
          v-model="search"
          clearable
          placeholder="请输入要搜索的值或添加的文本"
          :rules="inputRules"
          no-error-icon
          @keyup.enter="onAddValue"
        >
        </q-input>
      </div>
      <div class="col col-3">
        <q-btn unelevated class="ml-2" label="添加" color="primary" @click="onAddValue" />
        <q-btn label="清除" flat class="ml-2" @click="onClearAll" />
      </div>
    </div>
    <div class="row q-gutter-x-md">
      <div class="col border border-solid border-slate-200 h-64">
        <q-scroll-area class="h-full" @scroll="handleScroll">
          <div class="p-3">
            <q-checkbox
              size="xs"
              v-model="normalfilter.values"
              v-for="(option, index) in options"
              :key="index"
              :val="option.columnValue"
              :label="option.columnValue.toString()"
              class="!flex"
            />
            <q-inner-loading :showing="loading"> </q-inner-loading>
          </div>
        </q-scroll-area>
      </div>
      <div class="col border border-solid border-slate-200 h-64">
        <q-scroll-area class="h-full">
          <div class="p-3">
            <q-checkbox
              size="xs"
              v-model="normalfilter.values"
              v-for="(value, index) in normalfilter.values"
              :key="index"
              :val="value"
              :label="value.toString()"
              class="!flex"
            />
          </div>
        </q-scroll-area>
      </div>
    </div>
    <div class="row">
      <div class="offset-6 w-full">
        <div class="flex justify-between w-full">
          <q-checkbox
            v-model="normalfilter.includeAll"
            label="选定值为空时包含全部数据"
            :true-value="1"
            :false-value="0"
            size="xs"
          />
          <q-checkbox v-model="normalfilter.include" label="排除选定值" :true-value="0" :false-value="1" size="xs" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./normal"></script>
