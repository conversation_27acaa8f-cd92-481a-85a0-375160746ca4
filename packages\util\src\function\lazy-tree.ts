import { ref, type Ref } from 'vue';
import { TREE_NODE_CHILDREN_KEY, TREE_NODE_ID_KEY, TREE_NODE_LABEL_KEY, TREE_NODE_PARENT_KEY } from './tree';

export type LazyTreeNode<T extends Object> = LazyTreeNodeInstance<T> & T;

/**
 * 树节点实例
 * @param data 节点数据
 * @param childrenKey 子元素 key
 * @param loaded 子节点是否懒加载完成
 */
class LazyTreeNodeInstance<T extends Object> {
  lazyChildren?: LazyTreeNode<T>[];

  /**
   * 是否是懒加载节点
   * - 当开启懒加载且下级非空时为 `true`
   * - `quasar` 中使用
   */
  lazy = false;

  /** 缓存子节点 */
  lazyChildrenMap: Record<string, boolean> = {};

  constructor(data: T, childrenKey: keyof T, loaded: () => boolean, getParentKey: (node: T) => keyof T) {
    Object.assign(this, data);

    Object.keys(data).forEach((key) => {
      const val = data[key as keyof T];
      if (key !== childrenKey) {
        Object.defineProperty(this, key, {
          configurable: true,
          get: () => data[key as keyof T],
          set: (value: typeof val) => (data[key as keyof T] = value)
        });
      }
    });

    const parentKey = getParentKey(data);
    if (parentKey !== TREE_NODE_PARENT_KEY) {
      Object.defineProperty(this, parentKey, {
        get: () => data[parentKey]
      });
    }

    Object.defineProperty(this, childrenKey, {
      get: () => (this.lazy ? (loaded() ? this.lazyChildren : []) : this.lazyChildren),
      set: (value: LazyTreeNode<T>[]) => {
        this.lazyChildren = value;
      }
    });
  }
}

interface LazyTreeParams<T extends Object> {
  /** 是否开启懒加载 */
  enableLazy: boolean;
  /** 保留有父级ID但无对应父级数据的记录 */
  keepUnParent: boolean;
  /** id key, 默认值 `id` */
  idKey: keyof T;
  /** 显示文本 的 key, 此处用于搜索, 默认值 `title` */
  labelKey: keyof T;
  /** 指向父级 id 的 key, 默认值 `parentId` */
  parentKey: keyof T;
  /** 子元素 key, 默认值 `children` */
  childrenKey: keyof T;

  /** 初始化节点懒加载状态 */
  initNodeLazyFn?: (node: LazyTreeNode<T>) => boolean;

  /** 获取父级Key */
  getParentKey: (node: T) => keyof T;
}

/**
 * 前端懒加载树
 * @param data 数据集合
 * @param params 参数
 */
export class LazyTree<T extends Record<string, any>> {
  private origin!: T[];

  private originMap!: Map<T[keyof T], T>;

  /**
   * 缓存已展开的父级节点
   * - 由于 `quasar` 中树节点懒加载完成后会缓存状态, 所以在页面中展开过节点后需在此记录避免搜索后节点状态异常
   * - 同 `QTree.lazy` 数据
   */
  private lazy = {} as Record<T[keyof T], 'loaded'>;

  private params = {
    enableLazy: true,
    keepUnParent: true,
    idKey: TREE_NODE_ID_KEY as keyof T,
    labelKey: TREE_NODE_LABEL_KEY as keyof T,
    parentKey: TREE_NODE_PARENT_KEY as keyof T,
    childrenKey: TREE_NODE_CHILDREN_KEY as keyof T,
    getParentKey: () => this.params.parentKey
  } as LazyTreeParams<T>;

  private map!: Map<T[keyof T], LazyTreeNode<T>>;

  /** tree data */
  readonly data = ref<LazyTreeNode<T>[]>([]) as Ref<LazyTreeNode<T>[]>;

  constructor(data: T[], params?: Partial<LazyTreeParams<T>>) {
    params && Object.assign(this.params, params);

    this.setData(data);
  }

  /**
   * 设置数据
   * @param data 数据集合
   */
  setData(data: T[]) {
    this.origin = data;
    this.originMap = new Map();
    this.map = new Map();
    this.data.value = this.convert(data, this.map);
    return this.data;
  }

  /**
   * 根据唯一标识查找节点的原始对象
   * @param key `idKey` 对应字段的值
   */
  getItem(key: T[keyof T]) {
    return this.originMap.get(key);
  }

  /**
   * 根据唯一标识查找节点对象
   * @param key `idKey` 对应字段的值
   */
  getNode(key: T[keyof T]) {
    return this.map.get(key);
  }

  addNode(item: T, index?: number) {
    const node = this.createNode(item);

    const { idKey, getParentKey, keepUnParent } = this.params;
    const parentKey = getParentKey(node);
    const id = item[idKey];
    const parentId = item[parentKey];

    const push = (arr: LazyTreeNode<T>[]) => {
      index !== undefined && index !== -1 ? arr.splice(index, 0, node) : arr.push(node);
    };

    if (parentId) {
      const parent = this.map.get(parentId);
      if (!parent) {
        keepUnParent && push(this.data.value);
      } else {
        parent.lazyChildren = parent.lazyChildren || ([] as LazyTreeNode<T>[keyof T]);
        if (parent.lazyChildrenMap[id]) return;
        parent.lazyChildrenMap[id] = true;

        push(parent.lazyChildren!);

        this.map.set(parentId, parent);
      }
    } else {
      push(this.data.value);
    }

    this.origin.push(item);
    this.originMap.set(id, item);
    this.map.set(id, node);
  }

  removeNode(key: T[keyof T]) {
    const { idKey, getParentKey, childrenKey } = this.params;

    const remove = (_key: T[keyof T]) => {
      const node = this.map.get(_key);
      if (!node) return;

      node[childrenKey] && node[childrenKey].forEach((item: T) => remove(item[idKey]));

      this.map.delete(_key);
      this.originMap.delete(_key);
      this.origin.splice(
        this.origin.findIndex((item) => item[idKey] === _key),
        1
      );

      return node;
    };

    const node = remove(key);
    if (!node) return;

    const parentKey = getParentKey(node);

    const parent = this.map.get(node[parentKey]);
    const arr = (parent ? parent[childrenKey] : this.data.value) as T[];
    const index = arr?.findIndex((n: T) => n[idKey] === key);
    index > -1 && arr.splice(index, 1);
    parent?.lazyChildrenMap && delete parent.lazyChildrenMap[key];

    return index;
  }

  replaceNode(item: T, oldKey?: T[keyof T]) {
    const { idKey } = this.params;
    if (oldKey) {
      const node = this.getNode(oldKey);
      node && this.map.set(item[idKey], node);
      this.map.delete(oldKey);

      this.originMap.delete(oldKey);
      this.originMap.set(item[idKey], item);
      return;
    }

    const index = this.removeNode(item[idKey]);
    this.addNode(item, index);
  }

  /**
   * 根据唯一标识查找所有上级节点唯一标识
   * @param key `idKey` 对应字段的值
   */
  getParentKeys(key: T[keyof T]) {
    const { getParentKey } = this.params;

    const keys: T[keyof T][] = [];
    let node = this.getNode(key);
    if (!node) return keys;
    let parentKey = getParentKey(node);
    while (node && node[parentKey]) {
      keys.push(node[parentKey]);
      node = this.getNode(node[parentKey]);
      node && (parentKey = getParentKey(node));
    }
    return keys;
  }

  /**
   * 搜索
   * @param text 搜索文本, 为空时返回全部
   * @param filter
   *  + Array: 属性名(默认为 `labelKey 对应属性`)
   *  + Function: 自定义过滤方法
   * @param keepParent 是否保留匹配节点的所有上级节点, 默认值 `true`
   */
  search(
    text: string,
    filter: Array<keyof T> | ((item: T, index: number) => boolean) = [this.params.labelKey],
    keepParent = true
  ) {
    if (!text) {
      return this.data.value;
    }

    const { idKey } = this.params;
    const parents: T[keyof T][] = [];
    const data = this.origin.filter((item, index) => {
      const result = Array.isArray(filter)
        ? !!(filter as Array<keyof T>).find((attr) =>
            (item[attr] as string)?.toLowerCase().includes(text.toLowerCase())
          )
        : filter(item, index);

      if (keepParent && result) {
        parents.push(...this.getParentKeys(item[idKey]));
      }
      return result ? item : false;
    });

    keepParent &&
      parents.length &&
      new Set(parents).forEach((key) => {
        const parent = this.originMap.get(key);
        parent && !data.includes(parent) && data.push(parent);
      });

    return this.convert(data);
  }

  /**
   * 设置展开的父级节点
   * @param lazy 展开的父级节点, 此数据应与 `QTree.lazy` 数据保持相同
   */
  setLazy(lazy: Record<T[keyof T], 'loaded'>) {
    this.lazy = lazy;
  }

  /**
   * 懒加载下级
   * @param node 节点数据
   * @param key 唯一标识, 默认为 `idKey` 对应字段值
   * @param done 完成后回调
   */
  lazyLoad(node: LazyTreeNode<T>, key: T[keyof T], done?: (children: LazyTreeNode<T>[]) => void) {
    this.lazy[key] = 'loaded';
    node.lazy = false;
    done && done(node[this.params.childrenKey]);
  }

  private createNode(item: T) {
    const { idKey, childrenKey, enableLazy } = this.params;
    const id = item[idKey];

    !this.originMap.has(id) && this.originMap.set(id, item);

    // 树节点
    const node = new LazyTreeNodeInstance(
      item,
      childrenKey,
      () => !!this.lazy[id],
      this.params.getParentKey
    ) as LazyTreeNode<T>;

    if (enableLazy && this.params.initNodeLazyFn) {
      node.lazy = this.params.initNodeLazyFn(node);
    }

    return node;
  }

  private convert(data: T[], map: Map<T[keyof T], LazyTreeNode<T>> = new Map()) {
    let treeData: LazyTreeNode<T>[] = [];

    if (!data.length) {
      return treeData;
    }
    const { idKey, getParentKey, keepUnParent, enableLazy } = this.params;
    const unReadyParent = new Map<T[keyof T], number[]>();

    data.forEach((item) => {
      // 树节点
      const node = this.createNode(item);

      const parentKey = getParentKey(node);

      const id = item[idKey];
      const parentId = item[parentKey];

      if (map.get(id)) {
        // 由下级添加的不完整父级数据
        map.set(id, Object.assign(node, map.get(id)));

        if (keepUnParent && unReadyParent.has(id)) {
          const indexs = unReadyParent.get(id) as number[];
          indexs.forEach((i) => {
            treeData[i] = undefined as unknown as LazyTreeNode<T>;
          });
          unReadyParent.delete(id);
        }
      } else {
        map.set(id, node);
      }

      if (parentId) {
        if (keepUnParent) {
          const unReady = unReadyParent.has(parentId) || !map.get(parentId);
          if (unReady) {
            const index = treeData.push(node);
            const indexs = unReadyParent.get(parentId) || [];
            indexs.push(index - 1);
            unReadyParent.set(parentId, indexs);
          }
        }

        const parent = map.get(parentId) || ({} as LazyTreeNode<T>);
        enableLazy && (parent.lazy = !this.lazy[parentId]);
        parent.lazyChildren = parent.lazyChildren || ([] as LazyTreeNode<T>[keyof T]);
        parent.lazyChildren?.push(node);
        parent.lazyChildrenMap = parent.lazyChildrenMap || {};
        parent.lazyChildrenMap[id] = true;
        map.set(parentId, parent);
      } else {
        treeData.push(node);
      }
    });

    if (keepUnParent) {
      treeData = treeData.filter((node) => !!node);
    }

    return treeData;
  }
}
