<template>
  <div
    class="ht-app-file"
    :class="{
      'ht-app-file--dense': dense,
      'is-hover': hoverId === file.id,
      'is-active': isActive
    }"
    @click.stop="onClick"
    @mouseenter="setHover(file)"
    @mouseleave="setHover()"
  >
    <div class="ht-app-file__body text-font-secondary" @click="onClickBody">
      <slot>
        <ht-icon
          v-if="icon?.startsWith('hticon-')"
          :class="[
            'ht-app-file__icon',
            iconColor ? (iconColor.startsWith('text-') ? iconColor : 'text-' + iconColor) : 'text-font-placeholder'
          ]"
          :name="icon?.replace('hticon-', '')"
        />
        <q-icon
          v-else
          :class="[
            'ht-app-file__icon q-mx-md',
            iconColor ? (iconColor.startsWith('text-') ? iconColor : 'text-' + iconColor) : 'text-font-placeholder'
          ]"
          :name="icon"
        />
      </slot>
      <ht-icon
        v-if="favorite"
        :name="favoriteing ? '' : 'favorite' + (file.favorite ? '-fill' : '')"
        :class="['is-current-show absolute-top-left q-ma-xs', file.favorite && 'is-show text-favorite']"
        @click.stop="toggleFavorite"
      >
        <q-spinner-ios v-if="favoriteing" />
      </ht-icon>
      <q-icon
        v-if="showToolBar"
        v-ca:[file.privilegeId]="fileBaseAcl.toolbar"
        name="more_horiz"
        class="is-current-show absolute-top-right q-ma-xs"
        :class="$q.dark.isActive ? 'hover:text-font-placeholder' : 'hover:text-font-primary'"
        @click.stop.prevent
      >
        <q-menu :class="dense && 'text-caption'" no-focus no-refocus @update:model-value="toggleTools">
          <q-list class="is-merge-separator" :padding="!dense" dense>
            <slot name="operations" :file="file" :fileAcl="fileAcl" />
            <q-item v-if="rename" v-ca:[file.privilegeId]="fileBaseAcl.edit" clickable v-close-popup @click="editTitle">
              <q-item-section class="col-auto">
                <q-icon name="edit" />
              </q-item-section>
              <q-item-section>重命名</q-item-section>
            </q-item>

            <template v-if="favorite">
              <q-item
                v-if="file.favorite"
                v-acl="fileBaseAcl.deleteFavorite"
                clickable
                v-close-popup
                @click="toggleFavorite"
              >
                <q-item-section class="col-auto">
                  <q-icon name="star_border" />
                </q-item-section>
                <q-item-section>取消星标</q-item-section>
              </q-item>
              <q-item v-else v-acl="fileBaseAcl.saveFavorite" clickable v-close-popup @click="toggleFavorite">
                <q-item-section class="col-auto">
                  <q-icon name="star_border" />
                </q-item-section>
                <q-item-section>添加星标</q-item-section>
              </q-item>
            </template>
            <q-separator :class="!dense && 'q-my-xs'" />
            <slot name="tools" :file="file" :fileAcl="fileAcl" />
            <q-separator :class="!dense && 'q-my-xs'" />
            <q-item
              v-if="deleteType"
              v-ca:[file.privilegeId]="fileBaseAcl.del"
              clickable
              v-close-popup
              @click="delFile"
            >
              <q-item-section class="col-auto">
                <q-icon name="delete_outline" />
              </q-item-section>
              <q-item-section>删除</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-icon>
    </div>
    <div class="ht-app-file__title ellipsis text-center q-mb-sm" @click="onClickTitle">
      <q-input
        v-if="editId === file.id"
        v-model="title"
        :dense="false"
        ref="titleInputRef"
        input-class="text-center"
        @blur="updateTitle"
        @keyup.enter="updateTitle"
        :maxlength="50"
      />
      <slot v-else name="title" :file="file" :fileAcl="fileAcl">{{ file.title }}</slot>
    </div>
  </div>
</template>

<style lang="scss" src="./file.scss"></style>
<script lang="ts" src="./file.ts"></script>
