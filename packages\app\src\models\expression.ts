import { FilterType } from './filter';

/**
 * 选定值
 */
export class NormalFilter {
  // 0-排除，(取反)
  include = 1;

  filterType = FilterType.Normal;

  // 条件值
  values: string[] = [];

  // 默认：0，1-查询全部有值数据
  includeAll = 0;
}

/**
 * 通配符
 */
export class QueryFilter {
  include = 1;

  filterType = FilterType.Query;

  // 通配符类型 1  包含 2开头为 3 结尾为 4 精确匹配
  queryType = 1;

  // 条件值
  values: string[] = [];
}

/**
 * 值范围
 */
export class RangeFilter {
  filterType = FilterType.Range;

  // 计算表达式 下标值与条件值中位置的对应 =、>=、>、<=、<
  rangeOperator: string[] = ['>=', '<='];

  // 范围值
  values: string[] | number[] = [];
}

/**
 * 相对日期
 */
export class ScopeFilter {
  filterType = FilterType.Scope;

  /** 锚点相对于 */
  anchor = false;

  /** 锚点相对于的值 */
  anchorDate: string = '';

  /** 类型 */
  dateType: 'Years' | 'Quarters' | 'Months' | 'Weeks' | 'Days' | '' = '';

  /** 日期范围类型
   *
   * - `1`: 前一 ?
   * - `2`: 本 ?
   * - `3`: 后一 ?
   * - `4`: 最后一个 ?
   * - `5`: 下一步 ?
   * - `6`: 本 ？迄今 ( 在 `dateType` 不等于 `Days` 时有用 )
   *
   * `?` 为 (年、季度、月、星期、天)
   */
  dataScopeType: 1 | 2 | 3 | 4 | 5 | 6 | null = 2;

  /** /相对值 */
  dateValue = 3;
}

export enum ScopeDateType {
  Years = 'Years',
  Quarters = 'Quarters',
  Months = 'Months',
  Weeks = 'Weeks',
  Days = 'Days'
}

export const ScopeDateTypeName = {
  [ScopeDateType.Years]: '年',
  [ScopeDateType.Quarters]: '季度',
  [ScopeDateType.Months]: '月',
  [ScopeDateType.Weeks]: '周',
  [ScopeDateType.Days]: '天'
};
