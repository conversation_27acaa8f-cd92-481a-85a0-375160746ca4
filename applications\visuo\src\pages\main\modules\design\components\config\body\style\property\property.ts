import { computed, defineComponent, nextTick, ref } from 'vue';
import {
  Color,
  Constraints,
  DirectionType,
  FillPaints,
  FillType,
  Frame,
  Graph,
  GraphType,
  ResizeType,
  useDocumentStore
} from '@vis/document-core';
import { useDesignStore } from '../../../../../stores';
import VisPosition from './postion/index.vue';
import VisLayout from './layout/index.vue';
import VisAspect from './aspect/index.vue';
import { useGraph, useWidget } from '../../../../../hooks';
/**
 * 通用配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-property',
  components: {
    VisPosition,
    VisLayout,
    VisAspect
  },
  setup() {
    const designStore = useDesignStore();

    const activeGraph = computed(() => designStore.active.value.graph as Graph);

    const frame = computed(() => {
      if (activeGraph.value.type === GraphType.Frame) {
        return activeGraph.value as Frame;
      } else {
        return undefined;
      }
    });

    const docStore = useDocumentStore();

    const { isUndefined, handleManage } = useWidget();
    const undef = (key: keyof Graph) => {
      return (
        isUndefined(activeGraph.value, key) || (Array.isArray(activeGraph.value[key]) && !activeGraph.value[key].length)
      );
    };
    const manage = (key: keyof Graph) => {
      handleManage(activeGraph.value, key);
    };

    // 声明index数组，记录填充的key值（填充为头部追加，无法使用下标做为key）
    let idx = 0;
    let indexArr = activeGraph.value.fillPaints?.map(() => idx++) || [];
    /**
     * 添加填充
     */
    const addFillPaints = () => {
      const fillPaints = new FillPaints(FillType.Solid, new Color(0, 0, 0, 0.2), 0.2);
      if (isUndefined(activeGraph.value, 'fillPaints')) {
        activeGraph.value.fillPaints = [];
      }
      activeGraph.value.fillPaints?.unshift(fillPaints);

      indexArr.unshift(idx++);
    };

    /**
     * 删除某个填充
     * @param index
     */
    const deleteFillPaints = (index: number) => {
      if (activeGraph.value.fillPaints && activeGraph.value.fillPaints.length > 1) {
        activeGraph.value.fillPaints.splice(index, 1);
        indexArr.splice(index, 1);
      } else {
        activeGraph.value.fillPaints = undefined;
        indexArr = [];
      }
    };

    /**
     * 改变固定布局和响应式布局
     */
    const onChangeLayoutType = () => {
      if (frame.value) {
        frame.value.autoLayout.type = frame.value?.autoLayout.type === 'fixed' ? 'responsive' : 'fixed';
      }
    };

    /**
     * 适应内容大小
     */
    const onAdaptContent = () => {
      if (frame.value && frame.value.children.length) {
        const width: number[] = [];
        const height: number[] = [];
        const x: number[] = [];
        const y: number[] = [];
        frame.value.children.forEach((g) => {
          const left = g.transform.translate[0];
          const top = g.transform.translate[1];
          width.push(...[left, left + g.width]);
          height.push(...[top, top + g.height]);
          x.push(g.transform.translate[0]);
          y.push(g.transform.translate[1]);
        });
        const minX = Math.min(...x);
        const minY = Math.min(...y);

        frame.value.width = Math.round(Math.max(...width) - Math.min(...width));
        frame.value.height = Math.round(Math.max(...height) - Math.min(...height));

        if (frame.value.autoLayout.direction !== DirectionType.Freeform) {
          const padding = frame.value.autoLayout.padding;
          frame.value.width += padding[1] + padding[3];
          frame.value.height += padding[0] + padding[2];
        }

        frame.value.children.forEach((g) => {
          const left = g.transform.translate[0];
          const top = g.transform.translate[1];
          g.transform.translate = [Math.round(left - minX), Math.round(top - minY)];
        });
      }
    };

    const layoutRef = ref();

    const onSetupLayout = () => {
      if (frame.value) {
        frame.value.autoLayout.direction =
          frame.value.autoLayout.direction === DirectionType.Freeform
            ? DirectionType.Horizontal
            : DirectionType.Freeform;
        layoutRef.value.onChangeDirection(frame.value.autoLayout.direction);
      }
    };

    //#region 忽略自动布局
    const { findGraph, resetGraphXYByDom, activeGraphs } = useGraph();

    // 当前选中的图形的父级Frame
    const parentFrame = computed(() => {
      if (activeGraph.value.parent) {
        const activeFrame = designStore.active.value.frame as Frame;
        if (activeFrame && activeGraph.value.parent === activeFrame.id) {
          return activeFrame;
        } else {
          return findGraph(activeGraph.value.parent) as Frame;
        }
      } else {
        return undefined;
      }
    });

    /**
     * 忽略自动布局
     */
    const onIgnoreAutoLayout = () => {
      const graph = activeGraph.value;
      activeGraph.value.ignoreAutoLayout = !activeGraph.value.ignoreAutoLayout;

      const ignore = activeGraph.value.ignoreAutoLayout;
      activeGraph.value.constraints = ignore ? new Constraints() : undefined;
      if (ignore) {
        activeGraph.value.limitSize.width.resize = ResizeType.Fixed;
        activeGraph.value.limitSize.height.resize = ResizeType.Fixed;
        activeGraphs();
        nextTick(() => {
          activeGraphs([graph]);
        });
      } else {
        nextTick(() => {
          resetGraphXYByDom([activeGraph.value]);
          activeGraphs();
          nextTick(() => {
            activeGraphs([graph]);
          });
        });
      }
    };
    //#endregion

    return {
      activeGraph,
      frame,
      parentFrame,
      DirectionType,

      undef,
      manage,

      indexArr,
      addFillPaints,
      deleteFillPaints,

      layoutRef,
      onSetupLayout,
      onChangeLayoutType,

      onAdaptContent,

      onIgnoreAutoLayout
    };
  }
});
