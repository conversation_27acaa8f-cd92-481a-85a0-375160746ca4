const { getRootPath, run } = require('./utils');

/**
 * serve
 * @param {string} apps
 * @param {string} context
 */
module.exports.serve = (apps, context) => {
  context = context || getRootPath();

  run('cross-env', [`VITE_TARGET_APPS=${apps}`, 'pnpm serve'], { cwd: context });
};

/**
 * build
 * @param {string} apps
 * @param {string} dist
 * @param {string} context
 */
module.exports.build = (apps, dist, context) => {
  context = context || getRootPath();
  const args = ['build'];
  dist && args.push('--dist', dist);
  run('cross-env', [`VITE_TARGET_APPS=${apps}`, 'pnpm', ...args], { cwd: context });
};
