<template>
  <div v-show="!id" class="ht-material-geo ht-app-page__main col column relative q-gutter-y-md">
    <div class="ht-app-page__main-header row items-center">
      <label class="col text-h5"><PERSON><PERSON><PERSON><PERSON></label>
      <div class="col row flex-center gt-sm">
        <q-input
          v-model="filter"
          class="ht-field ht-field--medium ht-field--light"
          placeholder="搜索"
          debounce="300"
          dense
          rounded
          standout
        >
          <template v-slot:prepend>
            <q-icon name="search" size="20px" />
          </template>
        </q-input>
      </div>
      <div class="col">
        <div class="row justify-end q-gutter-x-md">
          <label v-acl="acl.create" class="ht-link" @click="create">
            <q-icon name="add" size="xs" />
            <span class="q-ml-xs">新建</span>
          </label>
          <label v-acl="acl.delete" class="ht-link is-negative" @click="del">
            <q-icon name="delete_outline" size="xs" />
            <span class="q-ml-xs">删除</span>
          </label>
        </div>
      </div>
    </div>

    <q-scroll-area class="col">
      <template v-if="tree.length">
        <div class="row justify-between items-center q-pb-sm q-pl-xs">
          <q-checkbox v-model="checkAll" class="q-mr-xs" dense></q-checkbox>
          <div class="col row">
            <div class="col"><label class="q-ml-xs q-pl-md">名称</label></div>
            <div class="col">编码</div>
            <div class="w-16 text-center">状态</div>
          </div>
        </div>
        <q-separator class="q-mt-xs" />
        <q-tree
          v-model:ticked="ticked"
          :nodes="tree"
          ref="treeRef"
          node-key="id"
          label-key="name"
          selected-color="primary"
          tick-strategy="strict"
          :filter="filter"
          default-expand-all
        >
          <template #default-header="props">
            <div class="col row items-center q-py-xs">
              <div class="col">
                <q-icon
                  :class="[
                    'q-tree__arrow',
                    props.expanded && 'q-tree__arrow--rotate',
                    !(props.node.children && props.node.children.length) && 'invisible'
                  ]"
                  :style="{ marginLeft: getParentsLen(props.node) * 16 + 'px' }"
                  name="play_arrow"
                  size="16px"
                  @click.stop="props.expanded = !props.expanded"
                />
                <label v-acl="acl.edit" class="ht-link is-primary" @click="edit(props.node)">
                  {{ props.node.name }}
                </label>
                <label v-acl="acl.noEdit">{{ props.node.name }}</label>
              </div>
              <div class="col">{{ props.node.cityCode }}</div>
              <div class="w-16 text-cetner">
                <q-toggle
                  v-model="props.node.active"
                  v-acl="acl.active"
                  :false-value="0"
                  :true-value="1"
                  @update:model-value="updateActive(props.node)"
                />
                <q-btn v-acl="acl.noActive" size="sm" color="primary" unelevated>
                  {{ props.node.active === 1 ? '启用' : '禁用' }}
                </q-btn>
              </div>
            </div>
          </template>
        </q-tree>
      </template>
      <ht-empty v-else-if="!loading" description="~ 空空如也 ~" />
    </q-scroll-area>
  </div>

  <ht-material-geo-info v-if="id" :id="id" :tree="tree" @onBack="back" />
</template>

<script src="./geo.ts" lang="ts"></script>
