<template>
  <q-avatar :icon="!avatar ? 'person' : undefined" size="36px" class="cursor-pointer">
    <q-img v-if="avatar" :src="avatar">
      <template v-slot:error>
        <q-icon name="person" class="absolute-center" />
      </template>
    </q-img>
    <q-popup-proxy class="ht-user-popup" breakpoint="0">
      <div class="ht-user-cover column flex-center">
        <q-avatar :icon="!avatar ? 'person' : undefined" size="48px">
          <q-img v-if="avatar" :src="avatar">
            <template v-slot:error>
              <q-icon name="person" class="absolute-center" />
            </template>
          </q-img>
        </q-avatar>
        <label class="q-pt-sm text-white">{{ name }}</label>
      </div>
      <q-list dense>
        <q-item v-if="!isProfile">
          <q-item-section v-close-popup @click="PageService.openProfile()">个人设置</q-item-section>
        </q-item>
        <q-item>
          <q-item-section v-close-popup @click="logout">退出登录</q-item-section>
        </q-item>
      </q-list>
    </q-popup-proxy>
  </q-avatar>
</template>

<script setup lang="ts">
import { AppService, AttachmentService, PageService, PassportService } from '@hetu/platform-shared';
import { SettingsService } from '@hetu/theme';
import { useQuasar } from 'quasar';

const user = SettingsService.user || {};
const name = user.fullName || '--';
const avatar = user.avatar ? AttachmentService.downloadFileUrl(user.avatar) : '';
const isProfile = AppService.appKey === import.meta.env.VITE_HETU_PROFILE_KEY;

const $q = useQuasar();
const logout = (): void => {
  $q.dialog({
    title: '提示',
    message: '确认退出 ?',
    ok: '确定',
    cancel: '取消'
  }).onOk(() => {
    PassportService.logout().then(() => PageService.toLogin());
  });
};
</script>
