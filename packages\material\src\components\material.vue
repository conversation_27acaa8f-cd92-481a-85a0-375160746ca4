<template>
  <q-dialog class="ht-material" ref="dialogRef" v-close-popup maximized>
    <label
      class="absolute-top-right z-top pointer-events-auto"
      :class="$q.screen.gt.lg ? 'q-pt-lg q-pr-lg' : $q.screen.gt.md ? 'q-mt-md q-mr-md' : 'q-mt-sm q-mr-sm'"
    >
      <q-btn
        :class="$q.screen.gt.lg && 'q-mt-md q-mr-md'"
        icon="close"
        :color="'grey-5'"
        :size="$q.screen.gt.md ? 'md' : 'sm'"
        round
        unelevated
        @click="onDialogCancel"
      ></q-btn>
    </label>
    <q-card
      :style="`width: ${$q.screen.gt.lg ? 1600 : $q.screen.gt.md ? 1320 : 1040}px; height: ${
        $q.screen.gt.lg ? 800 : 660
      }px`"
    >
      <q-layout view="hHh LpR fff" class="column">
        <q-page-container class="col column">
          <ht-app-page
            class="col"
            title="素材库"
            :menus="type ? null : menus"
            :active-menu="activeMenuId"
            dense
            @selectMenu="selectMenu"
          >
            <template #main>
              <ht-material-media
                v-if="materialType.Image === activeMenuId"
                v-model="value"
                :multi="!type"
                :activeMenu="activeMenu"
                :type="materialType.Image"
              />

              <ht-material-media
                v-else-if="materialType.Video === activeMenuId"
                v-model="value"
                :multi="!type"
                :activeMenu="activeMenu"
                :type="materialType.Video"
              />

              <ht-material-geo v-else-if="materialType.Geo === activeMenuId" :activeMenu="activeMenu" />
            </template>
          </ht-app-page>
        </q-page-container>

        <q-footer v-if="type" class="q-px-md q-py-sm transparent text-right" :class="!$q.dark.isActive && 'text-dark'">
          <q-btn class="ht-btn--outline" label="取消" flat @click="onDialogCancel" />
          <q-btn class="q-ml-md" label="确定" color="primary" unelevated @click="onSure" />
        </q-footer>
      </q-layout>
    </q-card>
  </q-dialog>
</template>
<script src="./material.ts" lang="ts"></script>
<style src="./material.scss" lang="scss"></style>
