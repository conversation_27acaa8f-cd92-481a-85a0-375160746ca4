import { computed, defineComponent, type PropType } from 'vue';
import { type ParagraphOptions } from '@vis/document-core';

export default defineComponent({
  name: 'vis-config-paragraph-option',
  props: {
    options: {
      type: Object as PropType<ParagraphOptions>,
      required: true
    }
  },
  setup(props) {
    const computedOptions = computed(() => props.options);

    const handleLink = () => {
      computedOptions.value.link.enable = !computedOptions.value.link.enable;
    };

    return {
      computedOptions,
      handleLink
    };
  }
});
