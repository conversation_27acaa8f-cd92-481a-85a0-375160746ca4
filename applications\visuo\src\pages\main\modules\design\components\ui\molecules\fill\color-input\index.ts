import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { type Color, ColorValueType } from '@vis/document-core';
import { useColorTransfer } from '../../../../../hooks';

export default defineComponent({
  name: 'vis-fill-color-input',
  props: {
    modelValue: {
      type: Object as PropType<Color>,
      required: true
    }
  },
  setup(props, { emit }) {
    const valueOptions = ref([
      ColorValueType.RGB,
      ColorValueType.HEX,
      ColorValueType.HSL,
      ColorValueType.HSB,
      ColorValueType.CSS
    ]);
    const valueType = ref(ColorValueType.HEX);

    const checkType = (value: ColorValueType) => {
      return valueType.value === value;
    };

    const { r, g, b, a } = props.modelValue;
    const { rgbToHsb, hsbToRgb, rgbToHsl, hslToRgb, rgbToHex, hexToRgb, rgbaStrToRgb } = useColorTransfer();

    // RGB
    const rgb = ref({ r, g, b });

    // HEX
    const hex = ref(rgbToHex(r, g, b));
    const checkHex = () => {
      const { r, g, b } = hexToRgb(hex.value);
      hex.value = rgbToHex(r, g, b);

      changeColor();
    };

    // HSB
    const hsb = ref(rgbToHsb(r, g, b));

    // HSL
    const hsl = ref(rgbToHsl(r, g, b));

    // CSS
    const css = ref(`rgba(${r}, ${g}, ${b}, ${a})`);
    const checkCss = () => {
      try {
        const { r, g, b, a } = rgbaStrToRgb(css.value);
        css.value = `rgba(${r}, ${g}, ${b}, ${a})`;
      } catch (error) {
        css.value = `rgba(${props.modelValue.r}, ${props.modelValue.g}, ${props.modelValue.b}, ${props.modelValue.a})`;
      }
    };

    // #region 透明度
    const alpha = ref(`${props.modelValue.a * 100}%`);
    watch(
      () => props.modelValue.a,
      () => {
        alpha.value = `${Math.round(props.modelValue.a * 100)}%`;
      }
    );
    const oldAlpha = ref('');

    const alphaRef = ref();
    const focusAlpha = () => {
      oldAlpha.value = alpha.value;
      alphaRef.value && alphaRef.value.select();
    };

    /**
     * 保持alpha在0-100之间
     */
    const updateAlpha = () => {
      const alphaNum = Math.round(Number(alpha.value.replace('%', '')));
      if (!alpha.value || Number.isNaN(alphaNum)) {
        alpha.value = oldAlpha.value;
      } else {
        if (alphaNum < 0) {
          alpha.value = '0%';
        } else if (alphaNum > 100) {
          alpha.value = '100%';
        } else {
          alpha.value = `${alphaNum}%`;
        }
        oldAlpha.value = alpha.value;

        Object.assign(props.modelValue, { a: parseInt(alpha.value.replace('%', '')) / 100 });
      }
    };
    // #endregion

    /**
     * 输入框改变r、g、b、a值
     */
    const changeColor = () => {
      switch (valueType.value) {
        case ColorValueType.HEX: {
          const { r, g, b } = hexToRgb(hex.value);
          Object.assign(props.modelValue, { r, g, b });
          break;
        }

        case ColorValueType.HSL: {
          const { r, g, b } = hslToRgb(hsl.value.h, hsl.value.s, hsl.value.l);
          Object.assign(props.modelValue, { r, g, b });
          break;
        }
        case ColorValueType.HSB: {
          const { r, g, b } = hsbToRgb(hsb.value.h, hsb.value.s, hsb.value.v);
          Object.assign(props.modelValue, { r, g, b });
          break;
        }
        case ColorValueType.CSS: {
          const { r, g, b, a } = rgbaStrToRgb(css.value);
          Object.assign(props.modelValue, { r, g, b, a });
          break;
        }

        default:
          Object.assign(props.modelValue, { r: rgb.value.r, g: rgb.value.g, b: rgb.value.b });
          break;
      }
      emit('update:modelValue', props.modelValue);
    };

    watch(
      () => props.modelValue,
      () => {
        rgb.value = { r: props.modelValue.r, g: props.modelValue.g, b: props.modelValue.b };
        hex.value = rgbToHex(props.modelValue.r, props.modelValue.g, props.modelValue.b);
        hsb.value = rgbToHsb(props.modelValue.r, props.modelValue.g, props.modelValue.b);
        hsl.value = rgbToHsl(props.modelValue.r, props.modelValue.g, props.modelValue.b);
        css.value = `rgba(${props.modelValue.r}, ${props.modelValue.g}, ${props.modelValue.b}, ${props.modelValue.a})`;
      },
      { deep: true }
    );
    return {
      ColorValueType,
      valueOptions,
      valueType,
      checkType,

      rgb,
      hex,
      checkHex,
      hsb,
      hsl,
      css,
      checkCss,

      changeColor,

      alpha,
      alphaRef,
      focusAlpha,
      updateAlpha
    };
  }
});
