import { isBoolean, isNumber } from 'lodash-es';
import { Font, TextAdapt, TextAlign } from '../../ui';

/**
 * 表单公用标签
 */
export class Label {
  /** 是否展示标签 */
  show: boolean = true;
  /** 标签内容 */
  text: string = '标签';
  /** 标签位置 */
  position: 'left' | 'top' = 'left';
  /** 对齐方式 */
  align = TextAlign.Left;
  /** 标签宽度 */
  width: number | string = 50;
  /** 内边距 */
  padding: [number, number, number, number] = [0, 0, 0, 0];
  /** 标签字体设置 */
  font: LabelFont = new LabelFont();

  constructor(show?: boolean, text?: string) {
    isBoolean(show) && (this.show = show);
    text && (this.text = text);
  }
}
/**
 * 标签字体
 */
export class LabelFont extends Font {
  /** 字间距 */
  letterSpacing: number = 0;
  /** 行高 */
  lineHeight: number = 0;
  /** 适应模式 */
  adapt = TextAdapt.Fixed;
}
/**
 * 表单验证
 */
export class FormRule {
  type = FormRuleType.Required;
  value?: number;
  constructor(type?: FormRuleType, value?: number) {
    type && (this.type = type);
    isNumber(value) && (this.value = value);
  }
}

/**
 * 表单验证类型
 */
export enum FormRuleType {
  Required = 'required',
  Email = 'email',
  Url = 'url',
  Tel = 'tel',
  MinLength = 'minLength',
  MaxLength = 'maxLength'
}

export const FormRuleOptions = [
  { label: '必填', value: FormRuleType.Required },
  {
    label: '邮箱',
    value: FormRuleType.Email
  },
  {
    label: '网址',
    value: FormRuleType.Url
  },
  {
    label: '电话',
    value: FormRuleType.Tel
  },
  { label: '最小长度', value: FormRuleType.MinLength },
  { label: '最大长度', value: FormRuleType.MaxLength }
];

export * from './input';
