import { HtException403, HtException404, HtException500, HtSpaceLogin } from '../components';
import type { RouteComponent, RouteRecordRaw } from 'vue-router';

export * from './space';

/** 异常路由 */
export const EXCEPTION_ROUTES: RouteRecordRaw[] = [
  {
    name: '403',
    path: '/403',
    component: HtException403,
    meta: {
      title: '403'
    }
  },
  {
    name: '404',
    path: '/404',
    component: HtException404,
    meta: {
      title: '404'
    }
  },
  {
    name: '500',
    path: '/500',
    component: HtException500,
    meta: {
      title: '500'
    }
  }
];

/** 通配符路由 */
export const WILDCARD_ROUTE: RouteRecordRaw = { path: '/:path(.*)*', redirect: '/404' };

/** 异常路由 & 通配符路由 */
export const DEF_ROUTES = [...EXCEPTION_ROUTES, WILDCARD_ROUTE];

/** 空间登录路由 */
export const SPACE_LOGIN_ROUTE: RouteRecordRaw = {
  name: 'login',
  path: '/login',
  component: HtSpaceLogin,
  meta: {
    title: '登录'
  }
};

/** 消息跳转路由 */
export const createJumpRoute = (component: RouteComponent) => {
  return {
    path: '/jump',
    name: 'jump',
    component
  };
};
