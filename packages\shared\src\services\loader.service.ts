import { createSingleClass } from '@hetu/core';
import { LazyService } from '@hetu/util';

export const LAZY_PATHS = {
  vs: './static/libs/monaco-editor/vs'
} as const;

export class LoaderServiceCtor {
  /**
   * AMD模块解析器文件
   * 由于monaco-editor的loader文件是基于AMD标准的模块解析器所以没有引入requireJS插件, 防止冲突
   */
  private readonly amdLoaderFile = './static/libs/monaco-editor/vs/loader.js';

  private setAMDConfig(amdRequire: any) {
    this.setAMDConfig = () => {};
    const paths = { ...LAZY_PATHS };
    amdRequire.config({
      paths: paths,
      'vs/nls': { availableLanguages: { '*': 'zh-cn' } }
    });
  }

  /**
   * 加载模块解析器(AMD)
   */
  amd(): Promise<any> {
    return LazyService.load(this.amdLoaderFile).then(() => {
      const amdRequire = (window as any).require;
      this.setAMDConfig(amdRequire);
      return amdRequire;
    });
  }
}

export const LoaderService = createSingleClass(LoaderServiceCtor);
