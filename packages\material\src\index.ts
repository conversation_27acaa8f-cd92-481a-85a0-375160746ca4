import { HtMaterial } from './components';
import { MaterialType } from './models';
import { Dialog } from 'quasar';

/**
 * 素材库弹出框
 * @param modelValue 选中值
 * @param type 类型
 */
const HtMaterialDialog = (modelValue?: string, type?: `${MaterialType}`) => {
  return Dialog.create({
    component: HtMaterial,
    componentProps: {
      modelValue,
      type
    }
  });
};

export { HtMaterialDialog, HtMaterial };
