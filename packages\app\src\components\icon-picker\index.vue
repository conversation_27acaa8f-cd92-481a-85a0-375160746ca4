<template>
  <div class="ht-icon-picker">
    <slot>
      <div class="ht-icon-picker__icon">
        <q-btn v-if="icon" :icon="icon.name" size="xs" outline padding="4px" />
        <q-btn v-else :icon="modelValue" size="xs" outline padding="4px" />
        <span class="ml-2" v-if="label">{{ label }}</span>
      </div>
    </slot>
    <q-menu v-model="visible" class="ht-icon-picker__panel">
      <q-card class="row column h-full">
        <q-card-section>
          <div class="row justify-between items-center mb-2 q-gutter-x-sm">
            <div class="col-6">
              <q-select
                v-model="iconSet"
                label="图标库"
                :options="['material-icons', 'material-icons-outlined']"
                hide-dropdown-icon
                dense
                @update:model-value="onChangeIcon"
              />
            </div>
            <div class="col-5">
              <q-input v-model="search" dense label="搜索"> </q-input>
            </div>
          </div>
          <div class="row flex-center q-gutter-x-sm" v-if="icon">
            <div class="col-1 !m-0">
              <ht-color-picker v-model="iconModel.color" />
            </div>
            <div class="col-5">
              <q-input v-model="iconModel.name" class="vis-field--mini" />
            </div>
            <div class="col-5 !ml-3.5">
              <q-input v-model.number="iconModel.size" type="number" dense class="vis-field--mini" />
            </div>
          </div>
          <q-input v-else v-model="iconModel.name" class="vis-field--mini" />
        </q-card-section>

        <q-card-section class="col !px-0 h-full">
          <q-icon-picker
            v-if="!loading"
            v-model="iconModel.name"
            v-model:model-pagination="pagination"
            :icons="icons"
            :filter="search"
            tooltips
            size="md"
          />
          <q-inner-loading :showing="loading" color="primary"> </q-inner-loading>
        </q-card-section>

        <q-separator />

        <q-card-actions :align="'right'">
          <q-btn flat size="sm" label="清空" @click="onClear" />
          <q-btn unelevated size="sm" color="primary" label="确定" @click="onSure" class="ml-2" />
        </q-card-actions>
      </q-card>
    </q-menu>
  </div>
</template>
<script lang="ts" setup>
import { IconStyle } from '.';
import { computed, ref, watch, type PropType } from 'vue';
import { QIconPicker } from '@quasar/quasar-ui-qiconpicker';

/**
 * 图标选择器
 * <AUTHOR>
 */
defineOptions({ name: 'ht-icon-picker' });

const props = defineProps({
  icon: {
    type: Object as PropType<IconStyle>
  },
  modelValue: {
    type: String
  },
  label: {
    type: String
  }
});

const emit = defineEmits(['update:modelValue']);

const search = ref('');

const visible = ref(false);

const iconSet = ref('material-icons');

const icons = computed(() => {
  return iconSet.value === 'material-icons' ? materialIcons.value : materialIconsOutlined.value;
});

const pagination = ref({
  itemsPerPage: 30,
  page: 0
});

const iconStyle = computed(() => props.icon);

const iconModel = ref(new IconStyle());

const materialIcons = ref([]);

const materialIconsOutlined = ref([]);

const loading = ref(true);

import('@quasar/quasar-ui-qiconpicker/src/components/icon-set/material-icons.js')
  .then((data) => {
    materialIcons.value = data.default.icons;
  })
  .finally(() => {
    loading.value = false;
  });

const onChangeIcon = () => {
  if (iconSet.value === 'material-icons-outlined' && !materialIconsOutlined.value.length) {
    loading.value = true;
    import('@quasar/quasar-ui-qiconpicker/src/components/icon-set/material-icons-outlined.js')
      .then((data) => {
        materialIconsOutlined.value = data.default.icons;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

watch(
  [() => props.modelValue, () => props.icon],
  () => {
    if (props.modelValue) {
      iconModel.value.name = props.modelValue;
    } else if (iconStyle.value) {
      iconModel.value.name = iconStyle.value.name;
      iconModel.value.color = iconStyle.value.color;
      iconModel.value.size = iconStyle.value.size;
    }
  },
  {
    immediate: true
  }
);

const onSure = () => {
  if (iconStyle.value) {
    iconStyle.value.name = iconModel.value.name;
    iconStyle.value.color = iconModel.value.color;
    iconStyle.value.size = iconModel.value.size;
  } else {
    emit('update:modelValue', iconModel.value.name);
  }

  visible.value = false;
};

const onClear = () => {
  iconModel.value = new IconStyle();
};
</script>

<style lang="scss">
@import '@quasar/quasar-ui-qiconpicker/src/index.sass';
.#{$ht-prefix}-icon-picker {
  &__panel {
    @apply overflow-hidden;

    width: 300px;
    height: 402px;

    .q-card {
      &__section {
        @apply py-2;
      }
    }

    .q-field {
      &__native {
        @apply text-xs;
      }

      &__label {
        @apply text-xs;
      }

      &__focusable-action {
        @apply text-sm;
      }
    }

    .q-icon-picker {
      &__container {
        .q-btn {
          @apply m-1 px-2;

          .q-icon {
            @apply text-xl;
          }
        }
      }

      &__footer {
        .q-btn {
          .q-icon {
            @apply text-lg;
          }
        }

        .q-field {
          .q-field__control {
            @apply h-7 px-1.5;

            min-height: 28px;

            .q-field__native {
              @apply text-xs p-0;

              min-height: 28px;
            }
          }
        }
      }
    }

    .#{$ht-prefix}-color-picker {
      @apply justify-end;
    }
  }
}
</style>
