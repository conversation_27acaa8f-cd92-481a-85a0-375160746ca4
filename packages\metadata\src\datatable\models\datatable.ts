import { BaseModel } from '@hetu/http';
import type { DatatableFields } from './datatable-field';
import type { FileInfo } from '@hetu/platform-shared';

/**
 * 数据表对象
 * <AUTHOR>
 */
export class Datatable extends BaseModel {
  /**
   * 数据源名称
   */
  datasourceId = '';

  /**
   * tableName表名
   */
  tableName = '';

  /**
   * 数据表别名
   */
  tableAlias = '';

  /**
   * 表字段集合
   */
  fields: DatatableFields = [];

  /**
   * 描述
   */
  remark?: string;

  constructor(id?: string, datasourceId?: string, tableName?: string, tableAlias?: string, remark?: string) {
    super();
    id && (this.id = id);
    datasourceId && (this.datasourceId = datasourceId);
    tableName && (this.tableName = tableName);
    tableAlias && (this.tableAlias = tableAlias);
    remark && (this.remark = remark);
  }
}

/**
 * 数据表文件
 */
export interface DatatableFile extends FileInfo {
  /** 数据源唯一标识 */
  datasourceId: string;
  /** 表别名 */
  tableAlias: string;
}

/**
 * 数据表对象数组
 * @type
 */
export type Datatables = Datatable[];
