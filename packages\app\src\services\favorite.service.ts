import { API_PREFIX, SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import {
  HttpApiService,
  type HttpApi,
  type ResponseResult,
  responseResult,
  responseReject,
  responseData
} from '@hetu/http';
import { createSingleClass } from '@hetu/core';
import type { FavoriteChoose } from '../models';

const API_HOST = `${API_PREFIX}${SYSTEM_API_PREFIX}/favorite`;

class FavoriteApi implements HttpApi {
  catalogSave = `${API_HOST}/catalog/save`;
  catalogDelete = `${API_HOST}/catalog/delete`;
  fileSave = `${API_HOST}/file/save`;
  fileDelete = `${API_HOST}/file/delete`;
  chooseSend = `${API_HOST}/choose/send`;
  chooseInfo = `${API_HOST}/choose/info`;
}

/**
 * 星标服务类
 * <AUTHOR>
 */
export class FavoriteServiceCtor extends HttpApiService<FavoriteApi> {
  httpApi = new FavoriteApi();

  httpModuleKey = 'favorite';

  constructor() {
    super();
  }

  /**
   * 目录添加星标
   * @param dataId 目录id
   * @param category 分类
   */
  catalogSave(dataId: string, category: string): Promise<ResponseResult | null> {
    return this.http.post(this.api.catalogSave, { dataId, category }).then(responseResult, responseReject);
  }

  /**
   * 目录取消星标
   * @param dataIds
   */
  catalogDelete(dataIds: string[], category: string): Promise<ResponseResult | null> {
    return this.http
      .delete(`${this.api.catalogDelete}/${category}`, {
        data: { dataIds }
      })
      .then(responseResult, responseReject);
  }

  /**
   * 文件添加星标
   * @param dataId 文件id
   * @param category 分类
   */
  fileSave(dataId: string, category: string): Promise<ResponseResult | null> {
    return this.http
      .post(`${this.api.fileSave}/${category}`, { dataId, category })
      .then(responseResult, responseReject);
  }

  /**
   * 文件取消星标
   * @param dataIds
   */
  fileDelete(dataIds: string[], category: string): Promise<ResponseResult | null> {
    return this.http
      .delete(`${this.api.fileDelete}/${category}`, { data: { dataIds } })
      .then(responseResult, responseReject);
  }

  /**
   * 修改接收方式
   * @param favoriteChoose
   * @returns
   */
  chooseSend(favoriteChoose: FavoriteChoose): Promise<ResponseResult | null> {
    return this.http.post(this.api.chooseSend, favoriteChoose).then(responseResult, responseReject);
  }

  /**
   * 获取星标选择框和勾选数据
   * @param dataId
   * @returns
   */
  chooseInfo(dataId: string): Promise<FavoriteChoose> {
    return this.http.get(this.api.chooseInfo, { params: { dataId } }).then(responseData, responseReject);
  }
}

export const FavoriteService = createSingleClass(FavoriteServiceCtor);
