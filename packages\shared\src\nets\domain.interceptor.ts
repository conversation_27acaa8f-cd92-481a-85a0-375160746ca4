import type { HttpRequestInterceptor, HttpRequestConfig } from '@hetu/http';
import { SpaceService } from '../services';

/** 请求中的域名标识参数名 */
const HTTP_DOMAIN_KEY = 'domain';

/** 请求中的租户标识参数名 */
const HTTP_TENANT_ID_KEY = 'tenantId';

/**
 * `Http` 请求拦截器: 用于添加域名标识
 * <AUTHOR>
 */
export class HttpDomainInterceptor implements HttpRequestInterceptor {
  intercept(config: HttpRequestConfig) {
    const url = config.url as string;
    if (!url.startsWith('./static')) {
      const domain = SpaceService.getDomainCode();
      if (domain) {
        config.headers[HTTP_DOMAIN_KEY] = domain;
      }

      const tenantId = SpaceService.getTenantId();
      if (tenantId) {
        config.headers[HTTP_TENANT_ID_KEY] = tenantId;
      }

      if (process.env.NODE_ENV !== 'production') {
        // 避免 build 时未删除此代码块
        if (url.startsWith('/mock')) {
          config.url += `${config.url?.includes('?') ? '&' : '?'}${HTTP_DOMAIN_KEY}=${domain}`;
        }
      }
    }
    return config;
  }
}
