import { Color } from './fill';

/**
 * 文本阴影
 */
export class TextEffects {
  /** 阴影类型 */
  type = EffectsType.Outset;

  /** 是否显示 */
  visible: boolean = false;
  /** 位置 */
  offset: { x: number; y: number } = { x: 0, y: 4 };
  /** 模糊 */
  blur: number = 4;
  /** 颜色 */
  color: Color = new Color(0, 0, 0, 0.25);

  constructor(type?: EffectsType, color?: Color) {
    type && (this.type = type);
    color && (this.color = color);
  }
}

/**
 * 阴影特效
 */
export class Effects extends TextEffects {
  /** 是否显示 */
  visible: boolean = true;
  /** 扩散 */
  spread: number = 0;
  constructor(type?: EffectsType, color?: Color) {
    super(type, color);
  }
}

export enum EffectsType {
  /** 内阴影 */
  Inset = 'inset',
  /** 外阴影 */
  Outset = 'outset',
  /** 模糊 */
  Blur = 'blur',
  /** 背景模糊 */
  BgBlur = 'bgBlur'
}
