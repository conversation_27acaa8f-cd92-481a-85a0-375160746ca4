import type { Cache, CacheStore } from './interface';

/**
 * 会话存储服务类
 * <AUTHOR>
 */
export class SessionStorageStore implements CacheStore {
  meta: Set<string> = new Set<string>();

  get(key: string): Cache {
    return JSON.parse(sessionStorage.getItem(key) || 'null') || null;
  }

  set(key: string, value: Cache): boolean {
    sessionStorage.setItem(key, JSON.stringify(value));
    return true;
  }

  remove(key: string) {
    sessionStorage.removeItem(key);
  }
}
