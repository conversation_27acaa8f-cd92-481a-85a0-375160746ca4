import type { HttpApi } from '@hetu/http';
import { API_PREFIX } from './index';

/**
 * 数据资源API
 * <AUTHOR>
 */
export class DatasetApi implements HttpApi {
  /** 查询数据集 `[GET]`*/
  datasetList = API_PREFIX + '/dataset/selection/list';

  /** 数据集详情 `[GET]`*/
  getById = API_PREFIX + '/dataset/get';

  /** 数据集详情，不带权限 `[GET]`*/
  getFieldListById = API_PREFIX + '/dataset/field/list';

  /** 获取查询参数列表 `[GET]`*/
  getQueryParam = API_PREFIX + '/dataset/query/params';

  fieldValues = API_PREFIX + '/dataset/field/value';

  /** 移动到空间 */
  spaceMove = `${API_PREFIX}/dataset/space/move`;
  /** 复制到空间 */
  spaceCopy = `${API_PREFIX}/dataset/space/copy`;

  /** 获取当前数据资源类型支持的数据库函数列表 */
  functionList = `${API_PREFIX}/dataset/function/list`;

  /** 获取数据资源id和名称 */
  datasetNames = `${API_PREFIX}/dataset/names`;

  /** 查询 dataframe 数据资源 */
  datasetListFromDataframe = '/dataframe/dataset/tree/list';

  /** 查询api 数据资源 */
  datasetListFromApi = '/api/dataset/treeList';

  [key: string]: any;
}
