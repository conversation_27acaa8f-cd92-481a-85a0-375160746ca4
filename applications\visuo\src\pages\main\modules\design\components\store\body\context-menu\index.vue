<template>
  <q-menu
    ref="contextMenu"
    v-model="showMenu"
    :context-menu="true"
    anchor="top left"
    self="top left"
    @before-show="onBeforeShow"
    @hide="onHide"
  >
    <q-list dense style="min-width: 100px">
      <q-item v-for="item in menuItems" :key="item.key" v-close-popup clickable @click="handleMenuItemClick(item)">
        <q-item-section v-if="item.icon" avatar>
          <q-icon :name="item.icon" />
        </q-item-section>
        <q-item-section>{{ item.label }}</q-item-section>
      </q-item>

      <q-separator v-if="hasSeparators" />

      <q-item
        v-for="group in groupedItems"
        :key="group.key"
        v-close-popup
        clickable
        @click="handleMenuItemClick(group)"
      >
        <q-item-section v-if="group.icon" avatar>
          <q-icon :name="group.icon" />
        </q-item-section>
        <q-item-section>{{ group.label }}</q-item-section>
      </q-item>
    </q-list>
  </q-menu>
</template>
<script lang="ts" src="./index.ts"></script>
<!-- <style lang="scss" src="./body.scss"></style> -->
