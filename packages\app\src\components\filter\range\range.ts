import { defineComponent, ref, type PropType, computed } from 'vue';
import { RangeFilter } from '../../../models';

/**
 * 筛选器-值范围
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-filter-range',
  props: {
    filter: {
      type: Object as PropType<RangeFilter>,
      required: true
    },
    dataType: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const rangeFilter = ref(props.filter);

    const isNumber = computed(() => {
      return ['integer', 'number', 'double'].includes(props.dataType);
    });

    const isDate = computed(() => {
      return ['date', 'date_1', 'date_2'].includes(props.dataType);
    });

    const isDateTime = computed(() => {
      return ['datetime', 'datetime_1', 'datetime_2'].includes(props.dataType);
    });

    const isTime = computed(() => {
      return ['time', 'time_1'].includes(props.dataType);
    });

    const format = computed(() => {
      return isDate.value ? 'YYYY-MM-DD' : isDateTime.value ? 'YYYY-MM-DD HH:mm:ss' : 'HH:mm:ss';
    });

    const values: any = computed(() => rangeFilter.value.values);

    const operators = [
      {
        label: '等于',
        value: '='
      },
      {
        label: '大于',
        value: '>'
      },
      {
        label: '小于',
        value: '<'
      },
      {
        label: '大于等于',
        value: '>='
      },
      {
        label: '小于等于',
        value: '<='
      },
      {
        label: '不等于',
        value: '!='
      }
    ];

    const onOperator = (index: number, operator: string) => {
      rangeFilter.value.rangeOperator[index] = operator;
    };

    /**
     * 校验值范围是否合法（空、数字、#{test}）
     * @param val
     * @returns
     */
    const checkNumber = (val: any) => {
      if (/#\{.*?\}/.test(val)) {
        if (/^#\{[0-9a-zA-Z.]+\}$/.test(val)) {
          return true;
        }
        return '变量替换格式错误，只允许数字、字母和.';
      }
      return !val || !Number.isNaN(Number(val));
    };
    const numberRules = [(val: any) => checkNumber(val) || '请输入数字或#{test}类型'];

    /**
     * 值范围输入不合法时置空
     * @param index
     */
    const formatNumber = (index: number) => {
      if (checkNumber(rangeFilter.value.values[index]) !== true) {
        rangeFilter.value.values[index] = '';
      }
    };

    return {
      rangeFilter,
      values,
      isNumber,
      isDate,
      isDateTime,
      isTime,
      operators,
      format,
      onOperator,
      numberRules,
      formatNumber
    };
  }
});
