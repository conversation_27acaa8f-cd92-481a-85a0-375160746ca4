<template>
  <q-dialog ref="dialogRef" class="ht-release-history" @hide="onDialogHide" v-close-popup>
    <q-table
      :rows="rows"
      :columns="columns"
      :pagination="pagination"
      :rows-per-page-options="[0]"
      row-key="index"
      virtual-scroll
    >
      <template v-slot:top>
        <div class="w-full row items-center justify-between no-wrap">
          <div class="q-table__title ellipsis">{{ name ? '发布历史 - ' + name : '发布历史' }}</div>
          <q-space />
          <q-btn icon="close" size="sm" flat round dense @click="onDialogCancel" />
        </div>
      </template>
      <template #body-cell-tools="props">
        <q-td :props="props">
          <q-btn v-acl="acl.rollbackVersion" size="xs" flat round @click="onRollback(props.row)">
            <q-tooltip>恢复</q-tooltip>
            <q-icon name="o_reply" size="16px" />
          </q-btn>
          <q-btn
            v-acl="acl.deleteVersion"
            :disabled="props.row.currentRelease"
            color="negative"
            size="xs"
            flat
            round
            @click="onDelete(props.row, props.rowIndex)"
          >
            <q-tooltip>删除</q-tooltip>
            <q-icon name="delete_outline" size="16px" />
          </q-btn>
        </q-td>
      </template>
    </q-table>
  </q-dialog>
</template>

<style lang="scss" src="./release-history.scss"></style>
<script lang="ts" src="./release-history.ts"></script>
