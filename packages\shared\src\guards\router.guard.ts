import { SystemState, useSystem } from '@hetu/platform-boot';
import { SettingsService, TitleService } from '@hetu/theme';
import { PlatformService } from '../services';
import { Notify } from 'quasar';
import type { RouteLocationNormalized } from 'vue-router';
import { useRouterWithout } from '@hetu/core';
import { nextTick } from 'vue';

export function createGlobalGuard() {
  let checked = false;
  const router = useRouterWithout();

  router.beforeEach(async (to: RouteLocationNormalized) => {
    SettingsService.setFetching(true);
    if (!['403', '500'].includes(to.name as string) && !checked) {
      // 验证系统初始化信息, 只验证一次
      const system = useSystem();
      const { state } = system;
      let path = system && state !== undefined ? '' : '/403';
      if (!path && ![SystemState.Normalized, SystemState.Overdue].includes(state)) {
        let message = '';
        if (SystemState.Unauthorized === state) {
          path = '/403';
          message = '未授权';
        } else {
          path = '/500';
          message = '系统初始化异常';
        }
        Notify.create({
          type: 'negative',
          message: `${message}, 请联系管理员`,
          position: 'top',
          timeout: 0
        });
      } else {
        checked = true;
      }

      if (path) {
        return { path };
      }
    }

    if (to.meta?.cloudOnly && !PlatformService.isDevCloud) {
      return { path: '/404' };
    }
  });

  router.afterEach(() => {
    SettingsService.setFetching(false);
    nextTick(() => TitleService.setTitle());
  });
}
