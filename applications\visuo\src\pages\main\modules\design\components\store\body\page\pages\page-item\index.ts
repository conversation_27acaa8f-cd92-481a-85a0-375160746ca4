import { Page, Group, GraphType } from '@vis/document-core';
import { defineComponent, ref, nextTick } from 'vue';
import type { PropType } from 'vue';
import { VIS_DESIGN_INFINITE_CANVAS } from '../../../../../../models';
import { usePage } from '../../../../../../hooks';
import draggable from 'vuedraggable';
import { throttle } from 'quasar';

/**
 * 图层树
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-page-item',
  components: { draggable },
  props: {
    nodes: {
      type: Array as PropType<(Group | Page)[]>,
      required: true
    },
    selectedIds: {
      type: Array as PropType<string[]>,
      required: true
    },
    expandedMap: {
      type: Map,
      required: true,
      default: () => new Map<string, boolean>()
    },
    activePageId: {
      type: String,
      required: true
    },
    homePageId: {
      type: String,
      required: true
    },
    itemKey: {
      type: String,
      default: VIS_DESIGN_INFINITE_CANVAS
    },
    dragTargetParentId: {
      type: String,
      default: null
    },
    isSearchHide: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:selected-ids', 'update:expanded-map', 'drag-start', 'drag-end', 'drag-add', 'drag-move'],
  setup(props, { emit }) {
    const { renamePage } = usePage();

    // #region 双击编辑
    const editingId = ref<string | null>(null);
    const editingName = ref<string>('');
    const editInputRef = ref<any>(null);
    const isDisabledDrag = ref<boolean>(false);

    const isEditing = (node: Page | Group) => {
      return editingId.value === node.id;
    };

    const onDoubleClick = (node: Page | Group, e: Event) => {
      e.stopPropagation();
      isDisabledDrag.value = true;
      editingId.value = node.id;
      editingName.value = node.name;
      nextTick(() => {
        editInputRef.value?.focus();
        editInputRef.value?.select();
      });
    };

    const onSaveName = (node: Page | Group) => {
      if (editingName.value.trim() && editingName.value !== node.name) {
        const nodeIds = node.type === GraphType.Group ? [...(node as Group).children.map((p) => p.id)] : [node.id];
        renamePage(nodeIds, editingName.value.trim(), node.type);
      }
      isDisabledDrag.value = false;
      editingId.value = null;
      editingName.value = '';
    };

    const onCancelEdit = () => {
      isDisabledDrag.value = false;
      editingId.value = null;
      editingName.value = '';
    };
    // #endregion

    // #region 展开收起
    const isExpanded = (node: Group) => {
      return props.expandedMap.get(node.id) || false;
    };
    const toggleExpand = (node: Group, e: Event) => {
      if (node.children && node.children.length > 0) {
        emit('update:expanded-map', node);
      }
    };
    const onEmitExpanded = (node: Group) => {
      emit('update:expanded-map', node);
    };
    // #endregion

    // #region 拖拽
    const onDragStart = (evt: CustomEvent) => {
      emit('drag-start', evt);
    };

    const onDragEnd = (evt: CustomEvent) => {
      emit('drag-end', evt);
    };

    const onDragAdd = (evt: CustomEvent) => {
      emit('drag-add', evt);
    };

    const onCheckMove = throttle((evt: any) => {
      emit('drag-move', evt);
      // 阻止编组嵌套拖拽
      if (evt.from && evt.to && evt.from !== evt.to && evt.draggedContext.element.type === GraphType.Group) {
        return false;
      }
    }, 100);

    const isDragTargetParent = (node: Group) => {
      return props.dragTargetParentId === node.id;
    };
    // #endregion

    // #region 选中高亮 - 支持多选
    const isActivated = (node: Page | Group) => props.activePageId === node.id;
    const isSelected = (node: Page | Group) => props.selectedIds.includes(node.id);
    const onSelect = (node: Page | Group, $event: Event) => {
      emit('update:selected-ids', node, $event);
    };
    // 转发id
    const onEmitSelected = (node: Page | Group, $event: Event) => {
      emit('update:selected-ids', node, $event);
    };
    // #endregion

    return {
      onDragStart,
      onDragEnd,
      onDragAdd,
      onCheckMove,
      isExpanded,
      toggleExpand,
      onEmitExpanded,
      isActivated,
      isSelected,
      onSelect,
      onEmitSelected,
      isEditing,
      isDisabledDrag,
      editingName,
      editInputRef,
      onDoubleClick,
      onSaveName,
      onCancelEdit,
      isDragTargetParent
    };
  }
});
