import { DEF_ROUTES, createSpaceRoutes, useAppGuard, useSpaceGuard, useTokenGuard } from '@hetu/platform-shared';
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  ...createSpaceRoutes({
    path: '/:id',
    name: 'preview',
    props: true,
    component: () => import('../index.vue'),
    meta: {
      title: '设计预览',
      spaceRoot: false,
      canActivate: [useTokenGuard, useSpaceGuard, useAppGuard]
    }
  }),
  ...DEF_ROUTES
];

export default routes;
