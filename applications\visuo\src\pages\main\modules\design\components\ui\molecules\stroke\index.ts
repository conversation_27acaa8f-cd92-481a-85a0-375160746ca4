import { computed, defineComponent, ref, type PropType, watch, onMounted } from 'vue';
import { StrokeAlign, StrokeType, Stroke, FillType } from '@vis/document-core';

/**
 * 描边
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-stroke',
  props: {
    modelValue: {
      type: Object as PropType<Stroke>
    },
    // 显示标题
    showTitle: {
      type: Boolean,
      default: false
    },
    minusWidth: {
      type: Number,
      default: 0
    }
  },
  setup(props, { emit }) {
    const computedStroke = computed({
      get() {
        if (!props.modelValue) {
          return new Stroke();
        }
        return props.modelValue;
      },
      set(value) {
        Object.assign({}, props.modelValue, value);
      }
    });
    const alignOptions = [
      { label: '居中', value: StrokeAlign.Center },
      { label: '内部', value: StrokeAlign.Inside },
      { label: '外部', value: StrokeAlign.Outside }
    ];

    const lineOptions = [
      { label: '实线', value: StrokeType.Solid, style: 'solid' },
      { label: '虚线', value: StrokeType.Dashed, style: 'dashed' },
      { label: '点线', value: StrokeType.Dotted, style: 'dotted' }
    ];

    // 非内部描边和渐变色描边只支持实线
    const styleDisabled = computed(() => {
      return computedStroke.value.align !== StrokeAlign.Inside || computedStroke.value.paints.type !== FillType.Solid;
    });

    const positionIndex = ref(4);

    // 修改线宽
    const handlePositionChange = (value: number) => {
      if (positionIndex.value === 4 || positionIndex.value === 5) {
        computedStroke.value.position = [value, value, value, value];
      } else {
        // 将其他元素设为0，只设置当前选中位置的值
        computedStroke.value.position = [0, 0, 0, 0];
        computedStroke.value.position[positionIndex.value] = value;
      }
    };

    watch(
      [() => props.modelValue?.align, () => computedStroke.value.paints.type],
      () => {
        if (styleDisabled.value) {
          computedStroke.value.style = StrokeType.Solid;
        }
      },
      { immediate: true }
    );

    // 描边
    const strokeOptions = [
      { label: '全部', icon: 'border-a', value: 4, active: true },
      { label: '顶部', icon: 'border-t', value: 0, active: false },
      { label: '底部', icon: 'border-b', value: 2, active: false },
      { label: '左侧', icon: 'border-l', value: 3, active: false },
      { label: '右侧', icon: 'border-r', value: 1, active: false }
    ];

    const initStrokeOptions = () => {
      const positions = computedStroke.value.position;
      strokeOptions.forEach((item) => (item.active = false));

      const nonZeroCount = positions.filter((pos) => pos > 0).length;
      const isAllEqual = nonZeroCount === 4 && positions.every((pos) => pos === positions[0]);

      let targetIndex = 4;

      if (nonZeroCount === 1) {
        targetIndex = positions.findIndex((pos) => pos > 0);
      } else if (nonZeroCount > 1 && !isAllEqual) {
        targetIndex = 5;
      }

      const option = strokeOptions.find((item) => item.value === targetIndex);
      if (option) {
        option.active = true;
        strokeIcon.value = option.icon;
      } else if (targetIndex === 5) {
        strokeIcon.value = 'border-a';
      }

      positionIndex.value = targetIndex;
    };

    onMounted(() => {
      initStrokeOptions();
    });

    const strokeIcon = ref('border-a');

    const handleStrokeChange = (item: { icon: string; value: number; active: boolean }) => {
      strokeOptions.forEach((item) => (item.active = false));
      item.active = true;
      positionIndex.value = item.value;
      const position = computedStroke.value.position.find((item) => item !== 0);
      handlePositionChange(position || 0);
      strokeIcon.value = item.icon || 'border-a';
    };

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    return {
      computedStroke,
      alignOptions,
      lineOptions,

      styleDisabled,

      positionIndex,
      handlePositionChange,

      strokeIcon,
      strokeOptions,
      handleStrokeChange,

      popupRef,
      popupShow,
      showPopup
    };
  }
});
