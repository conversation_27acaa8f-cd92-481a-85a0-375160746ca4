<template>
  <div class="vis-link-param w-full">
    <div v-for="(item, index) in computedModel" :key="index" class="vis-form-field__content q-mb-sm">
      <div class="flex vis-fill__content flex-1">
        <!-- 设置参数类型 -->
        <q-btn flat dense>
          <ht-icon class="vis-icon" :name="item.type === 'fix' ? 'vis-control' : 'vis-navigation-c'" />
          <q-menu v-model="showMenuType[index]" style="width: 150px" class="vis-menu" dense>
            <q-list dense>
              <q-item :active="item.type === 'fix'" @click="changeType('fix', index)" clickable>
                <q-item-section avatar>
                  <ht-icon name="vis-control" />
                </q-item-section>
                <q-item-section>固定参数</q-item-section>
              </q-item>
              <q-item :active="item.type === 'param'" @click="changeType('param', index)" clickable>
                <q-item-section avatar>
                  <ht-icon name="vis-navigation-c" />
                </q-item-section>
                <q-item-section>全局参数</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
        <q-separator vertical class="!m-0" />
        <!-- 输入参数名 -->
        <q-input v-model="item.name" placeholder="参数名" borderless class="rounded-borders flex-1 px-2"></q-input>
        <q-separator vertical class="!m-0" />
        <!-- 输入/选择参数值 -->
        <q-input
          v-if="item.type === 'fix'"
          v-model="item.value"
          placeholder="参数值"
          borderless
          class="rounded-borders flex-1 px-2"
        />
        <vis-select
          v-if="item.type === 'param'"
          v-model="item.value"
          :options="paramOptions"
          class="flex-1 no-outline"
          placeholder="参数值"
        >
          <template #no-option>
            <q-item>暂无全局参数</q-item>
          </template>
        </vis-select>
      </div>
      <!-- 删除按钮 -->
      <q-btn flat dense @click="onDelParam(index)">
        <ht-icon name="vis-subtract" />
      </q-btn>
    </div>

    <div class="add-param w-full flex">
      <q-btn :ripple="false" @click="onAddParam" class="add-btn !w-full">
        <ht-icon name="vis-add" class="vis-icon">添加参数</ht-icon>
      </q-btn>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
