import { ROUTE_DOMAIN_KEY, SpaceService } from '@hetu/platform-shared';
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';

export const useDomain = () => {
  const route = useRoute();

  // 当前空间标识
  const domain = ref<string | undefined>(SpaceService.getDomainCode());
  if (route.params[ROUTE_DOMAIN_KEY]) {
    watch(
      () => route.params[ROUTE_DOMAIN_KEY],
      () => (domain.value = SpaceService.getDomainCode())
    );
  }

  return domain;
};
