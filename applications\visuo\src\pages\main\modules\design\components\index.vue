<template>
  <q-layout view="hHh lpR fFf" class="vis-design">
    <q-drawer class="flex h-full" show-if-above :width="leftWidth" side="left">
      <vis-design-store class="flex-1" />
      <q-separator vertical />
    </q-drawer>
    <q-drawer class="flex h-full" show-if-above :width="rightWidth" side="right">
      <q-separator vertical />
      <vis-design-config class="flex-1" />
    </q-drawer>

    <q-page-container>
      <q-page class="vis-design-center">
        <vis-design-ruler />
        <vis-design-selecto />
        <draggable
          class="canvas-warp"
          v-show="dropbox"
          :list="libraryList"
          :group="{ name: 'widget-component', pull: false }"
          :sort="false"
          item-key="id"
          @add="addWidgetBlock($event)"
        >
          <template #item="{ element }">
            <span :attr="element"></span>
          </template>
        </draggable>
        <vis-design-infinite-viewer>
          <vis-design-canvas />
        </vis-design-infinite-viewer>

        <div class="empty" v-if="!page.children.length">
          <div class="empty-text">
            使用快捷键 F ,创建容器
            <div>或选择下方提供的容器尺寸</div>
          </div>
          <div class="empty-card">
            <q-card
              class="column"
              v-for="(group, index) in prepareSize"
              :key="index"
              flat
              @click="addFrameBySize(group.size[0])"
            >
              <q-icon :name="group.icon" />
              <q-card-section class="card-text" @click.stop>
                {{ group.text }}<q-icon size="12px" name="expand_more" />
                <q-menu dense style="width: 250px" class="vis-menu !max-h-none">
                  <q-list dense>
                    <q-item
                      v-for="(size, i) in group.size"
                      :key="i"
                      @click="addFrameBySize(size)"
                      v-close-popup
                      clickable
                    >
                      <q-item-section>{{ size.text }}</q-item-section>
                      <q-item-section side>{{ size.width }} × {{ size.height }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </q-card-section>
              <q-card-section class="!py-0 text-gray-500 w-26 text-center">
                {{ group.size[0].width }} x {{ group.size[0].height }}
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-page>
    </q-page-container>
    <vis-design-dock />
  </q-layout>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
