import { defineComponent, ref, computed, watch, nextTick } from 'vue';
import Moveable, {
  makeAble,
  type MoveableManagerInterface,
  type OnDrag,
  type OnDragEnd,
  type OnDragGroup,
  type OnDragGroupEnd,
  type OnDragGroupStart,
  type OnDragOrigin,
  type OnDragStart,
  type OnRender,
  type OnResize,
  type OnResizeEnd,
  type OnResizeGroupEnd,
  type OnResizeStart,
  type OnRotate,
  type OnRotateEnd,
  type OnRotateGroupEnd,
  type OnRound,
  type OnRoundEnd,
  type OnRoundGroupEnd,
  type OnScroll,
  type RectInfo,
  type Renderer
} from 'vue3-moveable';
import { useActionStore, useDesignStore } from '../../../../stores';
import { VueSelecto } from 'vue3-selecto';
import { VIS_DESIGN_INFINITE_CANVAS } from '../../../../models';
import { useConstraints, useGraph, useGraphMoveable, type RectPosition } from '../../../../hooks';
import { getCssVar } from 'quasar';
import {
  DirectionType,
  Frame,
  Graph,
  GRAPH_SIZE_MIN,
  GraphType,
  ResizeType,
  TextAdapt,
  useLayout
} from '@vis/document-core';
import type { Records } from '@hetu/util';
import isNumber from 'lodash-es/isNumber';

export default defineComponent({
  name: 'vis-design-moveable',
  components: {
    Moveable,
    VueSelecto
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();

    const actionStore = useActionStore();
    const actions = actionStore.actions;

    const {
      findGraph,
      graphBrothers,
      moveGraphs,
      formatRotate,
      foramtRadius,
      getFrameByPosition,
      flattenPageFrames,
      getCanvasPosSize,

      handGridItemPosition,
      getRowColByPositions,
      moveGraphToGrid
    } = useGraph();
    const active = computed(() => designStore.active.value);
    const activePage = computed(() => active.value.page);
    const activeFrame = computed(() => active.value.frame);
    const activeGraphIds = computed(() => active.value.graphIds);
    const activeGraphs = computed(() => active.value.graphs);
    const activeGraph = computed(() => active.value.graph);
    const canvasState = computed(() => designStore.canvasState.value);

    const infiniteCanvasRef = designStore.infiniteCanvasRef;

    const moveableRef = ref<Moveable>();

    // moveable的拖拽目标
    const moveableTargets = designStore.moveableTargets;

    const { isGrid, isFreeform, isFlex } = useLayout();
    const { guideLineStyles, getRectPosition, calcGraphPosition, setChildrenPosition } = useConstraints();
    const { getGraphGridDirections, getGraphGridEdges, onResizeFlexGrid, onResizeEndGrid } = useGraphMoveable();

    //#region 自定义块

    // 自定义提示大小
    const dimensionViewable = {
      name: 'dimensionViewable',
      props: [],
      events: [],
      render(moveable: MoveableManagerInterface, render: Renderer) {
        const rect = moveable.getRect();
        let width: number | string = Math.round(rect.offsetWidth);
        let height: number | string = Math.round(rect.offsetHeight);
        let grid = '';
        const id = moveable.props.target?.id;
        if (id) {
          const graph = activeGraphs.value.find((g) => g.id === id);
          if (graph) {
            switch (graph.limitSize.width.resize) {
              case ResizeType.Adapt:
                width = width + ' 适应';
                break;
              case ResizeType.Fill:
                width = width + ' 充满';
                break;
            }

            switch (graph.limitSize.height.resize) {
              case ResizeType.Adapt:
                height = height + ' 适应';
                break;
              case ResizeType.Fill:
                height = height + ' 充满';
                break;
            }

            if (graph.gridItem) {
              const row = graph.gridItem.rows[1] - graph.gridItem.rows[0];
              const col = graph.gridItem.columns[1] - graph.gridItem.columns[0];
              grid = `${row} x ${col}`;
            }
          }
        }

        return render.createElement(
          'div',
          {
            key: 'dimension-viewer',
            className: 'vis-moveable-dimension',
            style: {
              position: 'absolute',
              left: `${rect.width / 2}px`,
              top: `${rect.height + 5}px`,

              fontSize: '12px',
              whiteSpace: 'nowrap',
              fontWeight: 'bold',
              willChange: 'transform',
              transform: `translate(-50%, 0px)`
            }
          },
          [
            '\n        ',
            render.createElement(
              'span',
              {
                style: {
                  background: getCssVar('primary'),
                  borderRadius: '2px',
                  padding: '2px 4px',
                  color: 'white'
                }
              },
              ['\n            ', width, ' x ', height, '\n        ']
            ),
            grid
              ? render.createElement(
                  'span',
                  {
                    style: {
                      background: getCssVar('primary'),
                      borderRadius: '2px',
                      padding: '2px 4px',
                      color: 'white',
                      marginLeft: '5px'
                    }
                  },
                  ['\n            ', grid, '\n        ']
                )
              : null
          ]
        );
      }
    };

    // 自定义约束辅助线
    const customRotation = makeAble('customRotation', {
      render(moveable, react) {
        const rect = moveable.getRect() as RectInfo;
        const { pos1, pos2 } = moveable.state;

        const isDragging = moveable.isDragging();

        const id = moveable.props.target?.id;
        if (!id || isDragging) {
          return;
        }
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (!graph || !graph.parent || !activeFrame.value || !graph.constraints) {
          return;
        }

        const lines = guideLineStyles(graph) || [];

        return react.createElement(
          'div',
          {
            key: 'custom-rotation',
            className: 'vis-constraints-guide',
            style: {
              position: 'absolute'
            }
          },
          [
            '\n        ',
            ...lines.map((l) =>
              react.createElement('div', {
                className: 'hand',
                style: l
              })
            )
          ]
        );
      }
    });

    //#endregion

    //#region 改变位置、大小、旋转等事件

    // 是否允许拖拽组件
    const isDraggable = computed(() => {
      // dock中frame和textbox选中时不可拖拽
      if (actions.value.frame.active || actions.value.textbox.active) {
        return false;
      }
      // 按下command或ctrl时不可拖拽
      if (actionStore.isMetaCtrl.value) {
        return false;
      }
      return true;
    });

    // 方向控制器
    //  n:上  s: 下  e: 右 w: 左 nw:左上 ne:右上 se:右下 sw:左下
    const renderDirections = computed(() => {
      let directions: string[] = ['nw', 'ne', 'se', 'sw'];
      // 网格布局
      if (
        activeFrame.value &&
        isGrid(activeFrame.value) &&
        activeGraph.value &&
        !activeGraph.value.ignoreAutoLayout &&
        activeGraph.value.gridItem
      ) {
        directions = getGraphGridDirections(activeGraph.value);
      }
      return directions;
    });

    /**
     * 是否可以通过边缘缩放和调整大小  n:上  s: 下  e: 右 w: 左
     * grid布局根据对齐方式调整可拖拽的边
     */
    const edge = computed(() => {
      let directions = ['n', 's', 'e', 'w'];
      // 网格布局
      if (
        activeFrame.value &&
        isGrid(activeFrame.value) &&
        activeGraph.value &&
        !activeGraph.value.ignoreAutoLayout &&
        activeGraph.value.gridItem
      ) {
        directions = getGraphGridEdges(activeGraph.value);
      }
      return directions;
    });

    /**
     * 拖拽组件
     * @param e
     * @param container
     */
    const onDrag = (e: OnDrag) => {
      const id = e.target.id;
      const graph = findGraph(id);
      if (graph) {
        // 存储当前拖拽的id
        canvasState.value.dragging = [graph.id];
        const x = Math.round(e.translate[0]);
        const y = Math.round(e.translate[1]);
        graph.transform.translate = [x, y];

        // 基于整个画布的鼠标位置
        const rect = getCanvasPosSize(e.clientX, e.clientY);
        // 计算拖动时要放置的容器
        const frame = getFrameByPosition(rect.x, rect.y);
        designStore.canvasState.value.frame = frame;

        // 父容器是flex布局时拖拽时添加虚影,排除图形忽略了自动布局
        if (graph.parent && !graph.ignoreAutoLayout && activeFrame.value) {
          if (isFlex(activeFrame.value)) {
            const target = e.target as HTMLElement;
            const left = target.offsetLeft;
            const top = target.offsetTop;

            let ghostDom = document.querySelector('.vis-flex-item-ghost-dragging') as HTMLElement;
            if (ghostDom) {
              ghostDom.style = `left: ${left + x}px;top:${top + y}px;  width: ${e.width}px; height: ${e.height}px;`;
            } else {
              const parentDom = document.querySelector(`[id="${graph.parent}"]`) as HTMLElement;
              ghostDom = document.createElement('div');
              ghostDom.classList.add('vis-flex-item-ghost-dragging');
              ghostDom.style = `left: ${left + x}px;top:${top + y}px; width: ${e.width}px; height: ${e.height}px;`;

              parentDom?.appendChild(ghostDom);
            }
          }
        }

        if (frame) {
          // 非绝对定位
          if (isGrid(frame) && !graph.ignoreAutoLayout) {
            //activeFrame有值说明是在容器内拖拽移动，无值说明是从外部拖拽到容器内
            if (!activeFrame.value || activeFrame.value.id !== frame.id) {
              handGridItemPosition(frame);
            }
            // 计算鼠标在frame中的哪个格子里
            canvasState.value.gridRowCol = getRowColByPositions(frame, rect.x, rect.y, graph);
          }
        }
      }
    };

    const onDragStart = (e: OnDragStart) => {
      const target = e.target as HTMLElement;
      const id = target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        // 父级是grid布局时，拖动的时候使用transform，停止的时候使用gird
        if (activeFrame.value && isGrid(activeFrame.value)) {
          e.set(graph.transform.translate);
        }

        //算拖拽的图形能放置在哪个容器内： 开始拖拽时计算所有的容器的位置
        flattenPageFrames(activeGraphIds.value);

        // 非绝对定位
        if (!graph.ignoreAutoLayout && activeFrame.value && isGrid(activeFrame.value)) {
          // 计算activeFrame为grid布局时每个格子的位置和大小，用于拖拽时计算应该放置到哪个格子里
          handGridItemPosition(activeFrame.value);
        }
      }
    };

    /**
     * 拖拽结束
     * @param e
     * @returns
     */
    const onDragEnd = (e: OnDragEnd) => {
      // console.log('onDragEnd: ', e);
      const { isDrag, lastEvent, target } = e;
      const id = target.id;
      canvasState.value.dragging = [];
      if (!id || !isDrag || !lastEvent) {
        return;
      }

      const graph = activeGraphs.value.find((g) => g.id === id);
      if (!graph) {
        return;
      }

      // 拖拽结束删除移动是的虚影,仅限flex布局和grid布局时移动内部元素
      const ghostDom = document.querySelector('.vis-flex-item-ghost-dragging') as HTMLElement;
      if (ghostDom) {
        // 用虚影的位置设置图形的位置
        graph.transform.translate = [parseInt(ghostDom.style.left), parseInt(ghostDom.style.top)];
        document.querySelector('.vis-flex-item-ghost-dragging')?.remove();
      }

      const frame = designStore.canvasState.value.frame;
      const frameId = frame ? frame.id : VIS_DESIGN_INFINITE_CANVAS;
      if (graph.parent !== frameId) {
        // 移动到frameId容器里
        moveGraphs([graph], frameId, 0);
      } else {
        // 在容器中移动(非绝对定位) 1. grid布局时，移动到格子里
        if (!graph.ignoreAutoLayout && frame && isGrid(frame) && canvasState.value.gridRowCol) {
          moveGraphToGrid(graph, canvasState.value.gridRowCol);
        }
      }

      // 拖拽结束清空存储的数据
      designStore.canvasState.value.frame = undefined;

      canvasState.value.gridRowCol = undefined;
    };

    /** 保持纵横比 */
    const isKeepRatio = computed(() => designStore.active.value.graph?.aspectRatio);

    // 容器内子元素固定不变的值
    let rectPositions: Records<RectPosition> = {};

    /**
     * direction
     *  上：[0, -1] 下：[0, 1]左：[-1, 0] 右：[1, 0]
     *  左上：[-1, -1] 右上：[1, -1] 左下：[-1, 1] 右下：[1, 1]
     * @param e
     */
    const onResizeStart = (e: OnResizeStart) => {
      console.log('onResizeStart');
      // 容器内子元素设置约束后，容器改变尺寸开始时，计算子元素不变的值
      const id = e.target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        canvasState.value.resizeing = [id];
        // 父级是grid布局时，拖动的时候使用transform，停止的时候使用gird,初始拖动的时候translate是0，这里set下初始值
        if (activeFrame.value && isGrid(activeFrame.value)) {
          (e.dragStart as OnDragStart).set(graph.transform.translate);
        }
        rectPositions = getRectPosition(graph);

        // 当图形设置了其他适配方式时，调整尺寸后，变为固定尺寸
        if (e.direction[0] === 0) {
          // 调整的是高度
          graph.limitSize.height.resize = ResizeType.Fixed;
        } else if (e.direction[1] === 0) {
          // 调整的是宽度
          graph.limitSize.width.resize = ResizeType.Fixed;
        } else {
          // 调整的是宽高
          graph.limitSize.height.resize = ResizeType.Fixed;
          graph.limitSize.width.resize = ResizeType.Fixed;
        }

        // 宽高变化时处理最大、最小限制
        const max = [
          isNumber(graph.limitSize.width.max) ? graph.limitSize.width.max : 'auto',
          isNumber(graph.limitSize.height.max) ? graph.limitSize.height.max : 'auto'
        ];

        e.setMax(max);

        const min = [
          isNumber(graph.limitSize.width.min) ? graph.limitSize.width.min : 'auto',
          isNumber(graph.limitSize.height.min) ? graph.limitSize.height.min : 'auto'
        ];

        e.setMin(min);

        // 重置展示模式
        if (graph.text?.adapt) {
          graph.text.adapt = [TextAdapt.Single, TextAdapt.Auto].includes(graph.text.adapt)
            ? TextAdapt.Fixed
            : graph.text.adapt;
        }
      }
    };

    const onResizeEnd = (e: OnResizeEnd) => {
      const id = e.target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        // 在容器中
        if (activeFrame.value && graph.parent) {
          // 容器是网格布局时，当改变了graph的大小时，在此计算grid-row-end和grid-column-end
          if (isGrid(activeFrame.value) && graph.gridItem) {
            onResizeEndGrid(e, graph, activeFrame.value);
          }
        }
      }
      // resize 结束时，清空值
      rectPositions = {};
      canvasState.value.resizeing = [];
    };

    const onResize = (e: OnResize) => {
      //  console.log(e.direction, e);
      let width = e.width;
      let height = e.height;

      // 限制大小
      if (e.width < GRAPH_SIZE_MIN) {
        width = GRAPH_SIZE_MIN;
      }
      if (e.height < GRAPH_SIZE_MIN) {
        height = GRAPH_SIZE_MIN;
      }

      const id = e.target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        width = Math.round(width);
        height = Math.round(height);

        const x = Math.round(e.drag.translate[0]);
        const y = Math.round(e.drag.translate[1]);

        // 1.根级图形 2.自由布局
        if (!graph.parent || (activeFrame.value && isFreeform(activeFrame.value))) {
          graph.width = width;
          graph.height = height;
          graph.transform.translate = [x, y];

          e.target.style.width = `${width}px`;
          e.target.style.height = `${height}px`;
          e.target.style.transform = e.drag.transform;
        } else {
          if (activeFrame.value) {
            // 父级是flex布局或grid布局
            if (isFlex(activeFrame.value) || isGrid(activeFrame.value)) {
              onResizeFlexGrid(e, graph, activeFrame.value);
            }
          }
        }

        // 重新计算frame内子元素的位置及大小，不处理约束
        // graph是flex、grid布局，改变了graph的尺寸，需要计算子元素的位置和大小
        graph.type === GraphType.Frame && setChildrenPosition(graph as Frame);

        // 约束：容器内子元素设置约束后，容器改变尺寸开始时，子元素位置大小变化
        // 1. 自由布局内会设置约束；2.自动布局内子图形设置了忽略自由布局也会有约束
        calcGraphPosition(graph, rectPositions);
      }
    };

    const onRender = (e: OnRender) => {
      // console.log('onRender');
      // e.target.style.transform = e.transform;
    };

    // 组件尺寸大于100 * 100 时，才改变圆角
    const isRoundable = computed(() => {
      const activeGraphs = designStore.active.value.graphs;
      if (activeGraphs.length === 1) {
        const graph = activeGraphs[0];
        return graph.width > 100 && graph.height > 100;
      } else {
        return true;
      }
    });
    /**
     * 改变圆角事件
     * @param e
     */
    const onRound = (e: OnRound) => {
      const id = e.target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        graph.radius = foramtRadius(e.horizontals);
      }
    };

    /**
     * 图形旋转事件
     * @param e
     */
    const onRotate = (e: OnRotate) => {
      const id = e.target.id;
      const graph = activeGraphs.value.find((g) => g.id === id);
      if (graph) {
        graph.transform.rotate = formatRotate(e.rotation);
      }
    };

    /**
     * 旋转中心点移动事件
     * @param e
     */
    const onDragOrigin = (e: OnDragOrigin) => {
      // e.target.style.transformOrigin = e.transformOrigin;
      // console.log('onDragOrigin:', e);
    };

    //#endregion

    //#region group事件

    // 记录图形移动之前的位置
    let graphsOldTranslate: Records<number[]> = {};

    /**
     * 多选拖拽移动
     * @param e
     */
    const onDragGroup = (e: OnDragGroup) => {
      // 设置选中图形的位置
      const graphs: Graph[] = [];
      e.events.forEach((ev: OnDrag) => {
        const id = ev.target.id;
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (graph) {
          graph.transform.translate = [Math.round(ev.translate[0]), Math.round(ev.translate[1])];
          graphs.push(graph);
        }
      });

      // 鼠标位置
      const rect = getCanvasPosSize(e.clientX, e.clientY);
      // 计算拖动时要放置的容器
      const frame = getFrameByPosition(rect.x, rect.y);
      designStore.canvasState.value.frame = frame;

      // 父容器是flex、grid布局时拖拽时添加虚影
      if (graphs[0].parent && activeFrame.value) {
        if (activeFrame.value.autoLayout.direction !== DirectionType.Freeform) {
          const target = e.targets[0] as HTMLElement;
          const left = target.offsetLeft;
          const top = target.offsetTop;
          const x = e.translate[0];
          const y = e.translate[1];
          console.log(left + x, top + y, e.targets);
          let ghostDom = document.querySelector('.vis-flex-item-ghost-dragging') as HTMLElement;
          if (ghostDom) {
            ghostDom.style = `left: ${left + x}px;top:${top + y}px;  width: ${e.width}px; height: ${e.height}px;`;
          } else {
            const parentDom = document.querySelector(`[id="${graphs[0].parent}"]`) as HTMLElement;
            ghostDom = document.createElement('div');
            ghostDom.classList.add('vis-flex-item-ghost-dragging');
            ghostDom.style = `left: ${left + x}px;top:${top + y}px; width: ${e.width}px; height: ${e.height}px;`;

            parentDom?.appendChild(ghostDom);
          }
        }
      }
    };

    /**
     * 多选拖拽移动开始
     * @param e
     */
    const onDragGroupStart = (e: OnDragGroupStart) => {
      //算拖拽的图形能放置在哪个容器内： 开始拖拽时计算所有的容器的位置
      flattenPageFrames(activeGraphIds.value);

      //flex布局内多选移动， 拖拽开始记录拖拽之前图形的位置
      if (activeFrame.value && activeFrame.value.autoLayout.direction !== DirectionType.Freeform) {
        const padding = activeFrame.value.autoLayout.padding;
        e.events.forEach((ev: OnDragStart) => {
          const id = ev.target.id;
          const graph = activeGraphs.value.find((g) => g.id === id);
          if (graph) {
            graphsOldTranslate[id] = [
              graph.transform.translate[0] - padding[3],
              graph.transform.translate[1] - padding[0]
            ];
          }
        });
      }
    };
    /**
     * 选框移动结束事件
     * @param e
     */
    const onDragGroupEnd = (e: OnDragGroupEnd) => {
      const { isDrag } = e;

      if (!isDrag) {
        return;
      }

      const graphs: Graph[] = [];
      e.events.forEach((ev: OnDragEnd) => {
        const id = ev.target.id;
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (graph) {
          graphs.push(graph);
        }
      });

      if (!graphs.length) {
        return;
      }

      // 拖拽结束删除移动是的虚影,仅限flex布局和grid布局时移动内部元素
      const ghostDom = document.querySelector('.vis-flex-item-ghost-dragging') as HTMLElement;
      if (ghostDom) {
        // 用虚影的位置+图形之前的位置计算出图形移动后的位置
        graphs.forEach((g) => {
          g.transform.translate[0] = graphsOldTranslate[g.id][0] + parseInt(ghostDom.style.left);
          g.transform.translate[1] = graphsOldTranslate[g.id][1] + parseInt(ghostDom.style.top);
        });
        console.log('onDragGroupEnd:', ghostDom.style.left, ghostDom.style.top);
        document.querySelector('.vis-flex-item-ghost-dragging')?.remove();
        graphsOldTranslate = {};
      }

      // 框选移动
      const frame = designStore.canvasState.value.frame;
      const frameId = frame ? frame.id : VIS_DESIGN_INFINITE_CANVAS;
      if (graphs[0].parent !== frameId) {
        moveGraphs(graphs, frameId, 0);
      }

      // 拖拽结束清空存储的数据
      designStore.canvasState.value.frame = undefined;
    };

    /**
     * 选框改变尺寸结束事件
     * @param e
     */
    const onResizeGroupEnd = (e: OnResizeGroupEnd) => {
      e.events.forEach((ev: OnResizeEnd) => {
        const id = ev.target.id;
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (graph) {
          let width = ev.lastEvent.width;
          let height = ev.lastEvent.height;
          // 限制大小
          if (width < GRAPH_SIZE_MIN) {
            width = GRAPH_SIZE_MIN;
          }
          if (height < GRAPH_SIZE_MIN) {
            height = GRAPH_SIZE_MIN;
          }

          graph.width = Math.round(width);
          graph.height = Math.round(height);
          graph.transform.translate = [
            Math.round(ev.lastEvent.drag.translate[0]),
            Math.round(ev.lastEvent.drag.translate[1])
          ];
        }
      });
    };

    /**
     * 选框旋转结束事件
     * @param e
     */
    const onRotateGroupEnd = (e: OnRotateGroupEnd) => {
      designStore.canvasState.value.isRotate = false;
      e.events.forEach((ev: OnRotateEnd) => {
        const id = ev.target.id;
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (graph) {
          graph.transform.rotate = formatRotate(ev.lastEvent.rotation);
          graph.transform.translate = [
            Math.round(ev.lastEvent.drag.translate[0]),
            Math.round(ev.lastEvent.drag.translate[1])
          ];
        }
      });
    };

    /**
     * 选框改变圆角结束事件
     * @param e
     */
    const onRoundGroupEnd = (e: OnRoundGroupEnd) => {
      e.events.forEach((ev: OnRoundEnd) => {
        const id = ev.target.id;
        const graph = activeGraphs.value.find((g) => g.id === id);
        if (graph) {
          graph.radius = foramtRadius(e.lastEvent.horizontals);
        }
      });
    };

    //#endregion

    //#region 拖拽时画布滚动
    const scrollOptions = {
      container: () => infiniteCanvasRef.value?.getContainer(),
      threshold: 20,
      getScrollPosition: () => {
        return [
          infiniteCanvasRef.value?.getScrollLeft({ absolute: true }),
          infiniteCanvasRef.value?.getScrollTop({ absolute: true })
        ];
      }
    };
    const onScroll = ({ direction }: OnScroll) => {
      infiniteCanvasRef.value?.scrollBy(direction[0] * 10, direction[1] * 10);
    };

    //#endregion

    let controlBox: HTMLElement;
    /**
     * 改变选中图形时触发
     * @param e
     */
    const onChangeTargets = (e: any) => {
      // controlBox在多选后会重复添加，在这里把多余的删除掉
      if (controlBox !== e.currentTarget.getControlBoxElement()) {
        controlBox?.remove();
        controlBox = e.currentTarget.getControlBoxElement();
        controlBoxAddClass();
      }
    };

    const controlBoxAddClass = () => {
      // 由于旋转手柄的div上没有添加位置的class，css无法匹配，所以添加一个class
      const rotation = controlBox.querySelectorAll('.moveable-rotation');
      const handle = ['tl', 'tr', 'bl', 'br'];
      rotation?.forEach((el, index) => {
        el.classList.add(handle[index]);
      });
    };

    //#region  吸附相关 辅助线、辅助对齐元素等

    // 网格布局时网格辅助线
    const gridRowGuideLines = ref<number[]>([]);
    const gridColGuideLines = ref<number[]>([]);

    // 水平、垂直方向参考线集合 1. 默认为标尺的辅助线 2.当容器为网格布局时，参考线为网格线
    const horizontalGuidelines = computed(() => {
      if (activeFrame.value && isGrid(activeFrame.value)) {
        return gridRowGuideLines.value;
      }
      return activePage.value.guides.horizontal;
    });
    const verticalGuidelines = computed(() => {
      if (activeFrame.value && isGrid(activeFrame.value)) {
        return gridColGuideLines.value;
      }
      return activePage.value.guides.vertical;
    });

    // 吸附基准容器 1. 默认为null  2. 当容器为网格布局时，基准容器为所在的父容器,用于对齐网格
    const snapContainer = ref<HTMLElement | null>(null);

    // 辅助对齐元素集  对齐元素的规则：1. 与当前拖着组件的兄弟组件，并且不包含自身 2. 父组件
    const elementGuidelines = ref<HTMLElement[]>([]);

    // 吸附方向 只有自由布局时四周吸附
    const snapDirections = computed(() => {
      let directions: Records = { top: true, left: true, bottom: true, right: true, center: true, middle: true };
      if (activeFrame.value) {
        if (!isFreeform(activeFrame.value)) {
          directions = {};
        }
      }
      return directions;
    });

    watch(
      () => activeFrame.value?.id,
      () => {
        elementGuidelines.value = [];
        gridRowGuideLines.value = [];
        gridColGuideLines.value = [];
        snapContainer.value = null;
        if (activeGraphIds.value.length && activeGraphs.value.length) {
          // ------ elementGuidelines 辅助对齐元素 存在的情况：1.选中的图形为根级 2. 在frame里，frame是自由布局 ---------
          if (!activeFrame.value || (activeFrame.value && isFreeform(activeFrame.value))) {
            const parentId = activeGraphs.value[0].parent;
            // 找到选中组件的父级组件：框选时activeGraphs里的组件可能存在层级关系，这一步把内层组件去掉，只保留同一级的组件
            const actives = JSON.parse(JSON.stringify(activeGraphIds.value));
            activeGraphs.value.forEach((aw: Graph) => {
              if (activeGraphIds.value.includes(aw.parent)) {
                actives.splice(
                  actives.findIndex((aid: string) => aid === aw.id),
                  1
                );
              }
            });
            // 查找兄弟组件和父组件
            if (actives.length) {
              const activeId = actives[0];
              const brothers = graphBrothers(activeId);
              if (brothers.length) {
                brothers.forEach((g) => {
                  elementGuidelines.value.push(document.getElementById(g.id) as HTMLElement);
                });
              }
              parentId && elementGuidelines.value.push(document.getElementById(parentId) as HTMLElement);
            }
          }

          // ---------- gridRowGuideLines、gridColGuideLines、snapContainer  存在的情况：在frame里，frame是网格布局时 ----------
          // 1. 计算网格布局的吸附线的位置
          calcGridGuideLines();
        }
      },
      {
        deep: true
      }
    );

    watch(
      () => activeFrame.value?.autoLayout.gridSize,
      () => {
        nextTick(() => calcGridGuideLines());
      }
    );

    const calcGridGuideLines = () => {
      if (activeFrame.value && isGrid(activeFrame.value)) {
        snapContainer.value = document.getElementById(activeFrame.value.id) as HTMLElement;

        const gridItemDoms = document.querySelectorAll(`[id="${activeFrame.value.id}"] > .vis-frame-grid-ghost > div`);
        const rowIndexs: number[] = [];
        const colIndexs: number[] = [];
        // 行、列吸附线的位置
        const rows = new Set<number>();
        const cols = new Set<number>();

        gridItemDoms.forEach((item) => {
          const dom = item as HTMLElement;
          const rowI = parseInt(dom.getAttribute('data-row') as string);
          const colI = parseInt(dom.getAttribute('data-col') as string);
          // 减少计算只算一行列 一行行
          if (!rowIndexs.includes(rowI) || !colIndexs.includes(colI)) {
            const w = dom.offsetWidth;
            const h = dom.offsetHeight;
            const x = dom.offsetLeft;
            const y = dom.offsetTop;
            cols.add(x);
            cols.add(x + w);
            rows.add(y);
            rows.add(y + h);

            rowIndexs.push(rowI);
            colIndexs.push(colI);
          }
        });
        gridRowGuideLines.value = Array.from(rows);
        gridColGuideLines.value = Array.from(cols);
      }
    };

    //#endregion

    return {
      active,
      canvasState,

      moveableRef,
      moveableTargets,

      dimensionViewable,
      customRotation,

      elementGuidelines,
      verticalGuidelines,
      horizontalGuidelines,
      snapContainer,
      snapDirections,

      isDraggable,
      isRoundable,
      isKeepRatio,

      edge,
      renderDirections,

      onRender,
      onDrag,
      onDragStart,
      onDragEnd,

      onResize,
      onResizeStart,
      onResizeEnd,

      onRound,
      onRotate,

      onDragOrigin,

      onDragGroup,
      onDragGroupStart,
      onDragGroupEnd,
      onResizeGroupEnd,
      onRotateGroupEnd,
      onRoundGroupEnd,

      scrollOptions,
      onScroll,

      onChangeTargets
    };
  },
  mounted() {
    const designStore = useDesignStore();
    designStore.moveableRef.value = this.moveableRef;
  },
  methods: {}
});
