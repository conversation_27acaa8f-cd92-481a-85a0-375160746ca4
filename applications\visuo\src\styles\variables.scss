/* --------- 添加变量 ------------ */

$vis-prefix: vis;

// 变量前缀
$vis-design-prefix: '#{$vis-prefix}-design';

/* ------------ end -------------- */

/* --------- 主题样式 ------------ */

@mixin theme-background($prefix) {
  background-color: var(#{$prefix}-background-color);
  background-image: var(#{$prefix}-background-image);
  background-repeat: var(#{$prefix}-background-repeat);
  background-size: var(#{$prefix}-background-size);
}

@mixin theme-font($prefix) {
  color: var(#{$prefix}-color);
  font-family: var(#{$prefix}-font-family);
  font-size: var(#{$prefix}-font-size);
  font-weight: var(#{$prefix}-font-weight);
}

@mixin theme-border($prefix) {
  border-width: var(#{$prefix}-border-width);
  border-style: var(#{$prefix}-border-style);
  border-color: var(#{$prefix}-border-color);
}

/* --------- end ------------ */
