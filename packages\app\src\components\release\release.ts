import { defineComponent, ref, computed, type PropType, watch } from 'vue';
import { useQuasar, copyToClipboard, useDialogPluginComponent, QForm } from 'quasar';
import { useDomain, useQRCode } from '../../hooks';
import { ReleaseModel } from '../../models';
import { ReleaseService } from '../../services';
import { createQFormRules, passwordChar } from '@hetu/util';
import { TeamService, UserGroup, type Profile } from '@hetu/platform-shared';

type UserOption = { value: string; label: string; type: 0 | 1 };

export default defineComponent({
  name: 'ht-app-release',
  props: {
    value: {
      type: Object as PropType<ReleaseModel>,
      required: true
    },
    name: String,
    module: String,
    /** 是否显示token验证项 */
    tokenAuth: {
      type: Boolean,
      default: true
    },
    /** 是否显示验证参数项 */
    paramAuth: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const $q = useQuasar();
    const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

    const domain = useDomain();

    // 发布信息
    const model = ref(props.value);

    //#region 发布分享
    const enableRelease = ref(!!model.value.id);
    // 确认提示框
    const popoverVisible = ref(false);
    const releaseConfirm = (value: boolean) => {
      if (!value) {
        // 关闭前先确认
        popoverVisible.value = true;
        return;
      }

      ReleaseService.getUrl().then((data) => {
        if (data) {
          model.value.releaseUrl = data.releaseUrl;
          model.value.releaseCode = data.releaseCode;
          setReleaseUrl();
        }
      });
      toggleRelease();
    };
    const toggleRelease = () => {
      enableRelease.value = !enableRelease.value;
      popoverVisible.value = false;
      restrictionFormRef.value?.resetValidation();
    };
    // 分享链接
    const releaseUrl = ref('');
    const { qrcodeCanvasRef, renderQRCode, downloadQRCode } = useQRCode();
    const setReleaseUrl = () => {
      const path = [window.location.origin, window.location.pathname.replace('index.html', '')].join('');
      const url = [model.value.releaseUrl ? model.value.releaseUrl.replace('|webapp|', path) : path];
      url[0].endsWith('/') ? '' : url.push('/');
      url.push(props.module ? props.module + '/' : '', 'share/index.html');
      model.value.releaseCode ? url.push('#/', model.value.releaseCode) : '';

      releaseUrl.value = url.join('');
    };
    setReleaseUrl();
    watch(releaseUrl, () => renderQRCode(releaseUrl.value));
    //#endregion

    //#region 访问限制
    const restrictionFormRef = ref<QForm>();
    const restrictionFormDisable = computed(() => !enableRelease.value || !model.value.accessRestriction);
    const restrictionFormRules = createQFormRules<ReleaseModel>({
      accessPassword: [
        { required: true, message: '请输入密码' },
        { min: 8, type: 'string', message: '最小输入长度8' },
        {
          validator: (val: string) => {
            const result = passwordChar(val);
            return result ? result.message : true;
          }
        }
      ],
      accessTerm: [
        { required: true, message: '请输入时效' },
        { min: 0, type: 'number', message: '访问时效最小0' }
      ]
    });

    const accessRestrictionError = computed(
      () =>
        !!(
          enableRelease.value &&
          model.value.accessRestriction &&
          !(model.value.loginRestriction || passwordCheck.value || tokenCheck.value)
        )
    );
    const changeAccessRestriction = (value: boolean) => {
      !value && restrictionFormRef.value?.resetValidation();
    };

    // 登录验证
    const users = ref([...(model.value.userGroupIds || []), ...(model.value.memberIds || [])]);
    const userOptions = ref<UserOption[]>();
    const getUserOptions = async () => {
      if (userOptions.value) return;

      userOptions.value = [];
      const data = await Promise.all([TeamService.getGroups(), TeamService.getMembers()]);
      // 用户组
      data[0] &&
        data[0].forEach((item: UserGroup) => {
          userOptions.value!.push({ value: item.id, label: item.groupName, type: 0 });
        });
      // 成员
      data[1] &&
        data[1].forEach((item: Profile) => {
          userOptions.value!.push({ value: item.id, label: item.fullName, type: 1 });
        });
    };
    (users.value.length || model.value.loginRestriction) && getUserOptions();
    const toggleSelectUser = (selected: boolean, item: UserOption) => {
      const key = item.type ? 'memberIds' : 'userGroupIds';
      if (selected) {
        // 取消选中
        model.value[key]!.splice(
          model.value[key]!.findIndex((id) => id === item.value),
          1
        );
      } else {
        model.value[key] = model.value[key] ?? [];
        model.value[key]!.push(item.value);
      }
    };
    const clearUsers = () => {
      model.value.memberIds && (model.value.memberIds.length = 0);
      model.value.userGroupIds && (model.value.userGroupIds.length = 0);
    };
    const changeLoginRestriction = (value: boolean) => {
      if (value) {
        getUserOptions();

        passwordCheck.value = false;
        model.value.accessPassword = '';

        tokenCheck.value = false;
        model.value.accessToken = '';
        model.value.paramCheck = 0;

        termCheck.value = false;
        model.value.accessTerm = undefined;
      }

      restrictionFormRef.value?.resetValidation();
    };

    // 密码验证
    const passwordCheck = ref(!!model.value.accessPassword);
    model.value.accessPassword = model.value.accessPassword ?? '';
    const changePasswordCheck = () => {
      if (passwordCheck.value) {
        model.value.loginRestriction = 0;
      } else {
        model.value.accessPassword = '';
      }

      restrictionFormRef.value?.resetValidation();
    };

    // token 验证
    const tokenCheck = ref(!!model.value.accessToken);
    const changeTokenCheck = () => {
      if (tokenCheck.value) {
        model.value.loginRestriction = 0;
        ReleaseService.getToken().then((data) => {
          model.value.accessToken = data?.accessToken || '';
        });
      } else {
        model.value.accessToken = '';
        model.value.paramCheck = 0;
      }

      restrictionFormRef.value?.resetValidation();
    };

    // 验证时效
    const termCheck = ref(!!model.value.accessTerm);
    const changeTermCheck = () => {
      if (!termCheck.value) {
        model.value.accessTerm = undefined;
      } else {
        model.value.accessTerm = 0;
      }
    };
    //#endregion

    /** 复制到剪贴板 */
    const copy = async (text: string) => {
      try {
        await copyToClipboard(text);
        $q.notify({ message: '复制成功', position: 'top', type: 'positive' });
      } catch (error) {
        $q.notify({ message: '复制失败', position: 'top', type: 'negative' });
      }
    };

    const save = () => {
      if (accessRestrictionError.value) return;

      if (enableRelease.value) {
        restrictionFormRef.value?.validate().then((valid) => {
          if (!valid) return false;

          onDialogOK({ enable: enableRelease.value, model: model.value });
        });
      } else {
        onDialogOK({ enable: enableRelease.value, model: model.value });
      }
    };

    return {
      dialogRef,
      onDialogCancel,

      domain,
      model,

      enableRelease,
      popoverVisible,
      releaseConfirm,
      toggleRelease,

      releaseUrl,
      qrcodeCanvasRef,
      renderQRCode,
      downloadQRCode,

      restrictionFormRef,
      restrictionFormDisable,
      restrictionFormRules,

      accessRestrictionError,
      changeAccessRestriction,

      users,
      userOptions,
      toggleSelectUser,
      clearUsers,
      changeLoginRestriction,

      passwordCheck,
      changePasswordCheck,

      tokenCheck,
      changeTokenCheck,

      termCheck,
      changeTermCheck,

      copy,
      save
    };
  }
});
