<template>
  <draggable
    v-if="dataMapping[mapKey]?.length > 0"
    v-model="dataMapping[mapKey]"
    :item-key="getItemKey"
    :sort="true"
    tag="div"
    class="draggable-container"
    :animation="200"
    ghost-class="ghost"
    chosen-class="chosen"
    drag-class="drag"
    handle=".drag-handle"
  >
    <template #item="{ element, index }">
      <div class="field-item">
        <!-- 拖动手柄区域 -->
        <div class="drag-handle"></div>
        <q-btn :class="{ active: getConfigState(index) }">
          <ht-icon
            :name="`vis-${getFieldIcon(element)}`"
            class="vis-icon w-6"
            :class="{ 'vis-icon--active': getConfigState(index) }"
          ></ht-icon>
          <vis-field-config
            @before-show="toggleConfigState(index, true)"
            @before-hide="toggleConfigState(index, false)"
            @handleFilter="handleFilter(index)"
            @handleSort="handleSort(index, $event)"
          />
        </q-btn>
        <q-btn
          :ripple="false"
          class="field-btn"
          :class="{ active: getOpenState(index) }"
          @click="toggleOpenState(index, 'open')"
        >
          <span class="w-[calc(100%-24px)] text-left px-2">
            {{ element?.fieldName || element?.fieldAlias || '字段' }}
          </span>
          <q-icon class="!w-6" size="12px" :name="getOpenState(index) ? 'expand_less' : 'expand_more'" />
          <vis-field-list
            :fieldList="fieldMapping?.datasetField"
            :activeIds="[element.id]"
            :single="true"
            @closePopup="toggleOpenState(index, 'close')"
            @addField="selectField($event, index)"
          />
        </q-btn>
        <q-btn :ripple="false" class="remove-btn !w-6" @click="removeField(index)">
          <ht-icon name="vis-remove" class="vis-icon"></ht-icon>
        </q-btn>
      </div>
    </template>
  </draggable>
  <q-btn :ripple="false" class="add-field-btn">
    <ht-icon name="vis-add" class="vis-icon vis-icon--active w-6"></ht-icon>
    添加字段
    <vis-field-list
      :fieldList="fieldMapping?.datasetField"
      :activeIds="activeIds"
      :offset="[0, 8]"
      @closePopup="toggleOpenState(-1, 'close')"
      @addField="addField"
    />
  </q-btn>
</template>

<script lang="ts" src="./field-item.ts"></script>
