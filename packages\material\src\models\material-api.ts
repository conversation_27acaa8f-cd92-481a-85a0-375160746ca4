import { type HttpApi } from '@hetu/http';
import { API_PREFIX, SYSTEM_API_PREFIX } from '@hetu/platform-shared';

const API_HOST = `${API_PREFIX}${SYSTEM_API_PREFIX}/material`;

export class <PERSON><PERSON>pi implements HttpApi {
  list = `${API_HOST}`;
  save = `${API_HOST}/save`;
}

const GEO_API_HOST = `${API_PREFIX}${SYSTEM_API_PREFIX}/geo`;
export class MaterialGeoApi implements HttpApi {
  list = `${GEO_API_HOST}/list`;
  save = `${GEO_API_HOST}/save`;
  delete = `${GEO_API_HOST}/delete`;
  info = `${GEO_API_HOST}/info`;
  codeCheck = `${GEO_API_HOST}/code/check`;
  nameCheck = `${GEO_API_HOST}/name/check`;
  /** 启用禁用地理位置 */
  updateActive = `${GEO_API_HOST}/update/active`;
}
