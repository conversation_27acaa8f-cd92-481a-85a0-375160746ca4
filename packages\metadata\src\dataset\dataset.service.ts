import { responseData, responseReject, HttpApiService, type ResponseResult, responseResult } from '@hetu/http';
import type { DataFilterOption } from '../filter';
import { Dataset, DatasetApi, type QueryParams, type DatasetFile, DatasetField, AggregatorFunction } from './models';
import { createSingleClass } from '@hetu/core';
import type { FileData } from '@hetu/platform-shared';

/**
 * 数据资源服务类
 * <AUTHOR>
 */
export class DatasetServiceCtor<T extends DatasetApi = DatasetApi> extends HttpApiService<DatasetApi | T> {
  httpApi: DatasetApi = new DatasetApi();

  httpModuleKey = 'dataset';

  /**
   * 获取数据资源集合
   */
  getDatasetList(datasetType?: string): Promise<FileData<DatasetFile>> {
    return this.http.get(this.api.datasetList, { params: { datasetType } }).then(responseData, responseReject);
  }

  /**
   * 获取数据资源信息
   * @param id 数据资源ID
   * @param datasetType 数据资源类型
   */
  getDataset(id: string, datasetType?: string): Promise<Dataset> {
    return this.http
      .get(this.api.getById, {
        params: { id: id, datasetType: datasetType }
      })
      .then(responseData, responseReject);
  }

  /**
   * 获取查询参数列表
   * @param id 数据资源ID
   */
  getQueryParam(id: string): Promise<QueryParams> {
    return this.http
      .get(this.api.getQueryParam, {
        params: { id: id }
      })
      .then(responseData, responseReject);
  }

  /**
   * 获取 `columnName` 的字段值信息
   * @param id dataset id
   * @param columnName 列名
   */
  getFieldValues(id: string, columnName: string): Promise<DataFilterOption[]> {
    return this.http.get(this.api.fieldValues, { params: { id, columnName } }).then(responseData, responseReject);
  }

  /**
   * 团队/个人空间之间文件移动
   * @param id 文件id
   * @param targetDomain 目标团队标识
   * @param targetCatalogId 目标文件夹id
   * @param createCopy 是否创建副本并移动
   * @returns
   */
  spaceMove(
    id: string,
    targetDomain: string,
    targetCatalogId: string,
    createCopy: number
  ): Promise<ResponseResult | null> {
    return this.http
      .post(this.api.spaceMove, { id, targetDomain, targetCatalogId, createCopy })
      .then(responseResult, responseReject);
  }

  /**
   * 获取数据资源信息
   * @param id 数据资源ID
   */
  getFieldListById(id: string): Promise<{ datasetField: DatasetField[]; datasetName: string }> {
    return this.http
      .get(this.api.getFieldListById, {
        params: { id }
      })
      .then(responseData, responseReject);
  }

  /**
   * 获取当前数据资源类型支持的数据库函数列表
   * @param datasetId
   * @returns
   */
  getFunctions(datasetId: string): Promise<AggregatorFunction[]> {
    return this.http
      .get(this.api.functionList, {
        params: { id: datasetId }
      })
      .then(responseData, responseReject);
  }

  /**
   * 根据数据资源id返回名称
   * @param ids
   * @returns
   */
  getDatasetNames(ids: string[]): Promise<{ id: string; datasetName: string }[]> {
    return this.http.post(this.api.datasetNames, ids).then(responseData, responseReject);
  }

  /**
   * 获取dataframe数据资源 带权限列表
   */
  getDatasetListFromDataframe(): Promise<FileData<DatasetFile>> {
    return this.http.get(this.api.datasetListFromDataframe).then(responseData, responseReject);
  }

  /**
   * 获取api数据资源 带权限列表
   */
  getDatasetListFromApi(): Promise<FileData<DatasetFile>> {
    return this.http.get(this.api.datasetListFromApi).then(responseData, responseReject);
  }
}

export const DatasetService = createSingleClass(DatasetServiceCtor);
