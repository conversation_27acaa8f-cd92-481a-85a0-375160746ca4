import { computed, defineComponent, type PropType } from 'vue';
import { useUiStyle } from '../../../../hooks';
import { TextBox, TextAdapt, Block } from '../../../../models';

export default defineComponent({
  name: 'vis-textbox',
  props: {
    graph: {
      type: Object as PropType<TextBox>,
      required: true
    }
  },
  setup(props) {
    const textbox = computed(() => {
      return props.graph;
    });
    const content = computed(() => {
      return props.graph.content;
    });

    const computedText = computed(() => {
      return textbox.value.text;
    });
    const isFixed = computed(() => {
      return textbox.value.text?.adapt === TextAdapt.Fixed;
    });

    const { getTextStyle } = useUiStyle();
    const textStyle = computed(() => {
      const style = textbox.value.text ? getTextStyle(textbox.value.text) : {};

      return style;
    });

    /**
     * 计算超出样式
     */
    const adaptStyle = computed(() => {
      if (computedText.value?.adapt === TextAdapt.Ellipsis) {
        let lineNumber = 1;
        if (textbox.value.height) {
          const { lineHeight = 0, fontSize = 0 } = computedText.value;
          const baseHeight = lineHeight || fontSize * 1.5;
          const [top, right, bottom, left] = textbox.value.stroke?.position || [0, 0, 0, 0];
          lineNumber = Math.floor((textbox.value.height - top - bottom) / baseHeight);

          lineNumber = Math.max(lineNumber, 1);
        }
        return {
          display: '-webkit-box',
          webkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          webkitLineClamp: `${lineNumber}`
        };
      } else if (computedText.value?.adapt === TextAdapt.Single) {
        return {
          whiteSpace: 'nowrap'
        };
      }
      return {};
    });

    return {
      content,
      computedText,
      isFixed,
      textStyle,

      adaptStyle
    };
  }
});
