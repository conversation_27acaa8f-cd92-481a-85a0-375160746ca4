.#{$vis-prefix}-tab__item {
  display: var(--display, initial);
  align-items: var(--align-items, initial);
  justify-content: var(--justify-content, initial);
  box-shadow: var(--box-shadow, initial);
  color: var(--color, initial);
  font-family: var(--font-family, initial);
  font-size: var(--font-size, initial);
  font-style: var(--font-style, initial);
  font-weight: var(--font-weight, initial);
  letter-spacing: var(--letter-spacing, initial);
  line-height: var(--line-height, initial);
  text-align: var(--text-align, initial);
  text-decoration: var(--text-decoration, initial);
  text-indent: var(--text-indent, initial);
  text-shadow: var(--text-shadow, initial);
  filter: var(--filter, initial);
  backdrop-filter: var(--backdrop-filter, initial);

  * {
    border-radius: inherit;
  }

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border-radius: inherit;
    background-color: var(--background-color, initial);
    background-image: var(--background-image, initial);
    background-repeat: var(--background-repeat, initial);
    background-position: var(--background-position, initial);
    background-size: var(--background-size, initial);
  }

  &::after {
    content: '';
    display: block;
    position: absolute;
    z-index: -1;
    top: var(--border-offset-top, initial);
    left: var(--border-offset-left, initial);
    width: var(--border-offset-width, initial);
    height: var(--border-offset-height, initial);
    border-width: var(--border-width, initial);
    border-style: var(--border-style, initial);
    border-radius: inherit;
    border-color: var(--border-color, initial);
    border-image: var(--border-image, initial);
    border-image-slice: var(--border-image-slice, initial);
  }

  .vis-tab__text {
    display: var(--display, initial);
    align-items: var(--align-items, initial);
    justify-content: var(--justify-content, initial);
  }
}

.#{$vis-prefix}-tab__item.#{$vis_prefix}-tab__active {
  box-shadow: var(--active-box-shadow, initial);
  color: var(--active-color, initial);
  font-family: var(--active-font-family, initial);
  font-size: var(--active-font-size, initial);
  font-style: var(--active-font-style, initial);
  font-weight: var(--active-font-weight, initial);
  letter-spacing: var(--active-letter-spacing, initial);
  line-height: var(--active-line-height, initial);
  text-decoration: var(--active-text-decoration, initial);
  text-indent: var(--active-text-indent, initial);
  text-shadow: var(--active-text-shadow, initial);

  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border-radius: inherit;
    background-color: var(--active-background-color, initial);
    background-image: var(--active-background-image, initial);
    background-repeat: var(--active-background-repeat, initial);
    background-position: var(--active-background-position, initial);
    background-size: var(--active-background-size, initial);
  }

  &::after {
    content: '';
    display: block;
    position: absolute;
    z-index: -1;
    top: var(--active-border-offset-top, initial);
    left: var(--active-border-offset-left, initial);
    width: var(--active-border-offset-width, initial);
    height: var(--active-border-offset-height, initial);
    border-width: var(--active-border-width, initial);
    border-style: var(--active-border-style, initial);
    border-radius: inherit;
    border-color: var(--active-border-color, initial);
  }
}

.#{$vis-prefix}-tab__item.#{$vis-prefix}-tab__hover {
  &:hover {
    box-shadow: var(--hover-box-shadow, initial);
    color: var(--hover-color, initial);
    font-family: var(--hover-font-family, initial);
    font-size: var(--hover-font-size, initial);
    font-style: var(--hover-font-style, initial);
    font-weight: var(--hover-font-weight, initial);
    letter-spacing: var(--hover-letter-spacing, initial);
    line-height: var(--hover-line-height, initial);
    text-decoration: var(--hover-text-decoration, initial);
    text-indent: var(--hover-text-indent, initial);
    text-shadow: var(--hover-text-shadow, initial);
  }

  &:hover::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border-radius: inherit;
    background-color: var(--hover-background-color, initial);
    background-image: var(--hover-background-image, initial);
    background-repeat: var(--hover-background-repeat, initial);
    background-position: var(--hover-background-position, initial);
    background-size: var(--hover-background-size, initial);
  }

  &:hover::after {
    content: '';
    display: block;
    position: absolute;
    z-index: -1;
    top: var(--hover-border-offset-top, initial);
    left: var(--hover-border-offset-left, initial);
    width: var(--hover-border-offset-width, initial);
    height: var(--hover-border-offset-height, initial);
    border-width: var(--hover-border-width, initial);
    border-style: var(--hover-border-style, initial);
    border-radius: inherit;
    border-color: var(--hover-border-color, initial);
  }
}

@keyframes vertical-scroll {
  100% {
    transform: translateY(-100%);
  }
}

@keyframes horizontal-scroll {
  100% {
    transform: translateX(-100%);
  }
}
