<template>
  <q-dialog class="ht-app-release" ref="dialogRef" @show="renderQRCode(enableRelease ? releaseUrl : '')">
    <q-card class="w-168 !max-w-full">
      <q-card-section class="q-dialog__title row items-center justify-between no-wrap">
        <span class="ellipsis">{{ name ? '发布 - ' + name : '发布' }}</span>
        <q-icon name="close" class="ht-link" @click="onDialogCancel" />
      </q-card-section>

      <q-card-section class="row q-py-none">
        <div class="text-center q-mt-sm">
          <q-card bordered flat>
            <canvas class="w-40 h-40 rounded-borders" ref="qrcodeCanvasRef" />
          </q-card>
          <transition name="q-fade-in" mode="out-in">
            <q-btn class="ht-btn--outline q-mt-md" :disable="!enableRelease" unelevated @click="downloadQRCode(name)">
              保存二维码
            </q-btn>
          </transition>
        </div>

        <q-form class="col q-ml-md">
          <div>
            <q-toggle :model-value="enableRelease" left-label label="发布分享" @update:model-value="releaseConfirm" />
            <q-menu v-model="popoverVisible">
              <q-card class="w-46 q-px-md q-py-sm">
                <div>下一次打开发布分享时, 分享链接会发生变化!</div>
                <div class="q-mt-sm text-right q-gutter-x-sm">
                  <q-btn class="ht-btn--outline" unelevated size="sm" @click="popoverVisible = false">取消</q-btn>
                  <q-btn unelevated color="primary" size="sm" @click="toggleRelease">确定</q-btn>
                </div>
              </q-card>
            </q-menu>
          </div>

          <div>分享链接</div>
          <q-input
            v-model="releaseUrl"
            type="textarea"
            rows="2"
            input-class="!resize-none"
            placeholder="开启发布分享后可获得访问链接"
            :disable="!enableRelease"
            readonly
            outlined
            dense
          >
            <template v-slot:append>
              <q-btn size="xs" padding="xs" icon="content_copy" flat @click="copy(releaseUrl)" />
            </template>
          </q-input>

          <div class="row items-center">
            <q-toggle
              v-model="model.accessRestriction"
              left-label
              label="访问限制"
              :true-value="1"
              :false-value="0"
              :disable="!enableRelease"
              @update:model-value="changeAccessRestriction"
            />
            <div v-if="accessRestrictionError" class="q-ml-xs text-negative text-caption">请勾选验证方式</div>
          </div>

          <q-form ref="restrictionFormRef" :model="model">
            <div class="row">
              <q-field class="ht-field--small col-3" dense borderless>
                <q-checkbox
                  v-model="model.loginRestriction"
                  size="xs"
                  :true-value="1"
                  :false-value="0"
                  :disable="restrictionFormDisable"
                  @update:model-value="changeLoginRestriction"
                >
                  登录验证
                </q-checkbox>
              </q-field>

              <q-select
                v-model="users"
                class="ht-field--small col q-field--with-bottom"
                :options="userOptions"
                :disable="!model.loginRestriction || restrictionFormDisable"
                emit-value
                map-options
                use-chips
                multiple
                clearable
                outlined
                dense
                @clear="clearUsers"
              >
                <template v-slot:selected-item="{ removeAtIndex, index, tabindex, opt, selected }">
                  <q-chip
                    removable
                    dense
                    @remove="
                      removeAtIndex(index);
                      toggleSelectUser(selected, opt);
                    "
                    :tabindex="tabindex"
                  >
                    <q-avatar
                      color="secondary"
                      text-color="white"
                      :icon="opt.type === 1 ? 'person' : 'people_outline'"
                    />
                    {{ opt.label }}
                  </q-chip>
                </template>
                <template v-slot:option="{ itemProps, opt, selected }">
                  <q-item v-bind="itemProps" dense @click="toggleSelectUser(selected, opt)">
                    <q-item-section>
                      <q-item-label>{{ opt.label }}</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-icon :name="opt.type === 1 ? 'person' : 'people_outline'" size="16px" />
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>
            </div>

            <div class="row">
              <q-field class="ht-field--small col-3" dense borderless>
                <q-checkbox
                  v-model="passwordCheck"
                  size="xs"
                  :disable="restrictionFormDisable"
                  @update:model-value="changePasswordCheck"
                >
                  密码验证
                </q-checkbox>
              </q-field>

              <q-input
                v-model="model.accessPassword"
                class="ht-field--small col q-field--with-bottom"
                :rules="passwordCheck ? restrictionFormRules.accessPassword : []"
                :disable="!passwordCheck || restrictionFormDisable"
                dense
                no-error-icon
                outlined
                placeholder="请输入密码"
              />
            </div>

            <div class="row" v-if="tokenAuth">
              <q-field class="ht-field--small col-3" dense borderless>
                <q-checkbox
                  v-model="tokenCheck"
                  size="xs"
                  :disable="restrictionFormDisable"
                  @update:model-value="changeTokenCheck"
                >
                  Token验证
                </q-checkbox>
              </q-field>

              <q-input
                v-model="model.accessToken"
                class="ht-field--small col q-field--with-bottom"
                :disable="!tokenCheck || restrictionFormDisable"
                dense
                readonly
                no-error-icon
                outlined
              >
                <template v-slot:append>
                  <q-btn size="xs" padding="xs" icon="content_copy" flat @click="copy(model.accessToken)" />
                </template>
              </q-input>
            </div>

            <div class="row" v-if="paramAuth">
              <q-field class="ht-field--small col-3" dense borderless>
                <q-checkbox
                  v-model="model.paramCheck"
                  size="xs"
                  :true-value="1"
                  :false-value="0"
                  :disable="!tokenCheck || restrictionFormDisable"
                >
                  验证参数
                </q-checkbox>
              </q-field>

              <q-field class="ht-field--small col-3" dense borderless>
                <q-checkbox
                  v-model="termCheck"
                  size="xs"
                  :disable="(!passwordCheck && !tokenCheck) || restrictionFormDisable"
                  @update:model-value="changeTermCheck"
                >
                  验证时效
                </q-checkbox>
              </q-field>

              <q-input
                v-model.number="model.accessTerm"
                class="ht-field--small col q-field--with-bottom"
                :rules="termCheck ? restrictionFormRules.accessTerm : []"
                type="number"
                min="0"
                :disable="!termCheck || restrictionFormDisable"
                outlined
              >
                <template v-slot:append>
                  <span class="text-caption">天</span>
                </template>
              </q-input>
            </div>
          </q-form>

          <div class="q-mt-none">备注</div>
          <q-input
            v-model="model.remark"
            type="textarea"
            rows="2"
            input-class="!resize-none"
            placeholder="请输入备注"
            :disable="!enableRelease"
            outlined
            dense
          />
        </q-form>
      </q-card-section>

      <q-card-section class="q-gutter-x-md text-right">
        <q-btn class="ht-btn--outline" unelevated @click="onDialogCancel">取 消</q-btn>
        <q-btn color="primary" :disable="accessRestrictionError" unelevated @click="save">确 定</q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script src="./release.ts" lang="ts"></script>
