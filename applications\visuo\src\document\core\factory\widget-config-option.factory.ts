import {
  Color,
  FillPaints,
  FillType,
  InputOptions,
  ParagraphOptions,
  Stroke,
  StrokeType,
  Tab,
  TabOptions,
  Text,
  WidgetType
} from '../models';

/**
 * 组件配置工厂类
 * 用于管理不同组件的配置
 * <AUTHOR>
 */
export class WidgetConfigOptionFactory {
  /**
   * 段落组件配置
   */
  get [WidgetType.Paragraph]() {
    const options = new ParagraphOptions();
    const block = {
      text: new Text()
    };
    return {
      options,
      block
    };
  }

  /**
   * 标题组件配置
   */
  get [WidgetType.Title]() {
    return { options: {}, block: { text: new Text() } };
  }

  /**
   * 选项卡组件配置
   */
  get [WidgetType.Tab]() {
    const options = new TabOptions();
    const block = {
      text: new Text()
    };
    return {
      options,
      block
    };
  }

  /**
   * 文本输入组件配置
   */
  get [WidgetType.Input]() {
    const options = new InputOptions();
    const block = {
      stroke: new Stroke(undefined, undefined, new FillPaints(FillType.Solid, new Color(0, 0, 0, 0.1))),
      radius: [4, 4, 4, 4]
    };
    return { options, block };
  }
}
