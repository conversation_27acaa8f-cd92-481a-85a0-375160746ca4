@import '../index.scss';

.#{$vis-design-prefix}-dock {
  display: flex;
  position: fixed;
  bottom: 10px;
  left: 50%;
  justify-content: center;
  height: 38px;
  transform: translateX(-50%);

  [role='group'] {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 2px;
    padding: 0 4px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 0 0.5px rgb(0 0 0 / 18%), 0 3px 8px rgb(0 0 0 / 10%), 0 1px 3px rgb(0 0 0 / 10%);

    [role='toolbar'] {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 4px;
      padding: 6px;
      border-radius: 6px;
      font-size: 18px;
      cursor: pointer;

      i {
        width: 18px;
        height: 18px;
        line-height: 18px;
      }

      &:hover {
        background: #f5f5f5;
      }

      &.active {
        background: $primary;
        color: #fff;
      }
    }

    [role='toolbar-group'] {
      display: flex;
      align-items: center;
      justify-content: center;

      [role='more'] {
        padding: 8px 0;
        border-radius: 2px;
        font-size: 12px;
        cursor: pointer;

        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
}

.#{$vis-design-prefix}-dock-menu {
  box-shadow: 0 0 0 rgb(0 0 0 / 20%), 0 1px 2px rgb(0 0 0 / 14%), 0 1px 1px -1px rgb(0 0 0 / 12%);

  .q-item {
    &__section {
      font-size: $primary-font-size;

      &--avatar {
        min-width: 14px;
      }

      &--side {
        color: inherit;
      }
    }
  }
}
