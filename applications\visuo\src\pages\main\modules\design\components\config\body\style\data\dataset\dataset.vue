<template>
  <q-btn :ripple="false" class="dataset-btn vis-btn-hover-effect flex">
    <div class="!font-normal flex items-center flex-shrink-1">
      <div
        class="flex-shrink-1 min-w-0 break-all text-ellipsis whitespace-nowrap overflow-hidden"
        :style="{ maxWidth: `calc(${rightWidth}px - 110px)` }"
        style="text-transform: none; font-variant: normal"
      >
        {{ selected.label || '请选择' }}
      </div>
      <q-icon right size="12px" name="expand_more" />
    </div>

    <q-tooltip v-if="selected.label" class="max-w-[180px]">{{ selected.label }}</q-tooltip>
    <q-menu ref="menuRef" dense :style="{ width: `calc(${rightWidth}px - 40px)` }" :offset="[0, 8]" class="vis-menu">
      <q-input borderless v-model="search" :dense="false" clearable class="vis-input mx-3 mt-3 px-2 rounded-borders">
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
      </q-input>
      <q-scroll-area class="h-[calc(400px)]">
        <q-inner-loading :showing="loading && staticDataList.length === 0" color="primary" />
        <q-tree
          v-if="nodeTree.length > 0"
          class="vis-field--mini"
          :class="{ '!pb-0': staticDataList.length > 0 }"
          no-connectors
          no-transition
          :style="{ width: `calc(${rightWidth}px - 50px)` }"
          :nodes="nodeTree"
          :filter="search"
          label-key="title"
          node-key="id"
          no-results-label="暂无数据"
        >
          <template #default-header="prop">
            <div
              clickable
              class="row items-center w-full"
              :active="prop.node.id === selected.value"
              @click="onSelect(prop.node)"
            >
              <q-icon v-if="prop.node.dataType" name="o_folder" size="12px" class="q-mr-sm" />
              <div class="text-ellipsis whitespace-nowrap overflow-hidden flex-1 min-w-0">{{ prop.node.title }}</div>
              <q-tooltip class="w-[180px]">{{ prop.node.title }}</q-tooltip>
            </div>
          </template>
        </q-tree>
        <q-list v-if="staticDataList.length > 0" class="vis-list" :class="{ '!pt-0': staticDataList.length > 0 }">
          <q-separator v-if="nodeTree.length > 0" />
          <q-item v-for="item in staticDataList" :key="item.id" clickable @click="onSelect(item)">
            <q-item-section>
              <q-item-label>{{ item.title }}</q-item-label>
              <q-tooltip class="w-[180px]">{{ item.title }}</q-tooltip>
            </q-item-section>
          </q-item>
        </q-list>
        <div
          v-if="nodeTree.length === 0 && staticDataList.length === 0"
          class="flex items-center justify-center h-[calc(300px-40px)]"
        >
          <div class="text-center text-xs text-grey-5">暂无数据</div>
        </div>
      </q-scroll-area>
    </q-menu>
  </q-btn>
</template>

<script lang="ts" src="./dataset.ts" />
