import { BaseModel } from '@hetu/http';

/**
 * 类型
 */
export enum MaterialType {
  Image = 'image',
  Video = 'video',
  Geo = 'geo',
  Border = 'border',
  Icon = 'icon'
}

/**
 * 类型名称
 */
export const MaterialTypeName = {
  [MaterialType.Image]: '图片',
  [MaterialType.Video]: '视频',
  [MaterialType.Border]: '边框',
  [MaterialType.Icon]: '图标',
  [MaterialType.Geo]: 'GeoJson'
} as const;

/**
 * 素材库
 */
export class Material extends BaseModel {
  /**
   * 名称
   */
  name = '';

  /**
   * 文件名
   */
  fileName = '';

  /**
   * 文件大小
   */
  fileSize = 0;

  /**
   * 文件类型
   */
  extension = '';

  /**
   * 类型 图片：pic，视频 video
   */
  materialType = MaterialType.Image;

  /**
   * 数据
   */
  materialData?: Object | string;

  /**
   * 是否是默认
   */
  isDefault = 0;

  /**
   * 附件ID
   */
  attachmentId = '';

  /**
   * 目录id
   */
  catalogId = '';

  [key: string]: any;

  constructor(
    name?: string,
    materialType?: MaterialType,
    attachmentId?: string,
    catalogId?: string,
    extension?: string,
    fileSize?: number
  ) {
    super();
    name && (this.name = name);
    materialType && (this.materialType = materialType);
    attachmentId && (this.attachmentId = attachmentId);
    catalogId && (this.catalogId = catalogId);
    extension && (this.extension = extension);
    fileSize && (this.fileSize = fileSize);
  }
}
