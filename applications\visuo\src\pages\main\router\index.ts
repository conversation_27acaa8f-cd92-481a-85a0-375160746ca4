import { createWebHashHistory } from 'vue-router';
import { createRouter, useApp } from '@hetu/core';
import { createAppBeforeResolveGuard, createGlobalGuard } from '@hetu/platform-shared';
import { createCoBeforeResolveGuard } from '@hetu/platform-cooperation';
import routes from './routes';

export const router = createRouter({
  history: createWebHashHistory(),
  routes,
  // 是否应该禁止尾部斜杠。默认为假
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 })
});

export async function setupRouter() {
  createGlobalGuard();
  createAppBeforeResolveGuard();
  createCoBeforeResolveGuard();

  const app = useApp();
  app.use(router);

  // 路由准备就绪后挂载APP实例
  await router.isReady();
}
