import { setupAuthConfig, type AuthConfig } from './config';

export {
  TokenStoreType,
  CookieStorageStore,
  LocalStorageStore,
  MemoryStorageStore,
  SessionStorageStore
} from './store';
export type { TokenModel, TokenStore } from './store';
export * from './config';
export * from './token/token.guard';
export * from './token/token.interceptor';
export * from './token/token.service';

export const AuthModule = {
  install(app: unknown, config?: AuthConfig) {
    config && setupAuthConfig(config);
  }
};
