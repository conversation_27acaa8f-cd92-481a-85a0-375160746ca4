import { Dark, setCssVar } from 'quasar';
import { useScssVars } from '../scss-var';

export type NavModeType = 'side' | 'top' | 'both';

export type SeparatorType = 'none' | 'bordered' | 'elevated';

export class LayoutPanel {
  /** 是否显示 */
  visible: boolean;

  /** 连接样式 */
  separator: SeparatorType;

  get bordered() {
    return this.separator === 'bordered';
  }

  get elevated() {
    return this.separator === 'elevated';
  }

  constructor(visible = true, separator: SeparatorType = 'elevated') {
    this.visible = visible;
    this.separator = separator;
  }

  toggleVisible() {
    this.visible = !this.visible;
  }
}

export class LayoutHeader extends LayoutPanel {
  reveal = false;
  revealOffset?: number;

  constructor(visible?: boolean, separator?: SeparatorType) {
    super(visible, separator);
  }
}

export class LayoutFooter extends LayoutPanel {
  reveal = false;

  constructor(visible?: boolean, separator?: SeparatorType) {
    super(visible, separator);
  }
}

export class LayoutSide extends LayoutPanel {
  /** 收缩状态 */
  collapse = false;

  /** 是否悬浮 */
  overlay: boolean;

  private _miniCollapse = true;

  get miniCollapse() {
    return this.collapse ? this._miniCollapse : false;
  }

  animate = false;

  constructor(visible?: boolean, overlay = false) {
    super(visible);
    this.overlay = overlay;
  }

  toggleCollapse() {
    this.collapse = !this.collapse;
    this._miniCollapse = true;
  }

  innerToggleCollapse() {
    this.animate = true;
    this.toggleCollapse();
  }

  toggleMiniCollapse(collapse?: boolean) {
    collapse = collapse ?? !this._miniCollapse;
    this._miniCollapse = collapse;
  }
}

export class LayoutViewBuilder {
  hl: string;
  hc: string;
  hr: string;
  cl: string;
  cc = 'p';
  cr: string;
  fl: string;
  fc: string;
  fr: string;

  get view() {
    const header = `${this.hl}${this.hc}${this.hr}`;
    const content = `${this.cl}${this.cc}${this.cr}`;
    const footer = `${this.fl}${this.fc}${this.fr}`;
    return `${header} ${content} ${footer}`;
  }

  constructor(view: string) {
    this.hl = view[0];
    this.hc = view[1];
    this.hr = view[2];

    this.cl = view[4];
    this.cc = view[5];
    this.cr = view[6];

    this.fl = view[8];
    this.fc = view[9];
    this.fr = view[10];
  }
}

export class Layout {
  /** 布局结构 & 面板固定 */
  view = 'lHr LpR lfr';

  /** 暗黑模式 */
  get darkMode() {
    return Dark.mode;
  }

  /** 暗黑模式 */
  set darkMode(mode: boolean | 'auto') {
    Dark.set(mode);
  }

  /** 主题色 */
  private _primaryColor: string | undefined;

  /** 主题色 */
  get primaryColor() {
    return this._primaryColor || useScssVars().primaryColor || '#0079fe';
  }

  /** 主题色 */
  set primaryColor(color: string | undefined) {
    this._primaryColor = color;
    color && setCssVar('primary', color);
  }

  /** 页头 */
  header = new LayoutHeader();

  get topHeader(): boolean {
    return ['h', 'H'].includes(this.view[0]);
  }

  /** 页脚 */
  footer = new LayoutFooter(true, 'none');

  get topFooter(): boolean {
    return ['f', 'F'].includes(this.view[8]);
  }

  /** 左侧面板配置 */
  leftSide = new LayoutSide();

  get topLeftSider(): boolean {
    return ['l', 'L'].includes(this.view[0]);
  }

  /** 右侧面板配置 */
  rightSide = new LayoutSide(false, true);

  get topRightSider(): boolean {
    return ['r', 'R'].includes(this.view[2]);
  }

  isDrawerNav = false;

  /** 导航主题 */
  private _navTheme: 'light' | 'dark' = 'dark';

  /** 导航主题 */
  get navTheme() {
    return this.darkMode ? 'dark' : this._navTheme;
  }

  /** 导航主题 */
  set navTheme(navTheme: 'light' | 'dark') {
    this._navTheme = navTheme;
  }

  /** 导航模式 */
  private _navMode: NavModeType = 'side';

  get navOriginalMode() {
    return this._navMode;
  }

  /** 导航模式 */
  get navMode() {
    return this.isDrawerNav ? 'side' : this._navMode;
  }

  /** 导航模式 */
  set navMode(navMode: NavModeType) {
    this._navMode = navMode;
  }

  /** 内容区域宽度 */
  contentWidth: 'fluid' | 'fixed' = 'fluid';

  /** 登录主题 */
  loginTheme: 0 | 1 | 2 = 0;

  diff() {
    const def = new Layout();

    const diff = <T extends Object>(keys: Array<keyof T>, pKey?: keyof Layout) => {
      const obj = {} as T;
      keys.forEach((key) => {
        if (pKey) {
          if (this[pKey][key] !== def[pKey][key]) {
            obj[key] = this[pKey][key];
          }
        } else {
          if (this[key as keyof Layout] !== def[key as keyof Layout]) {
            const _key = ((key as string)[0] === '_' ? (key as string).substring(1) : key) as keyof T;
            obj[_key] = this[key as keyof Layout];
          }
        }
      });
      return Object.keys(obj).length ? obj : null;
    };

    const layout = diff<Layout>(['primaryColor', '_navMode', '_navTheme', 'view', 'contentWidth']) || ({} as Layout);
    if (this.darkMode !== false) {
      layout.darkMode = this.darkMode;
    }

    const header = diff<LayoutHeader>(['reveal', 'revealOffset', 'separator'], 'header');
    header && (layout.header = header);

    const footer = diff<LayoutFooter>(['reveal', 'separator'], 'footer');
    footer && (layout.footer = footer);

    const leftSide = diff<LayoutSide>(['overlay', 'separator'], 'leftSide');
    leftSide && (layout.leftSide = leftSide);

    const rightSide = diff<LayoutSide>(['overlay', 'separator'], 'rightSide');
    rightSide && (layout.rightSide = rightSide);

    return Object.keys(layout).length ? layout : null;
  }

  [key: string]: any;
}

export const LAYOUT_CONFIG_CACHE_KEY = 'layout_config';
