.vis-store-widget {
  @apply w-full h-full;

  &-header {
    @apply h-20 border-0 border-b border-solid border-gray-200;

    &-title {
      @apply flex p-2 justify-between items-center;

      &-text {
        @apply text-12px text-gray-800;
      }

      &-btn {
        @apply text-gray-800 text-2 pl-1 pr-1;
      }
    }

    &-search {
      padding: 5px 8px;
    }

    &-input {
      @apply h-7;
    }

    :deep(.q-field__control) {
      @apply h-full text-12px pl-1 pr-1;

      .q-field__append, .q-field__prepend{
        @apply h-full;

        .q-btn {
          @apply p-0;
        }
      }
    }
  }

}
