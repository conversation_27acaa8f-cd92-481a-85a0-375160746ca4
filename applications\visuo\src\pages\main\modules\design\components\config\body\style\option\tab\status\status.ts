import { type TabItemStyle } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';
import VisConfigStatusText from './text/text.vue';
import VisConfigStatusIcon from './icon/icon.vue';
import VisConfigStatusOther from './other/other.vue';

/**
 * 状态组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-tab-status',
  components: {
    VisConfigStatusText,
    VisConfigStatusIcon,
    VisConfigStatusOther
  },
  props: {
    modelValue: {
      type: Object as PropType<TabItemStyle>,
      required: true
    },
    title: {
      type: String,
      default: '悬浮'
    }
  },
  setup(props) {
    const popupShow = ref(false);

    const popupRef = ref();

    const tab = ref('text');
    const computedStyle = computed(() => props.modelValue);

    const showPopup = (e: Event) => {
      popupShow.value = !popupShow.value;
      popupRef.value?.handleShow(e);
    };

    const getData = (tab: string) => {
      switch (tab) {
        case 'text':
          return props.modelValue.font;
        case 'icon':
          return props.modelValue.icon;
        case 'other':
          return props.modelValue;
      }
    };
    return {
      popupShow,
      computedStyle,
      popupRef,
      tab,
      showPopup,
      getData
    };
  }
});
