import { defineComponent, ref } from 'vue';
import { useDesignStore } from '../../../../../../stores';

/**
 * 字段配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-field-config',
  setup(props, { emit }) {
    const fieldConfig = ref<
      {
        icon: string;
        label: string;
        value: string;
      }[]
    >([
      {
        icon: 'vis-letter-h',
        label: '升序',
        value: 'asc'
      }
    ]);

    const { aggregatorFunctions } = useDesignStore();

    const handleBeforeShow = () => {
      emit('before-show');
    };

    const handleBeforeHide = () => {
      emit('before-hide');
    };
    const handleFilter = () => {
      emit('handleFilter');
    };

    const handleSort = (order: 'asc' | 'desc') => {
      emit('handleSort', order);
    };

    return {
      fieldConfig,
      aggregatorFunctions,
      handleFilter,
      handleSort,
      handleBeforeShow,
      handleBeforeHide
    };
  }
});
