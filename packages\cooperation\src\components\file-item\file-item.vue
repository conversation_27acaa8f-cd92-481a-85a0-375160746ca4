<template>
  <template v-if="file.privilegeId">
    <q-item v-ca:[file.privilegeId]="acl.lookCooperation" clickable v-close-popup @click="openCoPanel">
      <q-item-section class="col-auto">
        <q-icon name="o_group" />
      </q-item-section>
      <q-item-section>协作成员</q-item-section>
    </q-item>
    <q-item
      v-if="isCanLeaveCo()"
      v-ca:[file.privilegeId]="acl.leaveCooperation"
      clickable
      v-close-popup
      @click="leaveCo"
    >
      <q-item-section class="col-auto">
        <q-icon name="o_exit_to_app" />
      </q-item-section>
      <q-item-section>退出协作</q-item-section>
    </q-item>
    <q-separator class="q-my-xs" />
  </template>
</template>
<script lang="ts" src="./file-item"></script>
