import type { Records } from '@hetu/util';
import { LinkTarget, LinkType, type Link, type LinkParam } from '../models';

/**
 * 链接公共方法
 * <AUTHOR>
 */
export const useLink = () => {
  /**
   * 计算param
   * @param params
   */
  const buildParams = (linkParams: Records) => {
    if (!linkParams.length) return '';

    const params: Records = {};
    linkParams.forEach((param: LinkParam) => {
      params[param.name] = param.value;
    });
    return Object.keys(params)
      .map((key: string) => {
        return `${key}=${params[key]}`;
      })
      .join('&');
  };

  /**
   * 计算路径和打开方式
   * @param link
   * @returns
   */
  const handleLink = (link: Link) => {
    if (!link.link) return { path: '', target: '' };
    // 计算方式
    const target = link.target === LinkTarget.Blank ? '_blank' : link.target === LinkTarget.Self ? '_self' : 'dialog';

    // 计算url
    let path = '';
    if (link.type === LinkType.Url) {
      path = link.link;
    } else {
      // TODO 处理容器预览和发布链接
      path = link.link;
    }
    // 计算param
    const paramStr = link.params ? buildParams(link.params) : '';
    path = `${path}${path.includes('?') ? '&' : '?'}${paramStr}`;

    return { path, target };
  };

  return {
    buildParams,
    handleLink
  };
};
