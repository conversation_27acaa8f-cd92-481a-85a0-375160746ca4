<template>
  <q-breadcrumbs
    :class="[
      'ht-app-breadcrumbs',
      $q.dark.isActive ? '' : list.length || hasSearch ? 'text-font-regular' : 'text-font-primary'
    ]"
    :active-color="$q.dark.isActive ? '' : 'font-primary'"
    gutter="xs"
  >
    <template v-slot:separator>
      <q-icon name="chevron_right" />
    </template>
    <q-breadcrumbs-el
      v-if="title"
      :class="[
        '!line-height-none shrink-0',
        !outHeader && 'text-h5',
        (list.length || hasSearch) && `${outHeader ? 'text-weight-bold ' : ''}cursor-pointer`
      ]"
      :label="title"
      @click="backup()"
    />
    <q-breadcrumbs-el v-if="list.length > 1 && !hasSearch" class="full-width" @click="backup(list[1])">
      <div class="text-weight-bold cursor-pointer full-width ellipsis">{{ list[1].title }}</div>
      <q-tooltip :delay="1000">{{ list[1].title }}</q-tooltip>
    </q-breadcrumbs-el>
    <q-breadcrumbs-el v-if="list.length" class="full-width" @click="hasSearch && backup(list[0])">
      <div :class="['full-width ellipsis', hasSearch && 'text-weight-bold cursor-pointer']">{{ list[0].title }}</div>
      <q-tooltip :delay="1000">{{ list[0].title }}</q-tooltip>
    </q-breadcrumbs-el>
    <q-breadcrumbs-el v-if="hasSearch" class="full-width">
      <div class="full-width ellipsis">{{ searchResult }}</div>
      <q-tooltip :delay="1000">{{ searchResult }}</q-tooltip>
    </q-breadcrumbs-el>
  </q-breadcrumbs>
</template>

<style lang="scss">
$ht-breadcrumbs-prefix-cls: '#{$ht-prefix}-app-breadcrumbs';

.#{$ht-breadcrumbs-prefix-cls} {
  > div {
    flex-wrap: nowrap;
    align-items: flex-end;
    line-height: 1;

    & > :nth-child(1) {
      flex-shrink: 0;
    }

    & > :not(.q-breadcrumbs__separator, :nth-child(1)) {
      min-width: 30px;
    }
  }
}
</style>

<script setup lang="ts">
import { computed } from 'vue';
import type { FileInfo, PageBreadcrumbsProps } from '../../models';

defineOptions({ name: 'ht-app-breadcrumbs' });

const props = defineProps<PageBreadcrumbsProps>();

const hasSearch = computed(() => props.search && props.search.text);
const searchResult = computed(() => `搜索"${props.search.text}"相关结果(${props.search.count})`);

const emit = defineEmits(['backup']);
const backup = (folder?: FileInfo) => emit('backup', folder);
</script>
