import { ACLResource } from '@hetu/acl';
/**
 * 回收站
 */
export class Recycle {
  // 文件、文件夹id
  dataId = '';
  // 类别：0-文件 1-文件夹
  dataType = 0;
  // 资源名称
  objectName = '';
  // 目录分类
  category = '';
  // 二级分类
  dataCategory = '';
  // 操作者姓名
  creatorName = '';
  // 删除时间
  createdTime = '';
  // 扩展字段
  extendData = '';
}

export enum RecycleACLResourceCode {
  /** 还原 */
  Restore = 'restore',
  /** 删除 */
  Delete = 'delete'
}

export abstract class RecycleACLResource extends ACLResource<typeof RecycleACLResourceCode> {
  get selectAll() {
    const code = this.resourceCode as typeof RecycleACLResourceCode;
    return {
      resource: { [this.group as string]: [code.Restore, code.Delete] }
    } as any;
  }
}
