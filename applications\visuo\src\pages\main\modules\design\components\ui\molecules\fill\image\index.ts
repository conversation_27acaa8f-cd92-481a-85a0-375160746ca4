import { defineComponent, ref, watch, type PropType } from 'vue';
import { type FillPaints, Image } from '@vis/document-core';
import { useMaterialDialog } from '@hetu/platform-app';
import { AttachmentService, isUUID } from '@hetu/platform-shared';

/**
 * 填充图片
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-fill-image',
  props: {
    modelValue: {
      type: Object as PropType<FillPaints>,
      required: true
    },
    /**
     * 是否为图标
     */
    isIcon: {
      type: Boolean,
      default: false
    },
    /**
     * 是否为侧边栏配置
     */
    isSidebar: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const image = ref(props.modelValue.image || new Image());

    const imageUrl = ref('');
    const inputUrl = ref('');

    const onSetUrl = () => {
      image.value.url = inputUrl.value;
    };

    const selectImage = async () => {
      const { HtMaterialDialog } = await useMaterialDialog();

      HtMaterialDialog(image.value.url, 'image').onOk((value) => {
        image.value.url = value;
      });
    };

    const fitOptions = [
      { label: '充满', value: 'cover' },
      { label: '适应', value: 'contain' },
      { label: '平铺', value: 'repeat' },
      { label: '拉伸', value: 'strech' }
    ];

    const handleUpdate = (obj: Object) => {
      emit('update:modelValue', Object.assign({}, props.modelValue, obj));
    };

    /**
     * 图片变化时提交更新
     */
    watch(
      () => image.value.url,
      () => {
        const newurl = image.value.url;
        if (newurl && !isUUID(newurl)) {
          imageUrl.value = newurl;
          inputUrl.value = newurl;
        } else {
          imageUrl.value = AttachmentService.downloadFileUrl(newurl);
          inputUrl.value = '';
        }

        handleUpdate(Object.assign({}, props.modelValue, { image: image.value }));
      },
      {
        immediate: true
      }
    );

    return {
      image,
      imageUrl,
      inputUrl,
      onSetUrl,
      selectImage,

      fitOptions
    };
  }
});
