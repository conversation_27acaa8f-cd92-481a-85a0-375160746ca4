import {
  transformerVariantGroup,
  transformerDirectives,
  presetAttributify,
  defineConfig,
  presetMini,
  presetUno,
  type UserConfig
} from 'unocss';

// https://github.com/unocss/unocss#readme
export default defineConfig({
  presets: [presetMini({ dark: 'class' }), presetAttributify(), presetUno()],
  transformers: [transformerDirectives(), transformerVariantGroup()],
  include: [`${__dirname}/packages/**/*.{vue,scss}`, `${__dirname}/applications/**/*.{vue,scss}`],
  exclude: [`/**/node_modules/**/*`, '/**/*.ts', '/**/*.js'],
  shortcuts: {
    // 'wh-full': 'w-full h-full'
  },
  theme: {
    colors: {
      // ---- quasar color ----
      // 要与 `quasar.variables.scss` 里保持一致
      primary: 'var(--q-primary)',
      secondary: 'var(--q-secondary)',
      accent: 'var(--q-accent)',
      dark: 'var(--q-dark)',
      'dark-page': 'var(--q-dark-page)',
      positive: 'var(--q-positive)',
      negative: 'var(--q-negative)',
      info: 'var(--q-info)',
      warning: 'var(--q-warning)',

      grey: {
        light: '#f5f7fa',
        lighter: '#E4E7ED',
        extra: { light: '#EBEEF5' }
      },

      // mix(#fff, $primary, 90%)
      'primary-lighter': '#eaf4fd',

      'dark-light': '#2d2d2d',

      favorite: '#ffa940',
      folder: '#ffa940',
      release: 'var(--q-positive)',

      // ---- el text color ----
      font: {
        primary: '#303133',
        regular: '#606266',
        secondary: '#909399',
        placeholder: '#C0C4CC'
      }
    }
  }
} as UserConfig);
