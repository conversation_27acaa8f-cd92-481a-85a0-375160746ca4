/** 应用标识 */
export const TOKEN_APP_KEY = Symbol('AppKey');

export enum AppType {
  /** 基础 */
  Basics = 'hetu_sys',
  /** 子应用 */
  Sub = 'hetu'
}

export interface App {
  /** 名称 */
  moduleName: string;

  /** 编码: 同应用标识 */
  moduleCode: string;

  /** 图标 */
  moduleIcon: string;

  /** 类型 */
  moduleType: AppType;

  /** 描述 */
  remark: string;
}

export const APP_CACHE_KEY = 'apps';

export const APP_CREATE_KEY = 'create';
