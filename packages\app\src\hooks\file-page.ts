import { computed, onBeforeUnmount, ref, watchEffect, watch, type <PERSON><PERSON>top<PERSON>and<PERSON>, provide } from 'vue';
import { useFilePageStore, FILE_PAGE_STORE_KEY } from '../stores';
import { useDomain } from './domain';
import { MYSPACE_CODE } from '@hetu/platform-shared';
import type { Menu } from '@hetu/theme';
import type { ACLResource } from '@hetu/acl';
import { OrderType, type QueryOrder } from '@hetu/http';
import { CacheService, CacheStoreType } from '@hetu/util';
import type { FileData, FileInfo, PageBreadcrumbsProps, PageQuery } from '../models';
import { FileTree, type FileTreeNode } from '../utils';
import { useQuasar } from 'quasar';
import { HtAppMove } from '../components';

export interface FilePageOptions<T extends FileInfo, D = FileData<T>> {
  acl: ACLResource<any>;

  category: string;

  cacheKey?: string;

  menus?: Menu[];

  fileTypes?: Array<{ name: string; label: string }>;

  isReleaseType?: boolean;

  hasAllType?: boolean;

  multiSelect?: boolean;

  order?: Partial<QueryOrder<keyof T>>;

  sortColumns?: Partial<Record<keyof T, string>>;

  store?: ReturnType<typeof useFilePageStore<T>>;

  extraTreeKey?: keyof T;

  selectTypeFilter?: (type: string, item: FileTreeNode<T>) => boolean;

  handleAction?: (action: string, args: any[]) => void;

  loadData(): Promise<D>;
}

export const useFilePage = <T extends FileInfo = FileInfo>(options: FilePageOptions<T>) => {
  const $q = useQuasar();

  const store = options.store ?? useFilePageStore<T>();
  provide(FILE_PAGE_STORE_KEY, store);

  const { acl, category } = options;
  const loading = ref(false);

  //#region 菜单
  const menus = options.menus || [];

  /** 选择菜单 */
  const selectMenu = (menu: Menu, preHandler?: () => boolean | void) => {
    query.value.mode = menu.menuCode;
    breadcrumbs.value.title = menu.menuName;

    if (preHandler) {
      const result = preHandler();
      if (result === void 0 || !result) {
        return;
      }
    }

    setNode();
  };
  //#endregion 菜单

  //#region 面包屑
  const breadcrumbs = ref<PageBreadcrumbsProps>({
    title: '我的工作台',
    search: { text: '', count: 0 },
    list: []
  });
  watch(store.parents, () => {
    breadcrumbs.value.list = store.parents.value;
  });
  const setBreadcrumbSearch = (count = 0) => {
    breadcrumbs.value.search.text = query.value.search;
    breadcrumbs.value.search.count = count;
  };
  const breadcrumbsBackup = (file?: string | T) => setNode(file, false);
  //#endregion 面包屑

  //#region 文件分类 -> tab
  const isReleaseType = options.isReleaseType ?? true;
  const hasAllType = options.hasAllType ?? true;
  let fileTypes = options.fileTypes;
  let defaultFileType: string | undefined;
  if (isReleaseType && !fileTypes) {
    fileTypes = [
      { name: 'all', label: '全部' },
      { name: '1', label: '已发布' },
      { name: '0', label: '未发布' }
    ];
    defaultFileType = 'all';
  } else {
    defaultFileType = fileTypes ? (hasAllType ? 'all' : fileTypes[0].name) : undefined;
  }
  /** 选择文件文件分类 */
  const selectType = (type: string) => {
    query.value.type = type;
    let condition: undefined | ((item: FileTreeNode<T>) => boolean);
    const selectTypeFilter = options.selectTypeFilter;
    if (selectTypeFilter && (!hasAllType || (hasAllType && type !== 'all'))) {
      condition = (item: FileTreeNode<T>) => selectTypeFilter(type, item);
    }
    node.value = node.value?.filter(condition);
    sort();
  };
  //#endregion 文件分类 -> tab

  //#region query & cache
  const query = ref<PageQuery<keyof T>>({
    search: '',
    order: Object.assign(
      {
        column: 'updateTime',
        sorting: OrderType.Desc
      },
      options.order
    ),
    mode: menus.length ? menus[0].menuCode : '',
    type: defaultFileType
  });

  const domain = useDomain();
  const cacheKey = () => (options.cacheKey ? `${options.cacheKey}_${domain.value || MYSPACE_CODE}` : '');
  const getCache = () => (options.cacheKey ? CacheService.getNone<Record<string, any> | null>(cacheKey()) : {});
  const setCache = () => {
    if (!options.cacheKey) {
      return;
    }

    CacheService.set(
      cacheKey(),
      {
        query: {
          order: query.value.order,
          mode: query.value.mode
        }
      },
      { type: CacheStoreType.Session }
    );
  };
  const mergeCacheQuery = () => {
    const cache = getCache();
    cache && cache.query && Object.assign(query.value, cache.query);
  };
  mergeCacheQuery();
  watch(domain, () => mergeCacheQuery());

  // 排序字段
  const sortColumns = options.sortColumns ?? {
    updateTime: '修改时间',
    orderNo: '序号'
  };
  //#endregion query & cache

  //#region 节点
  const node = store.open;
  /** 文件夹列表 */
  const folders = computed(() => node.value?.directorys || []);
  /** 文件列表 */
  const files = computed(() => node.value?.files || []);

  const folderGroup = computed(() => node.value?.id || query.value.mode);
  const fileGroup = computed(() => (node.value?.id || query.value.mode) + '_' + (query.value.type || 'file'));

  /**
   * 设置节点
   * @param file 文件夹 id 或 文件夹
   */
  const setNode = (file?: string | T, force = true) => {
    const id = !file || typeof file === 'string' ? file : file.id;
    // 清空搜索
    query.value.search = '';
    // 重置文件分类
    query.value.type = defaultFileType;

    if (!force && node.value && id === node.value?.id) {
      // search 无需执行前置过滤
      return search();
    }

    setBreadcrumbSearch();

    // 重置当前节点
    store.setOpen(store.tree?.getNode(id));
    sort();
  };

  const resetNode = () => {
    store.setOpen(store.tree?.getNode(node.value?.id));
    search();
  };

  const removeNode = (id: string) => {
    // 删除节点
    store.tree.removeNode(id);
    // 删除选中
    if (store.active.value) {
      if (options.multiSelect && Array.isArray(store.active.value)) {
        const index = store.active.value.findIndex((file) => file.id === id);
        index !== -1 && store.active.value.splice(index, 1);
      } else {
        store.active.value = undefined;
      }
    }
  };

  const setPreActiveNode = () => {
    if (store.preActiveId.value) {
      const fileType = store.tree?.getNode(store.preActiveId.value) as any;
      // 文件夹
      if (fileType && fileType.dataType) {
        store.setOpen(fileType);
      } else {
        const file = store.tree.getFile(store.preActiveId.value) as T;
        // 团队协作：父层级文件夹未分配协作，父层级文件夹内文件分配了协作, 从设计器返回，设置打开文件夹层级时需特殊处理。
        const parentId = file?.parentId && store.tree.getFile(file?.parentId) ? file?.parentId : '';
        setNode(parentId);
        store.active.value = options.multiSelect ? [file] : file;
      }
      store.preActiveId.value = undefined;
    } else {
      setNode();
    }
  };

  /** 排序 */
  const sort = () => {
    node.value = node.value?.sort(query.value.order as QueryOrder<keyof T>);
    setCache();
  };

  /** 搜索 */
  const search = () => {
    const result = node.value?.search(query.value.search);
    setBreadcrumbSearch(result?.length);
    store.setOpen(result);

    if (query.value.type && query.value.type !== defaultFileType) {
      selectType(query.value.type);
    } else {
      sort();
    }
  };

  /** 移动 */
  const move = (file?: T | T[], moveComponentProps?: Record<string, any>) => {
    file = file ?? store.active.value;
    if (!file && !(Array.isArray(file) && (file as Array<T>).length)) {
      return;
    }

    const sources = Array.isArray(file) ? file : [file];
    const targets = store.tree.getFile()?.directorys || [];

    $q.dialog({
      component: HtAppMove,
      componentProps: Object.assign(
        {
          title: Array.isArray(file) ? '' : file.title,
          parentId: store.open.value?.id,
          category: options.category,
          sources,
          targets
        },
        moveComponentProps
      )
    }).onOk(async ({ targetId, successIds }: { targetId: string; successIds: string[] }) => {
      successIds &&
        successIds.forEach((id) => {
          const item = store.tree.getFile(id) as FileInfo;
          removeNode(id);
          item.parentId = targetId;
          store.tree.addNode(item);
        });
    });
  };
  //#endregion 节点

  const handleAction = (action: string, args: any[]) => {
    if (action === 'setNode') {
      return setNode(...args);
    } else if (action === 'resetNode') {
      return resetNode();
    } else if (action === 'search') {
      return search();
    }

    options.handleAction && options.handleAction(action, args);
  };

  let actionWatchStopHandle: WatchStopHandle;
  const createActionHandle = () => {
    actionWatchStopHandle = watchEffect(() => {
      if (!store.action.value) return;
      handleAction(store.action.value.name, store.action.value.args);
      store.action.value = undefined;
    });
  };

  let isFirstInit = true;
  let domainWatchStopHandle: WatchStopHandle;
  const init = async () => {
    if (isFirstInit) {
      createActionHandle();
      domainWatchStopHandle = watch(domain, () => init());
    }

    loading.value = true;

    const data = await options.loadData();
    if (data) {
      store.tree = new FileTree<T>(data, options.extraTreeKey);
    }
    setPreActiveNode();

    loading.value = false;

    if (isFirstInit) {
      isFirstInit = false;
    }
  };

  onBeforeUnmount(() => {
    actionWatchStopHandle && actionWatchStopHandle();
    domainWatchStopHandle && domainWatchStopHandle();
    isFirstInit = true;
    store.clear();
  });

  return {
    acl,
    category,
    loading,

    menus,
    selectMenu,

    breadcrumbs,
    setBreadcrumbSearch,
    breadcrumbsBackup,

    fileTypes,
    selectType,

    domain,
    query,
    sortColumns,

    node,
    folders,
    files,
    folderGroup,
    fileGroup,
    setNode,
    resetNode,
    removeNode,
    setPreActiveNode,
    sort,
    search,
    move,

    handleAction,
    init
  };
};
