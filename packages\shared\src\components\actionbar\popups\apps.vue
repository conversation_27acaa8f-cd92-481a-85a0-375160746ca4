<template>
  <q-btn class="no-helper" icon="apps" size="12px" flat round>
    <q-popup-proxy
      class="ht-apps-popup"
      transition-show="flip-right"
      transition-hide="flip-left"
      breakpoint="0"
      @before-show="loadApps"
    >
      <div class="grid grid-cols-3">
        <div v-for="app in subApps" :key="app.moduleCode" @click="to(app.moduleCode)" class="ht-app-btn">
          <q-img :src="app.moduleIcon" fit="fill" spinner-size="1rem">
            <template v-slot:error>
              <ht-icon name="image-error" />
            </template>
            <ht-icon v-if="!app.moduleIcon" name="image-error" />
          </q-img>
          <div class="q-mt-sm text-xs">{{ app.moduleName }}</div>
        </div>
      </div>
      <q-separator class="q-my-sm !bg-grey-extra-light" />
      <div class="grid grid-cols-3">
        <div v-for="app in basicsApps" :key="app.moduleCode" @click="to(app.moduleCode)" class="ht-app-btn">
          <q-img :src="(app.moduleIcon || '.').includes('.') ? app.moduleIcon : ''" fit="fill">
            <template v-slot:error>
              <ht-icon name="image-error" />
            </template>
            <ht-icon v-if="!app.moduleIcon" name="image-error" />
            <ht-icon v-else-if="!app.moduleIcon.includes('.')" :name="app.moduleIcon" />
          </q-img>
          <div class="q-mt-sm text-xs">{{ app.moduleName }}</div>
        </div>
      </div>
    </q-popup-proxy>
  </q-btn>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { type App, AppType, AppService, PageService, SpaceService } from '@hetu/platform-shared';

const subApps = ref<App[]>();
const basicsApps = ref<App[]>();

const loadApps = () => {
  subApps.value = AppService.apps.filter((app: App) => app.moduleType === AppType.Sub);
  basicsApps.value = AppService.apps.filter((app: App) => {
    return app.moduleType === AppType.Basics && app.moduleCode !== import.meta.env.VITE_HETU_PROFILE_KEY;
  });
};

const to = (code: string): void => PageService.openApp(code, SpaceService.getDomainCode());
</script>
