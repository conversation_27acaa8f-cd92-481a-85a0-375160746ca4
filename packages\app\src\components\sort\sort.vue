<template>
  <div class="ht-app-sort ht-link">
    <q-icon :class="order.sorting === OrderType.Asc && 'flip-vertical'" name="sort" :size="iconSize || 'xs'" />
    <span class="q-pl-xs">{{ label }}</span>
    <q-menu :class="menuClass">
      <q-list class="q-py-xs" dense>
        <q-item>
          <q-item-section><q-item-label caption>字段</q-item-label></q-item-section>
        </q-item>
        <q-item
          v-for="(value, key) in columns"
          :key="key"
          :active="order.column === key"
          clickable
          @click="setColumn(key)"
        >
          <q-item-section class="q-px-md">{{ value }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section><q-item-label caption>排序</q-item-label></q-item-section>
        </q-item>
        <q-item
          v-for="(value, key) in orderText"
          :key="key"
          :active="order.sorting === key"
          clickable
          @click="setSorting(key)"
        >
          <q-item-section class="q-px-md">{{ value }}</q-item-section>
        </q-item>
      </q-list>
    </q-menu>
  </div>
</template>

<script setup lang="ts">
import { OrderType, OrderTypeText, type QueryOrder } from '@hetu/http';
import { computed } from 'vue';

defineOptions({ name: 'ht-app-sort' });

const props = defineProps<{
  label?: string;
  iconSize?: string;
  menuClass?: string;
  order: QueryOrder;
  columns: Record<string, string>;
}>();

const emit = defineEmits(['sort']);

const orderText = OrderTypeText;

const label = computed(() => props.columns[props.order.column] || props.label);

const setColumn = (value: string) => {
  const sortOrder = props.order;
  if (sortOrder.column === value) {
    return;
  }
  sortOrder.column = value;
  emit('sort', sortOrder);
};

const setSorting = (value: OrderType) => {
  const sortOrder = props.order;
  if (sortOrder.sorting === value) {
    return;
  }
  sortOrder.sorting = value;
  emit('sort', sortOrder);
};
</script>
