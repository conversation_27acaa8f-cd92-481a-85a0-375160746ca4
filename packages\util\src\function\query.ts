import { OrderType, type PagingQuery, type QueryOrder } from '@hetu/http';
import { TablePagination } from '../pagination';

/**
 * 构建并返回完整查询参数
 * @param pagination
 * @param filterModel 过滤 Model
 */
export function buildQuery(pagination: TablePagination, filterModel?: any): PagingQuery {
  // 筛选排序
  const filters = {} as any;
  const orders: QueryOrder[] = pagination.sortBy
    ? [{ column: pagination.sortBy as string, sorting: pagination.descending ? OrderType.Desc : OrderType.Asc }]
    : [];

  filterModel && Object.entries(filterModel).forEach(([fk, fv]) => fv !== '' && fv != null && (filters[fk] = fv));

  return {
    pageNum: pagination.page,
    // 为 0 时需要查询全部数据
    pageSize: pagination.rowsPerPage === 0 ? pagination.rowsNumber : pagination.rowsPerPage,
    orders,
    filters
  };
}
