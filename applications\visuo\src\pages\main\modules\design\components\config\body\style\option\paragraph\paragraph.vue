<template>
  <!-- 文本内容 -->
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span>文本内容</span>
    </div>
    <div class="vis-config-card__body">
      <div class="vis-form-inline">
        <div class="vis-form-field">
          <vis-textarea v-model="computedOptions.content" />
        </div>
      </div>
      <q-separator />
    </div>
  </div>
  <!-- 链接配置 -->
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span>链接配置</span>
      <q-btn flat dense @click="handleLink">
        <ht-icon class="vis-icon" :name="computedOptions.link.enable ? 'vis-subtract' : 'vis-add'" />
      </q-btn>
    </div>
    <div class="vis-config-card__body relative" v-if="computedOptions.link.enable">
      <vis-link v-model="computedOptions.link" />
    </div>
  </div>
</template>
<script lang="ts" src="./paragraph.ts"></script>
