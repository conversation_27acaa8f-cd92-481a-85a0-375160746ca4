/**
 * 获取地址栏参数
 * @param name 参数名称
 */
export function getQueryString(name: string) {
  const parsedUrl = new URL(window.location.href);
  return parsedUrl.searchParams.get(name);
}

/**
 * 判断是否是 `TJ` 开头，长度为32位id
 * @param id
 */
export function isUUID(id: string) {
  return id && id.startsWith('TJ') && id.length === 32;
}

/**
 * 导出文件下载
 * @param blob 文件数据流
 * @param type 文件类型
 * @param name 文件名称
 */
export function fileDownload(blob: BlobPart, type: string, name: string): boolean | void {
  if (!blob) {
    return;
  }

  const blobFile = new Blob([blob], { type });

  // 兼容ie版本
  if ((navigator as any).msSaveBlob) {
    return (navigator as any).msSaveBlob(blobFile, name);
  }

  const objectUrl = URL.createObjectURL(blobFile);
  const elink = document.createElement('a');
  elink.style.display = 'none';
  elink.href = objectUrl;
  elink.download = name;
  elink.click();
  URL.revokeObjectURL(objectUrl);
}

/**
 * 返回导出文件名称
 * @param headers
 * @returns
 */
export function getDownloadFileName(headers: Record<string, string> = {}): string {
  const name = headers['content-disposition']?.split('=')[1];
  return name ? decodeURIComponent(name.replace(/\"/g, '')) : '';
}

/**
 * 创建style标签，并追加到head中
 * @param style 样式字符串
 */
export function createStyle(style: string, id?: string) {
  const dom = document.createElement('style') as any;
  dom.type = 'text/css';
  dom.innerHTML = style;
  id && (dom.id = id);
  document.getElementsByTagName('head')[0].appendChild(dom);
}

/**
 * 在head中清除style标签
 * @param id style标签id
 */
export function removeStyle(id: string) {
  const dom = document.getElementById(id);
  dom && document.getElementsByTagName('head')[0].removeChild(dom);
}

export const browser = getBrowserType();

/**
 * 获取浏览器及版本信息  {type: 'IE', version: '9.0'}
 *
 * ```js
 * {
 *    type: 'IE' | 'Edge' | 'chrome' | 'firefox' | 'opera' | 'safari' | '',
 *    version: string;
 * }
 * ```
 */
function getBrowserType(): {
  /** `'IE' | 'Edge' | 'chrome' | 'firefox' | 'opera' | 'safari' | ''` */
  type: string;
  version: string;
} {
  const ua = navigator.userAgent.toLowerCase(),
    rMsie = /(msie\s|trident.*rv:)([\w.]+)/,
    rFirefox = /(firefox)\/([\w.]+)/,
    rOpera = /(opera).+version\/([\w.]+)/,
    rChrome = /(chrome)\/([\w.]+)/,
    rSafari = /version\/([\w.]+).*(safari)/,
    rEdge = /edge\/([\d.]+)/;

  let match = rMsie.exec(ua);
  if (match != null) {
    return { type: 'IE', version: match[2] || '0' };
  }
  match = rEdge.exec(ua);
  if (match != null) {
    return { type: 'Edge', version: match[2] || '0' };
  }
  match = rFirefox.exec(ua);
  if (match != null) {
    return { type: match[1] || '', version: match[2] || '0' };
  }
  match = rOpera.exec(ua);
  if (match != null) {
    return { type: match[1] || '', version: match[2] || '0' };
  }
  match = rChrome.exec(ua);
  if (match != null) {
    return { type: match[1] || '', version: match[2] || '0' };
  }
  match = rSafari.exec(ua);
  if (match != null) {
    return { type: match[2] || '', version: match[1] || '0' };
  }
  return { type: '', version: '0' };
}

/**
 * 滚动 `scrollableWrap` 直到 `dom` 可以被看到
 * @param dom dom
 * @param scrollableWrap 可滚动的包装 Element
 */
export function scrollDomIntoView(dom: Element, scrollableWrap: Element) {
  if (browser.type === 'chrome') {
    // only webkit
    if (typeof (dom as any).scrollIntoViewIfNeeded === 'function') {
      (dom as any).scrollIntoViewIfNeeded(true);
    } else {
      // other browsers
      dom.scrollIntoView({ behavior: 'smooth' });
    }
  } else {
    // 其他浏览器 (例如 firefox) 的 `scrollIntoView*` API 可能造成全屏滚动，所以手动计算
    const wrapRect = scrollableWrap.getClientRects()[0];
    const actdRect = dom.getClientRects()[0];
    const wrapT = wrapRect.top;
    const wrapH = wrapRect.height;
    const actdT = actdRect.top;
    const st = scrollableWrap.scrollTop;

    const actdY = actdT - wrapT;
    if (actdY > wrapH) {
      scrollableWrap.scrollTop = st + actdY;
    } else if (actdY < 0) {
      scrollableWrap.scrollTop = Math.abs(Math.abs(st) - Math.abs(actdY));
    }
  }
}

/**
 * 调整数值在范围内
 * @param number 校验数值
 * @param min 最小值
 * @param max 最大值
 * @returns 返回最终在范围内的数值
 */
export function adjustNumber(number: number, min?: number, max?: number) {
  const minValue = min === 0 ? 0 : min || -999999999;
  const maxValue = max === 0 ? 0 : max || 999999999;
  if (typeof number === 'string') {
    return Math.max(0, minValue);
  }
  return Math.min(Math.max(number, minValue), maxValue);
}
