import type { Records } from '@hetu/util';
import { GraphType, Graph, Stroke, Effects } from '../models';
import { useDocumentStore } from '../stores';
import { computed } from 'vue';
import { useUiStyle } from './ui-style';
import { useFill } from './fill';
import { useLayout } from './layout';

/**
 * 图形解析样式
 * @returns
 */
export const useGraphStyle = () => {
  const docStore = useDocumentStore();
  const isDesignMode = computed(() => docStore.isDesignMode.value);
  const isPreviewMode = computed(() => docStore.isPreviewMode.value);
  const isShareMode = computed(() => docStore.isShareMode.value);

  const { getStrokeStyle, createStyleVar, getEffectStyle } = useUiStyle();
  const { getFillPaintsStyle } = useFill();

  const { isFreeform, isFlex, isGrid } = useLayout();

  const graphClassName = (graph: Graph) => {
    const name = ['vis-graph', `vis-${graph.type.toLocaleLowerCase()}`];
    if (graph.locked) {
      name.push('lock');
    }
    if (!graph.visible) {
      name.push('hidden');
    }
    return name;
  };

  const graphComponentName = (graph: Graph) => {
    if (isDesignMode.value && [GraphType.Frame, GraphType.TextBox].includes(graph.type)) {
      return `vis-design-${graph.type.toLocaleLowerCase()}`;
    } else {
      return `vis-${graph.type.toLocaleLowerCase()}`;
    }
  };

  const graphStyle = (graph: Graph) => {};

  /**
   * 基础样式包括填充、描边、特效、圆角、透明度、层级
   * @param graph
   * @returns
   */
  const graphBaseStyle = (graph: Graph) => {
    const style: Records<string | number> = {
      zIndex: graph.order,
      borderRadius: graph.radius.join('px ') + 'px',
      opacity: graph.opacity / 100
    };

    // 计算填充、描边和特效
    const fill = getFillPaintsStyle(graph.fillPaints);
    const stroke = getStrokeStyle(graph.stroke as Stroke);
    const effects = getEffectStyle(graph.effects as Effects);
    const baseStyle = createStyleVar({ ...fill, ...stroke, ...effects });

    return { ...style, ...baseStyle };
  };

  return {
    graphClassName,
    graphComponentName,

    graphBaseStyle
  };
};
