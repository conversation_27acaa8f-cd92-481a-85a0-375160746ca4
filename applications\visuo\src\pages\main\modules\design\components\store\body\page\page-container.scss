@import '../../../index';

.#{$vis-prefix}-store-page-container {
  &-header {
    @apply flex justify-between lh-7 h-7 pl-1 pr-1;

    &-search {
      @apply flex items-center w-100%;
    }

    &-input {
      @apply w-100% bg-#efefef rounded-4px pl-1 pr-1;
    }
  }

  &-title {
    @apply lh-7 h-7;

    font-size: $primary-font-size;
  }

  &-btn {
    @include component-base;
    @apply p-0 m-0 w-6;

    font-size: $primary-font-size;

    &-group {
      @apply flex items-center;

      :deep(.q-btn) {
        .vis-icon,
        .q-icon {
          @apply text-#666 text-16px;
        }
      }
    }
  }

  &-tree_layout {
    .empty-content {
      @apply h-30px px-0 py-6px flex justify-center items-center text-#00000080;

      font-size: $primary-font-size;
    }
  }

  &-separator {
    position: relative;
    transition: background-color 0.2s ease;
    cursor: ns-resize;

    // 使用伪元素扩展触发区域
    &::before {
      content: '';
      position: absolute;
      z-index: 1;
      inset: -10px 0;
    }

    &:hover {
      background-color: rgb(25 118 210 / 10%);
    }

    &.is-dragging {
      background-color: rgb(25 118 210 / 20%);
    }
  }
}
