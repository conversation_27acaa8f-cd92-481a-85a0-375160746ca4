import {
  DEF_ROUTES,
  createJumpRoute,
  createSpaceRoutes,
  useAppGuard,
  useAppRouteGuard,
  useSpaceGuard,
  useTokenGuard
} from '@hetu/platform-shared';
import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // ...createSpaceRoutes({
  //   path: '/',
  //   //  component: Layout,
  //   meta: {
  //     spaceRedirect: '/design/document',
  //     canActivate: [useTokenGuard, useSpaceGuard, useAppGuard],
  //     canActivateChild: [useAppRouteGuard]
  //   },
  //   children: []
  // }),
  ...createSpaceRoutes({
    path: '/design/document/:id',
    name: 'design-document',
    props: true,
    component: () => import('../modules/design').then((m) => m.DocumentDesign),
    meta: {
      title: '可视化设计器',
      spaceRoot: false,
      canActivate: [useTokenGuard, useSpaceGuard, useAppGuard]
    }
  }),
  // ...createSpaceRoutes({
  //   path: '/test',
  //   name: 'test',
  //   props: true,
  //   component: () => RecycleComponent,
  //   meta: {
  //     title: '可视化设计器',
  //     spaceRoot: false,
  //     canActivate: [useTokenGuard, useSpaceGuard, useAppGuard]
  //   }
  // }),
  ...DEF_ROUTES
];

export default routes;
