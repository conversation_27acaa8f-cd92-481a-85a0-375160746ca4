import type { App } from 'vue';
import { ACLModule } from '@hetu/acl';
import { HttpModule } from '@hetu/http';
import { SettingsService, ThemeModule } from '@hetu/theme';

import 'virtual:uno.css';
import '../styles/index.scss';

import { setupQuasar } from './quasar';
import { components } from './components';
import { AppAuthConfig, AppHttpResponseInterceptor, HTTP_ALL_REQUEST_INTERCEPTORS, TOKEN_HTTP_APP_CODE } from './nets';
import { TOKEN_APP_KEY, TOKEN_HOME_PATH } from './models';
import { AuthModule, type AuthConfig } from '@hetu/auth';
import { useApp } from '@hetu/core';

export * from './components';
export * from './models';
export * from './routes';
export * from './services';
export * from './nets';
export * from './guards';
export * from './hooks';
export * from './utils';

type PlatformSharedOptions = {
  appKey: string;
  appCode?: string;
  authConfig?: AuthConfig;
  homePath?: false | string;
};

function install(app: App, options: PlatformSharedOptions) {
  setupQuasar(app);

  // 提供应用标识
  app.provide(TOKEN_APP_KEY, options.appKey);

  // 提供请求中的应用标识
  app.provide(TOKEN_HTTP_APP_CODE, options.appCode);

  // 提供首页路径
  app.provide(TOKEN_HOME_PATH, options.homePath ?? '/');

  // HTTP 请求模块
  app.use(HttpModule, {
    // 配置, 同 `AxiosRequestConfig`
    requestConfig: {
      baseURL: './'
    },
    interceptors: {
      request: HTTP_ALL_REQUEST_INTERCEPTORS,
      response: [AppHttpResponseInterceptor]
    },
    // API 地址配置
    apiConfig: () => SettingsService.api
  });

  // 访问权限控制模块
  app.use(ACLModule);

  // 用户认证模块
  app.use(AuthModule, options.authConfig ?? new AppAuthConfig());

  // 主题模块
  app.use(ThemeModule, { docTitleConfig: () => ({ suffix: SettingsService.app.name }) });

  // 全局注册组件
  Object.values(components).forEach((component) => app.component(component.name, component));
}

export function setupPlatformShared(options: PlatformSharedOptions) {
  const app = useApp();
  install(app, options);
}

export const PlatformSharedModule = { install };
