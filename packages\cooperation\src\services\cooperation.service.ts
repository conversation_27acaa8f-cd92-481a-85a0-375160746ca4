import { HttpApiService, responseData, responseReject, type HttpApi, responseResult } from '@hetu/http';
import { SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import type { CollaboratorDTO, CooperationACLDataType } from '../models';
import { createSingleClass } from '@hetu/core';

class CooperationApi implements HttpApi {
  permissions = `${SYSTEM_API_PREFIX}/catalog/privileges/`;
  saveCollaborator = `${SYSTEM_API_PREFIX}/collaborators`;
  removeCollaborator = `${SYSTEM_API_PREFIX}/collaborators/remove`;
  leaveCollaborator = `${SYSTEM_API_PREFIX}/collaborators/leave`;
}

class CooperationServiceCtor extends HttpApiService<CooperationApi> {
  httpApi = new CooperationApi();

  httpModuleKey = 'cooperation';

  /**
   * 获取应用协作权限组的资源权限信息配置
   * @param app 应用标识
   */
  getConfig(app: string): Promise<CooperationACLDataType[]> {
    return this.http.get(`${this.api.permissions}${app}`).then(responseData, responseReject);
  }

  /**
   * 保存协作者记录
   * @param data 文件协作者关联信息
   */
  saveCollaborator(data: CollaboratorDTO) {
    return this.http.post(this.api.saveCollaborator, data).then(responseResult, responseReject);
  }

  /**
   * 移除协作者
   * @param id 协作者记录ID
   */
  removeCollaborator(id: string, dataName: string) {
    return this.http.post(this.api.removeCollaborator, { id, dataName }).then(responseResult, responseReject);
  }

  /**
   * 退出协作
   * @param dataId 文件数据ID
   */
  leaveCollaborator(dataId: string, dataName: string) {
    return this.http.post(this.api.leaveCollaborator, { dataId, dataName }).then(responseResult, responseReject);
  }
}

/**
 * 协作服务类
 * <AUTHOR>
 */
export const CooperationService = createSingleClass(CooperationServiceCtor);
