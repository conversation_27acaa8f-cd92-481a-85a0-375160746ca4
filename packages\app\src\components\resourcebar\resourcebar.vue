<template>
  <div class="ht-resourcebar q-gutter-y-sm" :class="{ 'is-mini row justify-end': mini }">
    <div v-acl="acl.material" class="ht-resource" @click="openMaterial">
      <q-avatar size="40px" rounded>
        <img :src="'./static/img/material-logo.gif'" />
      </q-avatar>
      <div class="col-grow q-ml-sm">
        <div class="text-subtitle2 text-font-primary">素材库</div>
        <div class="text-font-secondary">图片/视频等资源管理</div>
      </div>
    </div>
    <div v-acl="acl.market" class="ht-resource" @click="openMarket">
      <q-avatar size="40px" rounded>
        <img :src="'./static/img/resource-logo.gif'" />
      </q-avatar>
      <div class="col-grow q-ml-sm">
        <div class="text-subtitle2 text-font-primary">资源广场</div>
        <div class="text-font-secondary">海量模板素材全新上线</div>
      </div>
      <q-icon name="arrow_forward" class="q-ml-sm text-font-secondary" />
    </div>
  </div>
</template>

<style lang="scss" src="./resourcebar.scss"></style>

<script src="./resource.ts" lang="ts"></script>
