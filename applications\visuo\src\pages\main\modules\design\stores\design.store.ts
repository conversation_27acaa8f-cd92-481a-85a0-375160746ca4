import { ref } from 'vue';
import { defineStore } from '@hetu/core';
import type InfiniteViewer from 'infinite-viewer';
import type Selecto from 'selecto';
import type Moveable from 'vue3-moveable';
import { syncedStore, getYjsDoc } from '@syncedstore/core';
import { WebsocketProvider } from 'y-websocket';
import { Page, Frame, type GuidesInterface, Graph } from '@vis/document-core';
import type { AggregatorFunction } from '@hetu/metadata-shared';

/**
 * 设计器状态管理
 * <AUTHOR>
 */
export const useDesignStore = defineStore(() => {
  const active = ref(new DesignActiveState());

  const fileStore = syncedStore({ file: {} as any });

  const fileYdoc = getYjsDoc(fileStore);

  // const wsProvider = new WebsocketProvider('ws://localhost:1234', 'fileDesign1a', fileYdoc);

  // 标尺
  const horizontalGuidesRef = ref<GuidesInterface>();
  const verticalGuidesRef = ref<GuidesInterface>();

  const rulerState = ref<{
    zoomRange: number[];
    zoom: number;
    // 标尺选中的范围
    selectedRangesH: number[][];
    selectedRangesV: number[][];
    defaultGuidesPos: number; // 初始参考线基准位置
    defaultScrollPos: number; // 初始滚动位置
  }>({
    zoomRange: [0.1, 2],
    zoom: 1,
    selectedRangesH: [],
    selectedRangesV: [],
    defaultGuidesPos: 0,
    defaultScrollPos: 0
  });

  // 无限画布
  const infiniteCanvasRef = ref<InfiniteViewer>();

  const moveableRef = ref<Moveable>();
  const moveableTargets = ref<Array<HTMLElement>>([]);

  const selectoRef = ref<Selecto>();

  /** 画布状态 */
  const canvasState = ref<{
    dropbox: boolean;
    isRotate: boolean;
    dragging: string[];
    resizeing: string[];
    frame: Frame | undefined;
    gridRowCol: number[] | undefined;
  }>({
    /** 是否显示用于添加组件的容器 */
    dropbox: false,
    /** 当前正在旋转图形 */
    isRotate: false,
    /** 当前正在拖拽的图形id，用于在flex布局中，正在拖拽的图形脱离flex布局 */
    dragging: [],
    /** 当前正在调整大小的图形 id*/
    resizeing: [],
    /** 当前可放置的容器 1.从左侧面板拖拽组件时，记录组件放置在哪个容器内 */
    frame: undefined,
    /** activeFrame为grid布局时，记录拖拽过程中图形放置的格子位置 [row, col]*/
    gridRowCol: undefined
  });

  //左侧选中的菜单
  const selectedMenu = ref<string>('pages');

  // 计数器
  const counter = ref(0);

  //#region 设计器状态
  // 左右两侧宽度值，默认240px
  const leftWidth = ref(240);
  const rightWidth = ref(240);
  // 设计器模式 vis 可视化 node 节点编程 analysis 分析
  const mode = ref('vis');

  //#endregion

  const aggregatorFunctions = ref<AggregatorFunction[]>([]);

  return {
    active,

    rulerState,
    horizontalGuidesRef,
    verticalGuidesRef,

    infiniteCanvasRef,

    moveableTargets,
    moveableRef,

    selectoRef,

    canvasState,

    fileStore,
    fileYdoc,

    selectedMenu,

    counter,

    /** 左侧宽度 */
    leftWidth,
    /** 右侧宽度 */
    rightWidth,
    /** 设计器模式 */
    mode,

    /** 聚合函数列表 */
    aggregatorFunctions
  };
});
export class DesignActiveState {
  /** 当前选中的页面 */
  page: Page = new Page();
  /** 当前选中的容器: 当前选中图形的父Frame */
  frame?: Frame;
  /** 当前选中的图形 */
  graphs: Graph[] = [];
  /** 当前选中的图形id */
  graphIds: string[] = [];
  /** 当前选中的图形 */
  get graph() {
    return this.graphs.length ? this.graphs[0] : undefined;
  }
}
