const { viteStaticCopy } = require('vite-plugin-static-copy');
const { appHooks } = require('./apps');

module.exports.copyAssets = function () {
  const assets = [];
  for (let app in appHooks) {
    if (!appHooks[app].getAssets) {
      continue;
    }
    const appAssets = appHooks[app].getAssets();
    if (Array.isArray(appAssets)) {
      assets.push(...appAssets);
    } else {
      assets.push(appAssets);
    }
  }

  return viteStaticCopy({ targets: assets });
};
