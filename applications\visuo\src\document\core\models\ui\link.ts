import isBoolean from 'lodash-es/isBoolean';

/**
 * 链接
 * 用于定义文档中的链接属性
 * <AUTHOR>
 */
export class Link {
  /** 是否启用链接 */
  enable: boolean = true;

  /** 链接类型 */
  type = LinkType.Url;

  /** 链接地址 */
  link: string = '';

  /** 链接打开方式 */
  target = LinkTarget.Blank;

  /** 链接参数列表 */
  params: LinkParam[] = [];

  /** 弹窗选项配置 */
  targetOption = new TargetOption();

  constructor(
    enable?: boolean,
    type?: LinkType,
    link?: string,
    target?: LinkTarget,
    params?: LinkParam[],
    targetOption?: TargetOption
  ) {
    isBoolean(enable) && (this.enable = enable);
    type && (this.type = type);
    link && (this.link = link);
    target && (this.target = target);
    params && (this.params = params);
    targetOption && (this.targetOption = targetOption);
  }
}

/** 链接类型 */
export enum LinkType {
  /** 外链 */
  Url = 'url',
  /** 容器 */
  Frame = 'frame'
}

/** 打开方式 */
export enum LinkTarget {
  /** 当前窗口 */
  Self = 'self',
  /** 新窗口 */
  Blank = 'blank',
  /** 弹窗形式 */
  Dialog = 'dialog'
}

/** 弹窗位置 */
export enum DialogPosition {
  Top = 'top',
  Right = 'right',
  Bottom = 'bottom',
  Left = 'left',
  Center = 'center'
}
/**
 * 携带参数
 */
export class LinkParam {
  /** 参数类型 fix-固定参数 | param-全局参数 */
  type: 'fix' | 'param' = 'fix';
  /** 参数名 */
  name: string = '';
  /** 参数值 */
  value: string = '';
}

export class TargetOption {
  /** 弹窗位置 */
  position: DialogPosition = DialogPosition.Center;
  /** 弹窗尺寸，固定尺寸-自定义 | full-全屏 | auto-自适应 */
  size: [number, number] | 'full' | 'auto' = [800, 600];
  /** 弹窗标题 */
  title: string = '';
  /** 弹窗遮罩 */
  mask: boolean = true;
  /** 是否可移动 */
  moveable: boolean = false;
  /** 弹窗外点击隐藏 */
  clickHide: boolean = true;
}
