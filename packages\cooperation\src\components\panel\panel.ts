import { computed, defineComponent, type PropType, ref } from 'vue';
import { Notify, useDialogPluginComponent } from 'quasar';
import {
  type Collaborator,
  type CooperationFile,
  CooperationACLResource,
  CooperationACLResourceCode,
  SubFileACLResource
} from '../../models';
import { CatalogFileService, CatalogService } from '@hetu/platform-app';
import { AppService, AttachmentService, TeamService, type Profile } from '@hetu/platform-shared';
import { CooperationACLService } from '../../services/cooperation-acl.service';
import { CooperationService } from '../../services/cooperation.service';
import type { ResponseResult } from '@hetu/http';

export default defineComponent({
  name: 'ht-co-panel',

  props: {
    file: {
      type: Object as PropType<CooperationFile>,
      default: function () {
        return {};
      }
    },
    category: {
      type: String,
      default: ''
    },
    acl: {
      type: Object as PropType<CooperationACLResource>,
      default: function () {
        return {};
      }
    },
    subAcl: {
      type: Object as PropType<SubFileACLResource>,
      default: function () {
        return {};
      }
    },
    isParentPrivilege: Boolean
  },

  setup(props) {
    const { dialogRef, onDialogCancel } = useDialogPluginComponent();

    const loading = ref<boolean>(false);
    const privilegeLoading = ref<boolean>(false);
    const query = ref<string>('');
    const members = ref<Profile[]>([]);
    const cooperationFile = computed(() => props.file).value;

    // 权限组
    const permissions = CooperationACLService.getByGroup(cooperationFile.dataType ? 'directory' : 'file');

    // 未加入列表
    const notJoinProfiles = computed(() => {
      const ids = currentPrivileges.value.map((p) => p.objectId);
      const allIds = parentPrivileges.value.filter((p) => p.privilegeId === allPrivilegeId()).map((p) => p.objectId);
      return members.value.filter((p) => {
        return (
          ![...ids, ...allIds].includes(p.id) &&
          (p.fullName || '').toLowerCase().includes(query.value?.toLowerCase() || '')
        );
      });
    });

    // 当前协作者列表 authMode = 0
    const currentPrivileges = computed(() => {
      return cooperationFile.privileges.filter((p) => {
        return !p.authMode && (p.fullName || '').toLowerCase().includes(query.value?.toLowerCase() || '');
      });
    });

    // 上级协作者 authMode = 1
    const parentPrivileges = computed(() => {
      return cooperationFile.privileges.filter((p) => {
        return p.authMode && (p.fullName || '').toLowerCase().includes(query.value?.toLowerCase() || '');
      });
    });

    // 所有者协作id
    const allPrivilegeId = () => {
      const all = CooperationACLService.getByGroup('all');
      return all.length && all[0].id;
    };

    // 获取成员列表
    const loadMember = () => {
      loading.value = true;
      query.value = '';
      TeamService.getMembers(0).then((profiles) => {
        members.value = profiles || [];
        loading.value = false;
      });
    };

    // 权限操作下拉菜单
    const handleCommand = async (privilegeId: string, privilege: Collaborator | Profile) => {
      let result: ResponseResult | null;
      if (privilegeId !== 'remove') {
        privilegeLoading.value = true;

        result = await CooperationService.saveCollaborator({
          dataId: cooperationFile.id,
          dataName: cooperationFile.title,
          objectId: (privilege as Collaborator).objectId || privilege.id,
          privilegeId: privilegeId,
          category: props.category,
          dataType: cooperationFile.dataType,
          moduleCode: AppService.appKey
        });
      } else {
        result = await CooperationService.removeCollaborator(privilege.id, cooperationFile.title);
      }

      if (result && result.status === 'success') {
        Notify.create({ message: result.message, type: 'positive', position: 'top' });
        getDetail();
      }
    };

    // 获取单个数据权限
    const getDetail = async () => {
      const srv = cooperationFile.dataType ? CatalogService : CatalogFileService;
      const file = await srv.detail<CooperationFile>(props.category, cooperationFile.id);
      cooperationFile.privilegeId = file.privilegeId;
      cooperationFile.privileges = file.privileges;
      privilegeLoading.value = false;
    };

    // 返回权限名称
    const getPrivilegeName = (privilege: Collaborator) => {
      const permission = CooperationACLService.get(privilege.privilegeId);
      return permission ? permission.privilegeName.split('_')[0] : '';
    };

    // 协作者是否可切换权限：是否是所有者，所有者不可更改可管理可编辑等操作
    const getIsChangePermission = (privilege: Collaborator) => {
      return !!permissions.find((p) => p.id === privilege.privilegeId);
    };
    const isSub = cooperationFile.privileges.find((item) => {
      return item.privilegeId === cooperationFile.privilegeId && item.authMode === 1;
    });
    const canToolbar = () => {
      const code = (isSub ? props.subAcl : props.acl).resourceCode as typeof CooperationACLResourceCode;
      return CooperationACLService.allow({
        resource: {
          [(isSub ? props.subAcl : props.acl).group as string]: [code.AddCooperation]
        },
        group: cooperationFile.privilegeId
      });
    };

    // 获取头像
    const avatarUrl = (id: string) => AttachmentService.downloadFileUrl(id);

    return {
      dialogRef,
      onDialogCancel,

      cooperationFile,
      loading,
      privilegeLoading,
      query,
      permissions,

      notJoinProfiles,
      currentPrivileges,
      parentPrivileges,

      loadMember,

      handleCommand,
      getPrivilegeName,
      getIsChangePermission,
      canToolbar,
      avatarUrl,
      isSub
    };
  }
});
