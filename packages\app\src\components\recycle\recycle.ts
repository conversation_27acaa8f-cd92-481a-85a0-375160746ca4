import { type Ref, defineComponent, ref, reactive, type PropType } from 'vue';
import { AppService, SpaceService } from '@hetu/platform-shared';
import { RecycleService } from '../../services';
import { Recycle, RecycleACLResourceCode } from '../../models';
import { OrderType } from '@hetu/http';
import { ACLResource } from '@hetu/acl';
import { CacheService, CacheStoreType } from '@hetu/util';
import type { QTableColumn } from 'quasar';

export default defineComponent({
  name: 'ht-app-recycle',
  props: {
    acl: {
      type: Object as PropType<ACLResource<RecycleACLResourceCode>>,
      required: true
    }
  },
  setup(props, { emit, expose }) {
    // 排序字段
    const sortColumns = {
      createdTime: '删除时间'
    };

    /** 查询参数 */
    const query = reactive({
      // 搜索值
      search: '',
      // 排序方式
      order: {
        column: 'createdTime',
        sorting: OrderType.Desc
      }
    });

    const loading = ref<boolean>(false); //加载状态

    let recycles: Recycle[] = reactive([]); /** 回收站列表集合 */

    const queryRecycle: Ref<Recycle[]> = ref([]); /**查询列表集合 */

    const selected: Ref<Recycle[]> = ref([]); /** 选中列表 */

    const appKey = AppService.appKey;

    /** 当前空间的域名标识 */
    const domain = SpaceService.getDomainCode();

    const cacheKey = `ht_recycle_${domain || 'me'}`;

    const getRecycleList = () => {
      /**获取回收站查询列表 */
      loading.value = true;
      RecycleService.getlist(appKey)
        .then((data) => {
          recycles = data || [];
          // 删除/还原失败获取失败交集数据，用于selected选中状态维持
          const errorIntersection = selected.value.filter((select) =>
            recycles.find((recycle) => recycle.dataId === select.dataId)
          );
          selected.value = [...errorIntersection];
          search();
        })
        .finally(() => {
          loading.value = false;
        });
    };

    /**暴露初始化列表的方法*/
    expose({
      getRecycleList
    });

    /**初始化页面内容 */
    const init = () => {
      const cache = CacheService.getNone<Record<string, any>>(cacheKey);
      if (cache) {
        query.order.column = cache.query.sort;
        query.order.sorting = cache.query.order;
      }
      getRecycleList();
    };

    init();

    /**
     * 搜索
     */
    const search = () => {
      const { search } = query;
      queryRecycle.value = recycles.filter((recycle) => {
        let valid = true;
        if (valid && search) {
          valid = (recycle.objectName || '').toLowerCase().includes(search.toLowerCase());
        }
        return valid;
      });
      sort(null);
    };

    /**
     * 排序
     */
    const sort = (order: typeof query.order | null) => {
      queryRecycle.value.sort((a: any, b: any) => {
        const isAsc = query.order.sorting === 'asc';
        let sortA = a[query.order.column];
        let sortB = b[query.order.column];
        if (['createdTime'].includes(query.order.column)) {
          sortA = a['_' + query.order.column] || (a['_' + query.order.column] = new Date(sortA).getTime());
          sortB = b['_' + query.order.column] || (b['_' + query.order.column] = new Date(sortB).getTime());
        }
        sortA = parseInt(sortA, 10);
        sortB = parseInt(sortB, 10);
        return sortA === sortB ? 0 : sortA > sortB ? (isAsc ? 1 : -1) : isAsc ? -1 : 1;
      });

      if (order && order.sorting) {
        orderChange(order.sorting);
      }
    };

    /**
     * 排序属性改变事件
     * @param key 排序属性
     */
    const orderChange = (key: OrderType) => {
      query.order.sorting = key;
      setCache();
    };

    /**缓存排序属性 */
    const setCache = () => {
      CacheService.set(
        cacheKey,
        {
          query: {
            order: query.order.sorting,
            sort: query.order.column
          }
        },
        { type: CacheStoreType.Session }
      );
    };

    /** 全选 */
    const selectall = ref<boolean>(false);

    /**
     * 全选
     */
    const onChangeSelectall = () => {
      selected.value = selectall.value ? queryRecycle.value : [];
    };

    /** 修改表格选中控制右上角全选状态*/
    const onActive = (recycles: readonly Recycle[]) => {
      selected.value = recycles as Recycle[];
      selectall.value = !!(queryRecycle.value.length && selected.value.length === queryRecycle.value.length);
    };

    /**
     * 还原文件、文件夹
     * @param data 文件信息
     */

    const onRestore = (data?: Recycle) => {
      const param = !data ? selected.value : data;
      emit('onRestore', param);
    };

    /**
     * 删除文件、文件夹
     * @param data 文件信息
     */
    const onDel = (data?: Recycle) => {
      const param = !data ? selected.value : data;
      emit('onDel', param);
    };

    /**
     * 回收站文件列表列属性配置
     */
    const columns = [
      {
        name: 'objectName',
        required: true,
        label: 'objectName',
        field: (row: Recycle) => row.objectName,
        sortable: true
      },
      {
        name: 'name',
        required: true,
        label: 'name',
        field: (row: Recycle) => row.dataId,
        sortable: true,
        align: 'left'
      },
      {
        name: 'creatorName',
        required: true,
        label: 'creatorName',
        field: (row: Recycle) => row.creatorName,
        sortable: true,
        align: 'center'
      },
      {
        name: 'createdTime',
        required: true,
        label: 'createdTime',
        field: (row: Recycle) => row.createdTime,
        sortable: true,
        align: 'left'
      },
      {
        name: 'operate',
        required: true,
        label: 'operate',
        field: (row: Recycle) => row.dataId,
        sortable: true
      }
    ] as QTableColumn[];

    return {
      loading,
      query,
      selectall,
      selected,
      queryRecycle,
      sortColumns,
      getRecycleList,
      columns,
      search,
      onRestore,
      onDel,
      onChangeSelectall,
      sort,
      onActive
    };
  }
});
