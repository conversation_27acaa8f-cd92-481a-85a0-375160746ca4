.#{$vis-design-prefix}-ruler {
  &-box {
    position: relative;
    z-index: 21;
    box-sizing: border-box;
    width: 20px;
    height: 20px;
    background: #f8f8fc;

    &::before,
    &::after {
      content: '';
      position: absolute;
      background: #e1e1e1;
    }

    &::before {
      left: 100%;
      width: 1px;
      height: 100%;
    }

    &::after {
      top: 100%;
      width: 100%;
      height: 1px;
    }
  }

  &-horizontal {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;

    canvas {
      border-bottom: 1px solid #e1e1e1;
    }
  }

  &-vertical {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 100%;

    canvas {
      border-right: 1px solid #e1e1e1;
    }
  }

  .scena-guides-manager {
    .scena-guides-guides {
      .scena-guides-guide {
        background-color: #f4aa9a;

        &.scena-guides-horizontal {
          .scena-guides-guide-pos {
            left: 26px;
          }
        }

        &.scena-guides-vertical {
          .scena-guides-guide-pos {
            top: 26px;
          }
        }

        &-pos {
          color: #fe9336;
          font-size: 8px;
          font-weight: inherit;
        }
      }

      .scena-guides-dragging {
        background: #fe9336;

        .scena-guides-guide-pos {
          display: none;
        }
      }

      .scena-guides-display-drag {
        top: -12px;
        left: 12px;
        color: #fe9336;
        font-size: 8px;
        font-weight: normal;
      }
    }
  }
}
