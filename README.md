# hetu-framework

河图应用前端工程

> windows 环境 bulid 报错, 报错原因是 `index.html` 中含有 `css`, 插件问题, 暂未解决

```
pnpm workspace:
  - packages
  - internal
  - applications
```

## Project setup

```bash
pnpm install
```

### VS Code 插件

- `eslint`
- `stylelint`
- `prettier`
- `unocss`
- `volar`

### Apps

- `visuo`: 可视化设计应用

### Compiles and hot-reloads for development

```bash
# `applications` 下的应用
pnpm serve:app

# 指定应用, 多个应用名使用逗号拼接: pnpm serve:app <appName>
# 如: `example` 应用
pnpm serve:app example

# 按提示选择应用 (多选)
pnpm serve:app c | pnpm serve:app choices
```

### Compiles and minifies for production

```bash
# `applications` 下的应用
pnpm build:app

# 指定应用, 多个应用名使用逗号拼接: pnpm build:app <appName>
# 如: `example` 应用
pnpm build:app example

# 按提示选择应用 (多选)
pnpm build:app c | pnpm build:app choices
```

### Lints and fixes files

```bash
# eslint
pnpm lint

# stylelint
pnpm lint:stylelint
```

### Git commit

> 需符合 [commitlint](https://github.com/conventional-changelog/commitlint/#what-is-commitlint) 提交规范, 否则不能提交

```
<type>(<scope>): <jiraId> <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

[参考说明](https://www.jianshu.com/p/201bd81e7dc9)

辅助命令

```bash
pnpm commit | pnpm exec git-cz
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
