import { computed, ref } from 'vue';
import { defineStore } from '@hetu/core';
import type { FileInfo } from '../models';
import type { FileTree, FileTreeNode } from '../utils';

export function filePageStore<F extends K, K extends FileInfo = FileInfo, T = FileTree<F, K>>() {
  const active = ref<F | F[]>();
  const preActiveId = ref();
  const hover = ref<F>();
  const hoverId = ref('');
  const open = ref<FileTreeNode<F>>();

  const current = computed(() => {
    if (hover.value) return hover.value;

    if (active.value) {
      if (Array.isArray(active.value)) {
        if (active.value.length) {
          return active.value[0];
        }
      } else {
        return active.value;
      }
    }

    if (open.value?.id) return open.value;

    return undefined;
  });

  const parents = computed(() => {
    const arr: FileTreeNode<F>[] = [];
    if (!open.value || !open.value.id) {
      return arr;
    }

    arr.push(open.value);
    const parent = open.value.getParent(false);
    parent && arr.push(parent as FileTreeNode<F>);
    return arr;
  });

  let timer: ReturnType<typeof setTimeout>;
  let preHover: F | undefined;
  const setHover = (file?: F) => {
    if (file) {
      clearTimeout(timer);
      if (hoverId.value && (!preHover || preHover.id !== hoverId.value)) {
        preHover = hover.value || file;
      }
      hover.value = file;
      return;
    }

    if (!hoverId.value) {
      preHover = undefined;
    }

    timer = setTimeout(() => {
      if (hoverId.value) {
        hover.value = preHover;
        return;
      }
      hover.value = undefined;
    }, 50);
  };

  const setOpen = (node?: FileTreeNode<F>) => {
    reset();
    open.value = node;
  };

  const reset = () => {
    active.value = undefined;
    hover.value = undefined;
    clearTimeout(timer);
  };

  let outsideState = {};
  const addState = (state: Record<string, any>) => {
    outsideState = state;
    Object.assign(result, state);
  };

  const action = ref<{
    name: string;
    args: any[];
  }>();

  const setAction = (name: string, args: any[] = []) => {
    action.value = { name, args };
  };

  const clear = () => {
    const id = hover.value?.id;
    const openId = open.value?.id;
    reset();

    open.value = undefined;
    action.value = undefined;
    // 点击文件进入，则清空数据，否则将记录父文件夹id，用于返回重置文件夹层级状态
    preActiveId.value = id ? undefined : openId;
    // preActiveId.value = undefined;
    delete (result as Partial<ResultType>).tree;

    Object.keys(outsideState).forEach((key) => {
      delete result[key as keyof typeof result];
    });
  };

  type ResultType = {
    /** 文件树 */
    tree: T;
  } & typeof result;

  const result = {
    /** 当前选中的文件 */
    active,
    preActiveId,
    /** 当前滑过的文件 */
    hover,
    hoverId,
    /** 当前文件 */
    current,
    /** 当前打开的文件夹 */
    open,

    /** 上级文件夹 */
    parents,

    setHover,
    setOpen,
    addState,

    action,
    setAction,

    clear
  };

  return result as ResultType;
}

export const useFilePageStore = defineStore(filePageStore) as typeof filePageStore;
