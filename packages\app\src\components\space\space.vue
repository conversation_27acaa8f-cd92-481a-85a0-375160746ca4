<template>
  <div class="ht-spaces" :class="{ 'is-mini row justify-end': mini }">
    <div v-if="isSubAccount" class="ht-space is-active">
      <q-avatar :icon="!activeTeam ? 'person' : undefined" size="36px" color="primary" text-color="white">
        <q-img v-if="activeTeam && activeTeam.avatar" :src="avatar(activeTeam.avatar)">
          <template v-slot:error>
            <q-icon name="person" class="absolute-center" />
          </template>
        </q-img>
        <span v-else>
          {{ activeTeam ? activeTeam.name.toUpperCase().charAt(0) : '空' }}
        </span>
      </q-avatar>
      <span class="col q-pl-sm ellipsis">{{ activeTeam ? activeTeam.name : '我的空间' }}</span>
    </div>
    <template v-else>
      <div class="ht-space" :class="{ 'is-active': !domain }" @click="switchSpace()">
        <q-avatar
          :icon="!user.avatar ? 'person' : undefined"
          size="36px"
          class="bg-font-placeholder"
          text-color="white"
        >
          <q-img v-if="user.avatar" :src="avatar(user.avatar)">
            <template v-slot:error>
              <q-icon name="person" class="absolute-center" />
            </template>
          </q-img>
        </q-avatar>
        <span class="col q-pl-sm ellipsis">个人空间</span>
      </div>

      <div class="ht-space q-mt-sm" :class="{ 'is-active': domain }">
        <q-avatar size="36px" color="primary" text-color="white">
          <q-img v-if="activeTeam && activeTeam.avatar" :src="avatar(activeTeam.avatar)">
            <template v-slot:error>
              <span class="row flex-center full-height">{{ activeTeam.name.toUpperCase().charAt(0) }}</span>
            </template>
          </q-img>
          <span v-else>{{ activeTeam ? activeTeam.name.toUpperCase().charAt(0) : '空' }}</span>
        </q-avatar>
        <span class="col q-ml-sm ellipsis">{{ activeTeam ? activeTeam.name : '我的空间' }} </span>
        <q-icon name="arrow_drop_down" size="xs" />

        <q-popup-proxy
          class="ht-spaces-popup pa-3 text-font-primary"
          anchor="bottom left"
          self="center right"
          :offset="[10, 0]"
          breakpoint="0"
          @before-show="showTeamsPopup"
        >
          <div class="text-base line-height-4 mb-3">空间列表</div>

          <q-scroll-area>
            <template v-if="loading">
              <div v-for="n in 3" :key="n" class="pa-2.5 mb-2 flex items-center">
                <q-skeleton size="36px" />
                <div class="col q-px-sm">
                  <q-skeleton type="text" height="18px" width="30%" />
                  <q-skeleton type="text" height="18px" width="70%" />
                </div>
                <q-skeleton height="30px" width="56px" />
              </div>
            </template>
            <template v-else>
              <div
                v-for="team in teams"
                :key="team.id"
                class="ht-space-team"
                :class="{ 'is-active': domain === team.domain }"
                v-close-popup
                @click="switchSpace(team.domain)"
              >
                <q-avatar size="36px" color="primary" text-color="white" rounded>
                  <q-img v-if="team && team.avatar" :src="avatar(team.avatar)">
                    <template v-slot:error>
                      <span class="row flex-center full-height">{{ team.name.toUpperCase().charAt(0) }}</span>
                    </template>
                  </q-img>
                  <span v-else>{{ team.name.toUpperCase().charAt(0) }}</span>
                </q-avatar>
                <div class="col q-pl-sm">
                  <div class="ellipsis">{{ team.name }}</div>
                  <div class="text-xs text-font-secondary ellipsis">{{ team.summary || '--' }}</div>
                </div>
                <q-btn
                  v-if="!team.owner"
                  v-acl="acl.quit"
                  label="退出"
                  color="negative"
                  size="sm"
                  unelevated
                  v-close-popup
                  @click.stop="quitTeam(team)"
                />
              </div>
            </template>
          </q-scroll-area>

          <q-btn
            v-acl="acl.create"
            class="full-width mt-3"
            :disabled="loading"
            label="创建空间"
            color="primary"
            unelevated
            v-close-popup
            @click="createTeam"
          />
        </q-popup-proxy>
      </div>
    </template>
  </div>
</template>

<style src="./space.scss" lang="scss"></style>
<script src="./space.ts" lang="ts"></script>
