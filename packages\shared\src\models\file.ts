import type { BaseModel } from '@hetu/http';

/**
 * 文件信息
 */
export interface FileInfo extends BaseModel {
  id: string;

  /** 名称 */
  title: string;

  /** 父级ID */
  parentId: string;

  /**
   * 数据类别
   * - 0: 文件
   * - 1: 文件夹
   */
  dataType: number;

  /** 文件类别 */
  category: string;

  /** 是否星标 */
  favorite: 0 | 1;

  attachmentId?: string;
}

/**
 * 文件列表响应数据模型
 */
export interface FileData<T extends K, K extends FileInfo = FileInfo> {
  /** 文件夹列表 */
  directorys: K[];

  /** 文件列表 */
  files: T[];
}
