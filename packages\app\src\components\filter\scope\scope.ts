import { defineComponent, ref, type PropType, computed } from 'vue';
import { ScopeDateType, ScopeDateTypeName, ScopeFilter } from '../../../models';

/**
 * 筛选器-相对日期
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-filter-scope',
  props: {
    filter: {
      type: Object as PropType<ScopeFilter>,
      required: true
    },
    dataType: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const scopeFilter = ref(props.filter);

    const isDate = computed(() => {
      return ['date', 'date_1', 'date_2'].includes(props.dataType);
    });

    const isDateTime = computed(() => {
      return ['datetime', 'datetime_1', 'datetime_2'].includes(props.dataType);
    });

    const format = computed(() => {
      return isDate.value ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss';
    });

    const options = computed(() => {
      const list: {
        label: string;
        value: string;
      }[] = [];
      for (const key in ScopeDateType) {
        list.push({ label: (ScopeDateTypeName as any)[key], value: key });
      }
      return list;
    });

    const onChangeType = (value: string) => {
      scopeFilter.value.dateType = scopeFilter.value.dateType === value ? '' : (value as any);
    };

    return { scopeFilter, options, ScopeDateType, ScopeDateTypeName, isDate, isDateTime, format, onChangeType };
  }
});
