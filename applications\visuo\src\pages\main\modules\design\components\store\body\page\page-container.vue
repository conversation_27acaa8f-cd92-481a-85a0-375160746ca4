<template>
  <div class="vis-store-page-container">
    <!-- 页面 -->
    <vis-store-page :pagesHeight="pagesHeight" />
    <q-separator
      class="vis-store-page-container-separator"
      @mousedown="onDragStart"
      :class="{ 'is-dragging': isDragging }"
    />
    <!-- 图层 -->
    <vis-store-layer :pagesHeight="pagesHeight" />
  </div>
</template>

<script lang="ts" src="./page-container.ts"></script>
<style lang="scss" src="./page-container.scss"></style>
