import {
  DirectionType,
  GRAPH_SIZE_MIN,
  TabResizeType,
  SelectMode,
  type TabOptions,
  IconOption,
  TabStyle,
  FillPaints,
  Stroke,
  Effects,
  TabItemStyle
} from '@vis/document-core';
import { computed, defineComponent, onMounted, ref, type PropType } from 'vue';
import VisAspect from '../../property/aspect/index.vue';
import VisConfigTabStatus from './status/status.vue';

/**
 * <AUTHOR>
 * 选项卡组件属性面板
 */
export default defineComponent({
  name: 'vis-config-tab-option',
  components: {
    VisAspect,
    VisConfigTabStatus
  },
  props: {
    options: {
      type: Object as PropType<TabOptions>,
      required: true
    }
  },
  setup(props) {
    const selectMode = ref(SelectMode.Single);

    const hoverItem = ref('text');

    const activeItem = ref('text');
    const tabOptions = computed(() => props.options);
    const tabStyle = computed(() => props.options.style);

    const selectOptions = [
      {
        value: SelectMode.Single,
        label: '单选模式'
      },
      {
        value: SelectMode.Multiple,
        label: '多选模式'
      }
    ];

    const overflowOptions = [
      {
        value: 0,
        label: '溢出'
      },
      {
        value: 1,
        label: '省略号'
      },
      {
        value: 2,
        label: '换行'
      },
      {
        value: 3,
        label: '跑马灯'
      }
    ];

    const textDirectionOptions = [
      {
        value: DirectionType.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Horizontal}`,
        tip: '水平'
      },
      {
        value: DirectionType.Vertical,
        label: '',
        icon: `hticon-vis-layout-${DirectionType.Vertical}`,
        tip: '垂直'
      }
    ];

    /**
     * 修改选择模式
     * @param val
     */
    const onChangeSelectMode = (val: SelectMode) => {
      tabOptions.value.multiple = val === SelectMode.Multiple ? true : false;
    };

    const showMenuWidth = ref(false);
    const showMenuHeight = ref(false);

    const onChangeResize = (type: 'resizeX' | 'resizeY', resize: TabResizeType) => {
      tabOptions.value[type] = resize;
    };

    //#region --------- 圆角 ---------------
    const radius = ref(4);

    const showRadius = ref(false);

    const radiusChange = (val: number) => {
      tabOptions.value.style.radius = [val, val, val, val];
    };

    //#endregion --------- 圆角 ---------------

    //#region ------------图标 ------------
    const toggleIcon = () => {
      if (tabOptions.value.icon) {
        tabOptions.value.icon = undefined;
      } else {
        tabOptions.value.icon = new IconOption();
      }
    };
    //#endregion ----------图标 -----------

    //#region-------------- 填充 描边 特效 --------------

    const addStyle = () => {
      tabOptions.value.style.background = new FillPaints();
    };

    const deleteStyle = () => {
      tabOptions.value.style.background = undefined;
    };

    const toggleStyle = (key: 'shadow' | 'border') => {
      if (key === 'shadow') {
        tabOptions.value.style.shadow = tabOptions.value.style.shadow ? undefined : new Effects();
      }
      if (key === 'border') {
        tabOptions.value.style.border = tabOptions.value.style.border ? undefined : new Stroke();
      }
    };

    //#endregion-------------- 填充 描边 特效 ---------------

    //#region ---------------- 状态 ----------------------

    const addStatus = (status: 'hover' | 'active') => {
      tabStyle.value[status] = new TabItemStyle();
    };

    const deleteStatus = (status: 'hover' | 'active') => {
      if (tabStyle.value[status]) {
        tabStyle.value[status] = undefined;
      }
    };

    //#endregion ---------------- 状态 ----------------------

    onMounted(() => {
      selectMode.value = tabOptions.value.multiple ? SelectMode.Multiple : SelectMode.Single;
    });

    return {
      selectMode,
      tabOptions,
      tabStyle,
      selectOptions,
      overflowOptions,
      textDirectionOptions,
      hoverItem,
      activeItem,
      TabResizeType,
      showMenuWidth,
      showMenuHeight,
      radius,
      showRadius,
      onChangeSelectMode,
      onChangeResize,
      radiusChange,
      toggleIcon,
      addStyle,
      deleteStyle,
      toggleStyle,
      addStatus,
      deleteStatus
    };
  }
});
