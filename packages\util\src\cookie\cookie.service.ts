import type { CookieOptions } from './interface';

/**
 * Cookie 操作类
 * @static
 * <AUTHOR>
 */
export class CookieService {
  private static get doc(): Document {
    return document;
  }

  /** 原始 Cookie 值 */
  static get cookie(): string {
    return this.doc.cookie;
  }

  /**
   * 获取所有 Cookie 键值对
   */
  static getAll(): { [key: string]: string } {
    const ret: { [key: string]: string } = {};
    const arr = this.cookie.split('; ');
    arr.forEach((cookie) => {
      const index = cookie.indexOf('=');
      if (index > 0) {
        const name = decodeURIComponent(cookie.substring(0, index));
        if (ret[name] == null) {
          ret[name] = decodeURIComponent(cookie.substring(index + 1));
        }
      }
    });
    return ret;
  }

  /**
   * 获取指定 `key` 的值
   * @param key `key` 值
   */
  static get(key: string): string | undefined {
    return this.getAll()[key];
  }

  /**
   * 设置指定 Cookie 键的值
   * @param key `key` 值
   * @param value `value` 值
   * @param options `cookie` 参数 (可选)
   */
  static put(key: string, value: string | undefined, options?: CookieOptions): void {
    const opt = { path: '/', ...options } as CookieOptions;
    if (typeof opt.expires === 'number') {
      opt.expires = new Date(+new Date() + opt.expires * 1000);
    }
    if (typeof opt.expires !== 'string') {
      opt.expires = opt.expires ? opt.expires.toUTCString() : '';
    }
    const optStr: { [key: string]: string | boolean } = opt as any;
    const attributes = Object.keys(optStr)
      .filter((k) => optStr[k] && optStr[k] !== true)
      .map((k) => `${k}=${(optStr[k] as string).split(';')[0]}`)
      .join(';');
    this.doc.cookie =
      encodeURIComponent(String(key)) + '=' + encodeURIComponent(String(value)) + (attributes ? '; ' + attributes : '');
  }

  /**
   * 移除指定 Cookie
   * @param key `key` 值
   * @param options `cookie` 参数 (可选)
   */
  static remove(key: string, options?: CookieOptions): void {
    const exp = new Date();
    exp.setDate(exp.getDate() - 1);
    this.put(key, undefined, { ...options, expires: exp });
  }

  /**
   * 移除所有 Cookies
   */
  static removeAll(): void {
    this.doc.cookie = '';
  }
}
