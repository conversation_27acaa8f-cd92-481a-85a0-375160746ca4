<template>
  <ht-app-page class="ht-app-recycle" :isEmptyBody="!(queryRecycle && queryRecycle.length) && !loading">
    <template #header>
      <label class="col text-h5 text-bold">回收站</label>
      <div class="col row flex-center">
        <q-input
          v-model="query.search"
          class="ht-field ht-field--medium ht-field--light"
          placeholder="搜索"
          dense
          rounded
          standout
          @keyup.enter="search"
          @blur="search"
        >
          <template v-slot:prepend>
            <q-icon name="search" size="20px" />
          </template>
        </q-input>
      </div>
      <div class="col">
        <div class="row justify-end q-gutter-x-md">
          <ht-app-sort
            v-show="!selected.length || (selected.length && $q.screen.gt.md)"
            :order="query.order"
            :columns="sortColumns"
            @sort="sort"
          />
          <template v-if="selected.length">
            <label class="ht-link" v-acl="acl.restore" @click="onRestore()">
              <ht-icon name="undo" />
              <span class="q-ml-xs">还原</span>
            </label>
            <label class="ht-link" v-acl="acl.delete" @click="onDel()">
              <q-icon name="delete_outline" size="xs" />
              <span class="q-ml-xs">删除</span>
            </label>
          </template>
          <q-checkbox
            v-acl="acl.selectAll"
            v-model="selectall"
            label="全选"
            @update:model-value="onChangeSelectall"
            size="xs"
          />
        </div>
      </div>
    </template>
    <q-table
      v-if="queryRecycle && queryRecycle.length"
      :rows="queryRecycle"
      :columns="columns"
      flat
      class="ht-app-recycle-list"
      card-class="!text-font-regular"
      v-model:selected="selected"
      :rows-per-page-options="[0]"
      selection="multiple"
      row-key="dataId"
      hide-header
      hide-bottom
      @update:selected="onActive"
    >
      <template v-slot:header-selection="scope">
        <q-checkbox v-model="scope.selected" size="xs" />
      </template>
      <template v-slot:body-cell-objectName="props">
        <td :props="props" class="text-center w-25.5">
          <slot name="objectSign" :row="props.row"></slot>
        </td>
      </template>
      <template v-slot:body-cell-name="props">
        <td :props="props">
          <span>{{ props.row.objectName }}</span>
          <div class="text-ellipsis">
            <slot name="objectType" :row="props.row"></slot>
          </div>
        </td>
      </template>
      <template v-slot:body-cell-creatorName="props">
        <q-td :props="props">
          <div class="text-font-placeholder">删除人</div>
          {{ props.value }}
        </q-td>
      </template>
      <template v-slot:body-cell-createdTime="props">
        <q-td :props="props">
          <div class="text-font-placeholder">删除时间</div>
          {{ props.value }}
        </q-td>
      </template>
      <template #body-cell-operate="props">
        <q-td :props="props">
          <div class="flex justify-end q-gutter-x-sm">
            <label class="ht-link" v-acl="acl.restore">
              <ht-icon name="undo" @click="onRestore(props.row)" />
              <q-tooltip :delay="200">还原</q-tooltip>
            </label>
            <label class="ht-link" v-acl="acl.delete">
              <q-icon name="delete_outline" size="xs" @click="onDel(props.row)" />
              <q-tooltip :delay="200">删除</q-tooltip>
            </label>
          </div>
        </q-td>
      </template>
    </q-table>
  </ht-app-page>
</template>
<script src="./recycle.ts" lang="ts"></script>
