import type { Records } from '@hetu/util';
import { Tab, WidgetConfig } from '../models';
import { WidgetName, WidgetGroup, WidgetSubGroup, WidgetType } from '../models/block/config/widget-enum';
import { WidgetConfigDataFactory } from './widget-config-data.factory';

/**
 * 组件配置工厂类
 * 用于管理和创建各种组件的配置信息
 * <AUTHOR>
 */
export class WidgetConfigFactory {
  private readonly dataFactory: WidgetConfigDataFactory;
  private _configs: Records<WidgetConfig> = {};

  constructor() {
    this.dataFactory = new WidgetConfigDataFactory();
    this._configs = this.createConfigs();
  }

  /**
   * 获取所有组件配置
   */
  public get configs(): Records<WidgetConfig> {
    return this._configs;
  }

  /**
   * 获取指定组件的配置
   * @param type 组件名称
   */
  public getConfig(type: WidgetType): WidgetConfig | undefined {
    return this._configs[type];
  }

  /**
   * 创建组件配置
   * @private
   */
  private createConfigs(): Records<WidgetConfig> {
    const configs: Records<WidgetConfig> = {};

    // 段落组件配置
    configs[WidgetType.Paragraph] = new WidgetConfig(
      WidgetType.Paragraph,
      WidgetName.Paragraph,
      WidgetName.Paragraph,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      180,
      100,
      this.dataFactory[WidgetType.Paragraph].datasetType as 'static' | 'dynamic',
      this.dataFactory[WidgetType.Paragraph].static
    );

    // 标题组件配置
    configs[WidgetType.Title] = new WidgetConfig(
      WidgetType.Title,
      WidgetName.Title,
      WidgetName.Title,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      150,
      50,
      this.dataFactory[WidgetType.Title].datasetType as 'static' | 'dynamic',
      this.dataFactory[WidgetType.Title].static
    );

    // 选项卡组件配置
    configs[WidgetType.Tab] = new WidgetConfig(
      WidgetType.Tab,
      WidgetName.Tab,
      WidgetName.Tab,
      WidgetGroup.Words,
      WidgetSubGroup.Interactive,
      400,
      50,
      this.dataFactory[WidgetType.Tab].datasetType as 'static' | 'dynamic',
      this.dataFactory[WidgetType.Tab].static
    );

    // 文本输入组件配置
    configs[WidgetType.Input] = new WidgetConfig(
      WidgetType.Input,
      WidgetName.Input,
      WidgetName.Input,
      WidgetGroup.Words,
      WidgetSubGroup.Form,
      240,
      40,
      this.dataFactory[WidgetType.Input].datasetType as 'static' | 'dynamic',
      this.dataFactory[WidgetType.Input].static
    );

    return configs;
  }
}
