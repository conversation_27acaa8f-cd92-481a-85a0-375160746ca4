import { ref } from 'vue';
import { useUtilConfig } from '../config';
import type { QTable } from 'quasar';

// NOTE: 此值会被 `<q-table :pagination.sync="pagination" />` 转为对象字面量, 请勿添加原型方法
/**
 * `<q-table :pagination="" />` 所用分页 Model
 */
export class TablePagination<T = Record<string, any>> {
  /** 每页条数, 为 `0` 时表示查询所有数据 */
  rowsPerPage = 10;

  /** 总条数 */
  rowsNumber = 0;

  /** 页码 */
  page = 1;

  /** 排序字段 */
  sortBy?: keyof T;

  /** 是否为降序 */
  descending?: boolean;

  /** 总页数 */
  totalPages = 0;

  /** 跳转到某页 */
  jumpPage = '1';
}

/**
 * `QTable` 组件使用
 *
 * #### Example:
 * *Template:*
 * ```html
 * <template>
 *  <q-table :pagination.sync="pagination.model" @request="onRequest" />
 * </template>
 *
 * <script setup lang='ts'>
 *  const pagination = useTablePagination();
 *  function onRequest(payload: { pagination: TablePagination<T> }) {
 *    pagination.updateByRequest(payload);
 *    ...
 *  }
 * </script>
 * ```
 */
export const useTablePagination = <T = Record<string, any>>() => {
  const tableRef = ref<QTable>();

  const loading = ref(false);

  const pagination = ref(new TablePagination<T>());

  const rowsPerPageOptions = () => {
    const { rowsPerPageOptions } = useUtilConfig();
    return rowsPerPageOptions === 'default' ? undefined : rowsPerPageOptions;
  };

  const resolveJumpPage = (): boolean => {
    const pn = Number.parseInt(pagination.value.jumpPage, 10);

    // 检查输入的值是否是在页码范围内的不同数字
    if (!Number.isNaN(pn) && pn !== pagination.value.page && pn > 0 && pn <= pagination.value.totalPages) {
      pagination.value.page = pn;
      return true;
    }

    // 还原旧值
    pagination.value.jumpPage = '' + pagination.value.page;
    return false;
  };

  const updateTotalPages = (rowsNumber?: number) => {
    pagination.value.rowsNumber = rowsNumber ?? pagination.value.rowsNumber;
    // `rowsPerPage` 为 `0` 时, 意为查询所有数据, 仅返回一页
    pagination.value.totalPages =
      pagination.value.rowsPerPage === 0 ? 1 : Math.ceil(pagination.value.rowsNumber / pagination.value.rowsPerPage);
  };

  const updateByRequest = (payload: { pagination: Partial<TablePagination<T>> }) =>
    Object.assign(pagination.value, payload.pagination);

  return {
    tableRef,
    loading,
    pagination,
    /** 每页数量选项配置 */
    rowsPerPageOptions,
    /**
     * 通过 `jumpPage` 更新 `page` 值
     * @returns `page` 是否被更新, 当返回 `true` 时可执行发送请求等操作
     * */
    resolveJumpPage,
    /** 更新 `totalPages` 值 */
    updateTotalPages,
    updateByRequest
  };
};
