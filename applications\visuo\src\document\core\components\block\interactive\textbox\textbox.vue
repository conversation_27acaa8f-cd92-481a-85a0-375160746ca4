<template>
  <div class="vis-text-content fit" style="padding: var(--border-width, initial)">
    <q-scroll-area v-if="isFixed" class="fit" :content-style="{ height: '100%' }">
      <div class="vis-text-content fit" :style="textStyle">
        <span v-html="content" style="margin: auto"></span>
      </div>
    </q-scroll-area>
    <div v-else class="vis-text-content fit" :style="textStyle">
      <span v-html="content" :style="adaptStyle"></span>
    </div>
  </div>
</template>
<script lang="ts" src="./textbox.ts"></script>
