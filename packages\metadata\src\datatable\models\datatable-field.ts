import { BaseModel } from '@hetu/http';

/**
 * 数据表字段对象
 * <AUTHOR>
 */
export class DatatableField extends BaseModel {
  /**
   * 字段名
   */
  fieldName?: string;

  /**
   * 别名
   */
  fieldAlias?: string;

  /**
   * 字段类型
   */
  fieldDatatype?: string;

  /**
   * 描述
   */
  remark?: string;

  /**
   * 辅助字段，判断字段名称是否合法
   */
  isIllegalFieldAlias = false;
}

/**
 * 数据表字段对象数组
 * @type
 */
export type DatatableFields = DatatableField[];
