import { setupThemeConfig, type ThemeConfig } from './config';

export * from './preloader';
export * from './settings/settings.service';
export * from './settings/model';
export * from './settings/layout';
export * from './title/title.service';
export * from './nav/nav.service';
export * from './nav/model';
export * from './scss-var';
export * from './color';

export const ThemeModule = {
  install(app: unknown, config?: Partial<ThemeConfig>) {
    if (!config) return;

    setupThemeConfig(config);
  }
};
