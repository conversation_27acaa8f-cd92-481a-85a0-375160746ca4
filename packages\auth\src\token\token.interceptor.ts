import type { HttpRequestConfig, HttpRequestFullInterceptor } from '@hetu/http';
import { TokenService } from './token.service';

/**
 * Http 请求 Token 拦截器
 * <AUTHOR>
 */
export class HttpTokenInterceptor implements HttpRequestFullInterceptor {
  protected get model() {
    return TokenService.get();
  }

  intercept(config: HttpRequestConfig): HttpRequestConfig {
    let ignore = false;
    const { ignores, allowAnonymousKey, sendKey, sendPlace, sendTemplate } = TokenService.config;

    // 验证当前请求地址是否不进行 token 检查
    if (ignores) {
      for (const regex of ignores) {
        if (regex.test(config.url || '')) {
          ignore = true;
          break;
        }
      }
    }

    // 验证是否有忽略 token 检查标识
    if (!ignore && allowAnonymousKey) {
      ignore =
        (config.params && config.params[allowAnonymousKey]) ||
        (config.data && config.data[allowAnonymousKey]) ||
        // eslint-disable-next-line no-useless-escape
        new RegExp(`[\?|&]${allowAnonymousKey}=[^&]+`).test(config.url || '');
    }

    // 不忽略 token, 按配置将 token 放在请求信息中
    if (!ignore) {
      let token = this.model.token;

      // 判断 token 是否存在,
      if (token) {
        let place = config.headers;
        if (sendPlace === 'body') {
          place = config.data;
        } else if (sendPlace === 'url') {
          place = config.params;
        }

        // 替换模板
        token = sendTemplate!.replace(/\$\{([\w]+)\}/g, (_: string, g) => this.model[g]);

        place[(this.model.key || sendKey)!] = token;
      } else {
        // TODO: 中断请求并跳转登录页, 待定:
        // 考虑到后台有忽略 token 的请求地址配置, 如果中断则忽略 token 的请求需在前端同步配置
        // return;
      }
    }

    return config;
  }

  error(error: any) {
    throw new Error(`[Request] Token Interceptor Error: ${error}`);
  }
}
