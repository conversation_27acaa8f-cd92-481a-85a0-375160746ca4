import { defineComponent } from 'vue';
import { PageService, PlatformService, SpaceService } from '@hetu/platform-shared';
import { useMaterialDialog } from '../../hooks';

export default defineComponent({
  name: 'ht-resourcebar',
  props: {
    mini: {
      type: <PERSON>olean
    }
  },
  setup() {
    const acl = PlatformService.acl;
    const openMarket = () => {
      const app = import.meta.env.VITE_HETU_MARKET_KEY;
      PageService.openApp(app, SpaceService.getDomainCode());
    };

    const openMaterial = async () => {
      const { HtMaterialDialog } = await useMaterialDialog();
      HtMaterialDialog();
    };

    return {
      acl,
      openMarket,
      openMaterial
    };
  }
});
