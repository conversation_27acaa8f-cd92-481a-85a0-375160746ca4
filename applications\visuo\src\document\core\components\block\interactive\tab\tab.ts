import { computed, defineComponent, ref, type CSSProperties, type PropType, onMounted, watch, nextTick } from 'vue';
import {
  Block,
  DirectionType,
  Effects,
  FillPaints,
  Font,
  ImageType,
  Stroke,
  Tab,
  TabOption,
  TabResizeType,
  Text,
  TextAlign,
  VerticalAlign
} from '../../../../models';
import { useBase, useFill, useUiStyle } from '../../../../hooks';
import { AttachmentService } from '@hetu/platform-shared';

/**
 * <AUTHOR>
 * 选项卡组件
 */
export default defineComponent({
  name: 'vis-tab',
  props: {
    widget: {
      type: Object as PropType<Tab>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    const { getStrokeStyle, getEffectStyle, createStyleVar, getTextStyle, getTextEffectStyle, getTextOverflow } =
      useUiStyle();
    const { getFillPaintsStyle } = useFill();
    useBase();

    const data = ref<TabOption[]>((props.widget as any)?.staticData || []);
    const tabOption = computed(() => props.widget.options);
    const marqueeRefs = ref<HTMLElement[]>([]);

    const tabContentRef = ref<HTMLElement[]>([]);

    const changLineRefs = ref<HTMLElement[]>([]);

    const tabRef = ref<HTMLElement[]>([]);

    const hoverIndex = ref(-1);

    //#region 选中
    const getIndex = () => {
      if (typeof tabOption.value.defaultIndex === 'number') return [tabOption.value.defaultIndex - 1];
      if (tabOption.value.multiple) {
        // 多选
        return tabOption.value.defaultIndex
          .split(',')
          .map(Number)
          .filter((num) => !isNaN(num))
          .map((num) => num - 1);
      } else {
        // 单选
        const defaultArr = tabOption.value.defaultIndex.split(',');
        if (defaultArr.length) {
          // 取第一个
          return isNaN(Number(defaultArr[0])) ? [] : [Number(defaultArr[0]) - 1];
        } else {
          // 没有项返回空数组
          return [];
        }
      }
    };
    /**
     * 选中项
     */
    const selectedIndexs = ref(getIndex());

    watch(
      () => tabOption.value.defaultIndex,
      () => {
        selectedIndexs.value = getIndex();
      }
    );

    watch(
      () => tabOption.value.multiple,
      (val) => {
        if (!val) {
          selectedIndexs.value = getIndex();
        }
      }
    );

    const handleActive = (idx: number) => {
      if (tabOption.value.multiple) {
        // 多选
        if (selectedIndexs.value.includes(idx)) {
          const index = selectedIndexs.value.findIndex((item) => item === idx);
          index != -1 && selectedIndexs.value.splice(index, 1);
        } else {
          selectedIndexs.value.push(idx);
        }
      } else {
        // 单选
        if (selectedIndexs.value.includes(idx)) return;
        selectedIndexs.value = [idx];
      }
    };

    //#endregion 选中

    /**
     * 跑马灯滚动速度值
     */
    const SPEED = 30;

    /**
     * 存储每个tab的是否溢出
     */
    const textOverflowFlags = ref<boolean[]>(Array((props.widget as any)?.staticData?.length || 0).fill(false));

    const layout = computed(() => tabOption.value.layout);

    const tabStyle = computed(() => {
      const grid = {
        display: 'grid',
        gridTemplateColumns: `repeat(${layout.value.column}, ${
          tabOption.value.resizeX === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.width + 'px'
        })`,
        gridTemplateRows: `repeat(${layout.value.row},${
          tabOption.value.resizeY === TabResizeType.Adapt ? 'minmax(0, 1fr)' : tabOption.value.height + 'px'
        })`,
        gridColumnGap: layout.value.gutter[1] + 'px',
        columnGap: layout.value.gutter[1] + 'px',
        gridRowGap: layout.value.gutter[0] + 'px',
        rowGap: layout.value.gutter[0] + 'px'
      };
      return {
        height:
          tabOption.value.resizeX === TabResizeType.Fixed &&
          layout.value.scrollbar &&
          tabOption.value.resizeY === TabResizeType.Adapt
            ? props.block.height + 'px'
            : '100%',
        ...grid
      };
    });

    const tabItemStyle = (index: number) => {
      const style = tabOption.value.style;
      const width = tabOption.value.resizeX === TabResizeType.Adapt ? '100%' : tabOption.value.width + 'px';
      const height = tabOption.value.resizeY === TabResizeType.Adapt ? '100%' : tabOption.value.height + 'px';
      const fill = getFillPaintsStyle(style.background ? [style.background as FillPaints] : undefined);
      const stroke = getStrokeStyle(style.border);
      const effects = getEffectStyle(style.shadow);
      const textStyle = getTextStyle(style.font as unknown as Text, style.textDirection === DirectionType.Vertical);
      const textShadow = getTextEffectStyle(style.font.textEffects);
      const varStyle = createStyleVar({ ...fill, ...stroke, ...effects, ...textStyle, ...textShadow });

      // 处理悬浮样式变量
      let varHoverTextStyle = {};
      if (style.hover) {
        const hoverTextStyle = getTextStyle(
          style.hover.font as unknown as Text,
          style.textDirection === DirectionType.Vertical
        );
        const hoverFill = getFillPaintsStyle(
          style.hover.background ? [style.hover.background as FillPaints] : undefined
        );
        const hoverStroke = getStrokeStyle(style.hover.border);
        const hoverEffects = getEffectStyle(style.hover.shadow);
        const hoverTextShadow = getTextEffectStyle(style.hover.font.textEffects, 'hover');
        varHoverTextStyle = createStyleVar(
          { ...hoverTextStyle, ...hoverTextShadow, ...hoverFill, ...hoverStroke, ...hoverEffects },
          'hover'
        );
      }

      // 处理激活样式变量
      let varActiveTextStyle = {};
      if (style.active) {
        const activeTextStyle = getTextStyle(
          style.active.font as unknown as Text,
          style.textDirection === DirectionType.Vertical
        );

        const activeFill = getFillPaintsStyle(
          style.active.background ? [style.active.background as FillPaints] : undefined
        );
        const activeStroke = getStrokeStyle(style.active.border);
        const activeEffects = getEffectStyle(style.active.shadow);
        const activeTextShadow = getTextEffectStyle(style.active.font.textEffects, 'active');
        varActiveTextStyle = createStyleVar(
          { ...activeTextStyle, ...activeTextShadow, ...activeFill, ...activeStroke, ...activeEffects },
          'active'
        );
      }

      // 文本溢出处理
      return {
        width,
        height,
        borderRadius: style.radius.map((r) => r + 'px').join(' '),
        ...varStyle,
        display: 'flex',
        flexDirection: style.textDirection === DirectionType.Horizontal ? 'row' : 'column',
        // 水平对齐
        [style.textDirection === DirectionType.Horizontal ? 'justifyContent' : 'alignItems']:
          style.font.alignHorizontal === TextAlign.Center
            ? 'center'
            : style.font.alignHorizontal === TextAlign.Left
            ? 'flex-start'
            : 'flex-end',
        // 垂直对齐
        [style.textDirection === DirectionType.Horizontal ? 'alignItems' : 'justifyContent']:
          style.font.alignVertical === VerticalAlign.Center
            ? 'center'
            : style.font.alignVertical === VerticalAlign.Top
            ? 'flex-start'
            : 'flex-end',
        ...varHoverTextStyle,
        ...varActiveTextStyle
      } as CSSProperties;
    };

    const tabContentStyle = (index: number) => {
      const style = tabOption.value.style;

      const textStyle = getTextStyle(style.font as unknown as Text, style.textDirection === DirectionType.Vertical);
      const allHeight = tabOption.value.icon
        ? tabOption.value.icon.height + tabOption.value.icon.gutter[0] + tabOption.value.icon.gutter[2]
        : 0;
      const allWidth = tabOption.value.icon
        ? tabOption.value.icon.width + tabOption.value.icon.gutter[1] + tabOption.value.icon.gutter[3]
        : 0;
      const varTextStyle = tabOption.value.icon ? {} : createStyleVar(textStyle);
      return {
        ...overflowStyle.value,
        ...varTextStyle,
        justifyContent:
          style.overflow === 3 && textOverflowFlags.value[index] ? 'flex-start' : textStyle.justifyContent,
        writingMode: style.textDirection === DirectionType.Horizontal ? 'horizontal-tb' : 'vertical-lr',
        textOrientation: 'upright',

        flex: textOverflowFlags.value[index] ? '1' : dealOverflow(index),
        height:
          style.textDirection === DirectionType.Horizontal
            ? '100%'
            : textOverflowFlags.value[index]
            ? `${
                tabOption.value.icon
                  ? tabContentRef.value &&
                    tabContentRef.value[index] &&
                    allHeight < tabContentRef.value[index].clientHeight
                    ? tabContentRef.value[index].clientHeight - allHeight
                    : 0
                  : tabRef.value[index] && tabRef.value[index].clientHeight
              }px`
            : 'auto',
        width:
          style.textDirection === DirectionType.Vertical
            ? '100%'
            : textOverflowFlags.value[index]
            ? `${
                tabOption.value.icon
                  ? tabContentRef.value &&
                    tabContentRef.value[index] &&
                    allWidth < tabContentRef.value[index].clientWidth
                    ? tabContentRef.value[index].clientWidth - allWidth
                    : 0
                  : tabRef.value[index] && tabRef.value[index].clientWidth
              }px`
            : 'auto'
      } as CSSProperties;
    };

    const dealOverflow = (index: number) => {
      if (tabOption.value.style.overflow === 1) {
        if (tabOption.value.style.textDirection === DirectionType.Horizontal) {
          return marqueeRefs.value[index] && marqueeRefs.value[index].scrollWidth > marqueeRefs.value[index].clientWidth
            ? '1'
            : '0 1 auto';
        } else {
          return marqueeRefs.value[index] &&
            marqueeRefs.value[index].scrollHeight > marqueeRefs.value[index].clientHeight
            ? '1'
            : '0 1 auto';
        }
      } else if (tabOption.value.style.overflow === 2) {
        // 换行
        if (tabOption.value.style.textDirection === DirectionType.Horizontal) {
          // 文本水平 检测高度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientHeight > changLineRefs.value[index].clientHeight ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        } else {
          // 文本垂直 检测宽度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientWidth > changLineRefs.value[index].clientWidth ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        }
      }
    };

    const overflowStyle = computed(() => {
      const style = tabOption.value.style;
      const overflow = getTextOverflow(style.overflow);
      return overflow as CSSProperties;
    });

    // 检测文本是否溢出
    const checkTextOverflow = () => {
      nextTick(() => {
        if (!marqueeRefs.value || marqueeRefs.value.length === 0) return;

        // 确保textOverflowFlags数组长度与data相同
        if (textOverflowFlags.value.length !== data.value.length) {
          textOverflowFlags.value = Array(data.value.length).fill(false);
        }

        // 遍历所有元素检查溢出
        marqueeRefs.value.forEach((el, index) => {
          if (!el) return;
          const parentEl = el.parentElement;
          if (!parentEl) return;
          // 检查元素是否溢出
          const isOverflowing =
            tabOption.value.style.textDirection === DirectionType.Horizontal
              ? el.clientWidth > parentEl.clientWidth + 2
              : el.clientHeight > parentEl.clientHeight + 2;
          textOverflowFlags.value[index] = isOverflowing;
        });

        // 强制更新
        textOverflowFlags.value = [...textOverflowFlags.value];
      });
    };

    // 获取每个项目的溢出样式
    const getItemOverflowStyle = (index: number) => {
      // nextTick(() => {
      const style = tabOption.value.style;
      // 确保索引有效
      if (index < 0 || index >= (textOverflowFlags.value.length || 0)) {
        return getTextOverflow(style.overflow) as CSSProperties;
      }
      // 只有在overflow为3（跑马灯）且文本确实溢出时才应用动画
      if (style.overflow === 3 && textOverflowFlags.value[index]) {
        const speed = tabOption.value.style.scrollSpeed > 0 ? SPEED * tabOption.value.style.scrollSpeed : SPEED;
        let duration = SPEED;
        if (marqueeRefs.value[index]) {
          const length =
            style.textDirection === DirectionType.Horizontal
              ? marqueeRefs.value[index].clientWidth
              : marqueeRefs.value[index].clientHeight;
          duration = length / speed;
        }
        return {
          display: 'inline-block',
          animation: `${
            style.textDirection === DirectionType.Horizontal ? DirectionType.Horizontal : DirectionType.Vertical
          }-scroll ${duration}s linear infinite`,
          paddingRight: (style.textDirection === DirectionType.Horizontal ? 5 : 0) + 'px',
          paddingBottom: (style.textDirection === DirectionType.Horizontal ? 0 : 5) + 'px'
        };
      }
      return getTextOverflow(style.overflow) as CSSProperties;
    };

    // 监听数据变化和样式变化，重新检测溢出
    watch(
      [() => data.value, () => props.block.width, () => props.block.height, () => tabOption.value],
      () => {
        checkTextOverflow();
      },
      { deep: true }
    );

    //#region 图标
    const getIconStyle = computed(() => {
      return {
        margin: tabOption.value.icon?.gutter.map((val) => val + 'px').join(' '),
        width: tabOption.value.icon?.width + 'px',
        height: tabOption.value.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    const getHoverIconStyle = computed(() => {
      return {
        margin: tabOption.value.style.hover?.icon?.gutter.map((val) => val + 'px').join(' '),
        width: tabOption.value.style.hover?.icon?.width + 'px',
        height: tabOption.value.style.hover?.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    const getActiveIconStyle = computed(() => {
      return {
        margin: tabOption.value.style.active?.icon?.gutter.map((val) => val + 'px').join(' '),
        width: tabOption.value.style.active?.icon?.width + 'px',
        height: tabOption.value.style.active?.icon?.height + 'px',
        borderRadius: '0px'
      };
    });

    /**
     * 默认状态下的图标url
     */
    const iconUrl = computed(() => {
      if (!tabOption.value.icon || !tabOption.value.icon.image) return '';
      const { type, url } = tabOption.value.icon.image;
      return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
    });

    const shouldShowIcon = (idx: number) => {
      if (hoverIndex.value === idx) {
        // 说明鼠标有悬浮
        if (tabOption.value.style.hover?.visible) {
          //开了眼睛
          return tabOption.value.style.hover.icon.image?.url ? true : false;
        } else {
          // 没开眼睛
          return selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible
            ? tabOption.value.style.active.icon.image?.url
              ? true
              : false
            : !!tabOption.value.icon;
        }
      } else {
        return selectedIndexs.value.includes(idx) && tabOption.value.style.active?.visible
          ? tabOption.value.style.active.icon.image?.url
            ? true
            : false
          : !!tabOption.value.icon;
      }
    };

    /**
     * 悬浮状态下的iconUrl
     */
    const hoverIconUrl = (idx: number) => {
      if (
        hoverIndex.value === idx &&
        tabOption.value.style.hover?.visible &&
        tabOption.value.style.hover?.icon.image?.url
      ) {
        const { type, url } = tabOption.value.style.hover.icon.image;
        return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
      }
      return;
    };

    /**
     * 选中状态下的iconUrl
     * @param idx
     * @returns
     */
    const activeIconUrl = (idx: number) => {
      if (
        selectedIndexs.value.includes(idx) &&
        tabOption.value.style.active?.visible &&
        tabOption.value.style.active?.icon.image?.url
      ) {
        const { type, url } = tabOption.value.style.active.icon.image;
        return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
      }
      return;
    };

    //#endregion 图标

    onMounted(() => {
      // 确保DOM渲染完成后再检测
      checkTextOverflow();
    });

    return {
      tabOption,
      data,
      tabStyle,
      layout,
      TabResizeType,
      overflowStyle,
      marqueeRefs,
      tabContentRef,
      tabRef,
      changLineRefs,
      textOverflowFlags,
      getIconStyle,
      getHoverIconStyle,
      getActiveIconStyle,
      iconUrl,
      hoverIndex,
      selectedIndexs,
      tabItemStyle,
      tabContentStyle,
      getItemOverflowStyle,
      shouldShowIcon,
      hoverIconUrl,
      handleActive,
      activeIconUrl
    };
  }
});
