import type { Paragraph, WidgetBlock } from '../models';

/**
 * 组件公共方法
 * <AUTHOR>
 */
export const useWidget = () => {
  /**
   * 获取组件数据
   * @param widget 组件
   * @returns 组件数据
   */
  const getWidgetData = (widget: WidgetBlock) => {
    if (widget?.datasetType === 'static') {
      return (widget as any)?.staticData[0].content;
    }
  };

  /**
   * 获取组件样式
   * @param widget 组件
   * @returns 组件样式
   */
  const getWidgetStyle = (widget: WidgetBlock) => {
    const style = widget?.options?.style;

    return style;
  };

  /**
   * 获取组件配置
   * @param widget 组件
   * @returns 组件配置
   */
  const getWidgetOptions = (widget: WidgetBlock) => {
    const option = widget?.options;
    return option;
  };

  return { getWidgetData, getWidgetStyle, getWidgetOptions };
};
