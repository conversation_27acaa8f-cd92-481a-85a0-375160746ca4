import { DirectoryACLResource, DirectoryACLResourceCode, FileACLResourceCode } from '@hetu/platform-app';
import { type ACLType } from '@hetu/acl';

/**
 * 素材管理对象
 * <AUTHOR>
 */

export enum ImageACLResourceCode {
  Download = 'download',
  Upload = 'upload'
}

type ACLResourceCode = Record<Partial<keyof typeof ImageACLResourceCode>, string> &
  Record<Partial<keyof typeof FileACLResourceCode>, string> &
  Record<Partial<keyof typeof DirectoryACLResourceCode>, string>;

export class ImageACLResource extends DirectoryACLResource<typeof ImageACLResourceCode> {
  readonly group = 'image';

  /** 文件操作 */
  get toolbar() {
    const code = this.resourceCode as unknown as ACLResourceCode;
    return {
      resource: {
        [this.group]: [code.Move, code.Delete]
      }
    };
  }

  get checkAll() {
    const code = this.resourceCode as unknown as ACLResourceCode;
    return { resource: { [this.group]: [code.Move, code.Delete] } } as ACLType;
  }

  get deleteTool() {
    const code = this.resourceCode as unknown as ACLResourceCode;
    return { resource: { [this.group]: [code.Delete, code.DeleteDirectory] } } as ACLType;
  }

  get moveTool() {
    const code = this.resourceCode as unknown as ACLResourceCode;
    return { resource: { [this.group]: [code.Move, code.MoveDirectory] } } as ACLType;
  }

  constructor() {
    super();
    this.resourceCode = Object.assign({}, ImageACLResourceCode, DirectoryACLResourceCode, FileACLResourceCode);
  }
}
