import { getQueryString, getSpacePath } from '../utils';
import { PlatformService } from './platform.service';

/**
 * 页面服务类
 * @description 云平台以域名的方式切换, 非云平台使用路由参数
 * @see 打开方式:
 *  - 当前页重定向: 初始化, 登录&注册, 平台工作区
 *  - 新窗口: 各应用, 个人设置, 团队管理, 资源广场, 平台工作区(从资源广场进入时)
 * @see 源:
 *  - 主: 初始化, 登录&注册, 个人设置
 *  - 团队(必须有团队域名标识): 团队管理
 *  - 当前: 各应用, 平台主页, 资源广场
 * <AUTHOR>
 */
export class PageService {
  /** 包含平台基础应用页和各子应用页面, 子应用主页 `key` 为子应用标识 */
  private static get pages(): Record<string, string> {
    const pages = import.meta.env.VITE_HETU_PAGES;
    return pages ? JSON.parse(pages) : {};
  }

  /** 协议: `https://` */
  private static get protocol() {
    return location.protocol + '//';
  }

  /** 端口: `:80` */
  private static get port() {
    const port = location.port;
    return port ? ':' + port : port;
  }

  /** 主源: `https://www.aaas.com` */
  private static get mainOrigin() {
    return `${this.protocol}${PlatformService.domain}${this.port}`;
  }

  /**
   * 获取页面地址, 默认为主源
   * @param name 页面名称
   * @param domain 域名标识
   */
  static getPageUrl(name: string, domain?: string) {
    let url = this.getPagePath(name);
    if (!url) {
      return '';
    }
    if (PlatformService.domain) {
      url = `${this.protocol}${domain ? domain + '.' : ''}${PlatformService.domain}${this.port}${
        PlatformService.contextpath
      }${url}#`;
    } else {
      url = `.${url}#${getSpacePath('', domain)}`;
    }
    return url;
  }

  /**
   * 获取主源页面地址: 与租户空间无关的页面
   * @param name 页面名称
   */
  static getMainOriginPageUrl(name: string) {
    let url = this.getPagePath(name);
    if (!url) {
      return '';
    }
    if (PlatformService.domain) {
      url = `${this.mainOrigin}${url}`;
    } else {
      url = `.${url}`;
    }
    return url;
  }

  /**
   * 获取相对于当前页面的指定页面地址
   * @param name 页面名称
   */
  static getRelativePageUrl(name: string) {
    let url = this.getPagePath(name);
    if (!url) {
      return '';
    }

    const baseURI = document.baseURI;
    url = new URL(baseURI + '.' + url, location.href).href;
    return url;
  }

  /**
   * 打开应用主页
   * @param appKey 应用标识
   * @param domain 域名标识
   */
  static openApp(appKey: string, domain?: string) {
    console.log(appKey, this.pages);
    const url = this.getPageUrl(appKey, domain);
    if (!url) {
      return;
    }
    window.open(url);
  }

  /**
   * 打开团队管理页: 在当前域名下打开
   * @param domain 域名标识
   */
  static openTeam(domain: string) {
    if (!domain) {
      return;
    }
    const pageName = import.meta.env.VITE_HETU_TEAM_KEY;
    const url = this.getPageUrl(pageName, domain);
    if (!url) {
      return;
    }
    window.open(url);
  }

  /**
   * 打开平台主页
   * @param domain 域名标识
   */
  static openMain(domain?: string) {
    window.open(this.getPageUrl(import.meta.env.VITE_HETU_PLATFORM_KEY, domain) || './');
  }

  /**
   * 打开个人设置页: 云平台模式通过主域名打开
   */
  static openProfile() {
    const pageName = import.meta.env.VITE_HETU_PROFILE_KEY;
    const url = this.getMainOriginPageUrl(pageName);
    if (!url) {
      return;
    }
    window.open(url);
  }

  /**
   * 进入应用主页
   * @param appKey 应用标识
   * @param domain 域名标识
   */
  static toApp(appKey: string, domain?: string) {
    const url = this.getPageUrl(appKey, domain);
    if (!url) {
      return;
    }
    location.href = url;
  }

  /**
   * 进入平台主页
   * @param domain 域名标识
   */
  static toMain(domain?: string) {
    location.href = this.getPageUrl(import.meta.env.VITE_HETU_PLATFORM_KEY, domain) || './';
  }

  /**
   * 进入登录页
   */
  static toLogin(redirect?: string) {
    const pageName = import.meta.env.VITE_HETU_ACCOUNT_KEY;
    let url = this.getMainOriginPageUrl(pageName);
    if (!url) {
      return;
    }

    const params = [];
    const authingMode = getQueryString('authingMode');
    authingMode && params.push(`authingMode=${authingMode}`);
    redirect && params.push(`redirect=${encodeURIComponent(redirect)}`);
    url = `${url}${params.length ? `?${params.join('&')}` : ''}`;
    location.href = url;
  }

  /**
   * 获取页面路径
   * @param name 页面名称
   */
  private static getPagePath(name: string) {
    const url = this.pages[name];
    if (!url) {
      return;
    }
    return url.startsWith('/') ? url : `/${url}`;
  }
}
