import { merge } from 'lodash-es';
import { DocTitleConfig } from './title/title.service';
import { SettingsService } from './settings/settings.service';
import { addScssVars } from './scss-var';

export interface ThemeConfig {
  userCacheKey?: string;
  scssVars?: string | Record<string, any>;
  docTitleConfig: Partial<DocTitleConfig> | (() => Partial<DocTitleConfig>);
}

/** 默认配置 */
const themeConfig: ThemeConfig = {
  docTitleConfig: new DocTitleConfig()
};

export const setupThemeConfig = (config: Partial<ThemeConfig>) => {
  config.userCacheKey && (SettingsService.userCacheKey = config.userCacheKey);
  config.scssVars && addScssVars(config.scssVars);

  merge(themeConfig, config);
};

export const useThemeConfig = () => themeConfig;
