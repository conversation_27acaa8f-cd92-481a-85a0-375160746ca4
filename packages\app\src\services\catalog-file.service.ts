import { SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import { createSingleClass } from '@hetu/core';
import {
  HttpApiService,
  type HttpApi,
  type ResponseResult,
  responseResult,
  responseReject,
  responseData
} from '@hetu/http';
import type { FileInfo } from '../models';

const API_HOST = `${SYSTEM_API_PREFIX}/catalog/file`;

class CatalogFileApi implements HttpApi {
  fileDelete = `${API_HOST}/delete`;
  fileRecycle = `${API_HOST}/recycle/delete`;
  fileDetail = `${API_HOST}/detail`;
  fileMove = `${API_HOST}/move`;
}

/**
 * 目录文件服务类
 * <AUTHOR>
 */
export class CatalogFileServiceCtor extends HttpApiService<CatalogFileApi> {
  httpApi = new CatalogFileApi();

  httpModuleKey = 'catalog';

  /**
   * 删除
   * @param category 类别
   * @param ids 文件 ID
   */
  delete(category: string, ids: string[]): Promise<ResponseResult | null> {
    return this.http
      .delete(this.api.fileDelete + `/${category}`, { data: { ids } })
      .then(responseResult, responseReject);
  }

  /**
   * 删除到回收站
   * @param category 类别
   * @param ids 文件 ID
   */
  recycle(category: string, ids: string[]): Promise<ResponseResult | null> {
    return this.http
      .delete(this.api.fileRecycle + `/${category}`, { data: { ids } })
      .then(responseResult, responseReject);
  }

  /**
   * 查询文件详情
   * @param category 类别
   * @param id 文件 ID
   */
  detail<T extends FileInfo = FileInfo>(category: string, id: string): Promise<T> {
    return this.http.get(`${this.api.fileDetail}/${category}`, { params: { id } }).then(responseData, responseReject);
  }

  /**
   * 移动文件至目录
   * @param category 类别
   * @param id 文件 ID
   * @param targetId 目标目录 ID
   */
  move(category: string, ids: string[], targetId: string): Promise<ResponseResult | null> {
    return this.http.post(this.api.fileMove + `/${category}`, { ids, targetId }).then(responseResult, responseReject);
  }
}

export const CatalogFileService = createSingleClass(CatalogFileServiceCtor);
