/**
 * 预加载
 */
export const preloader = (className = 'ht-preloader') => {
  const preloaderElement = document.querySelector(`.${className}`) as HTMLElement;
  const body = document.querySelector('body') as HTMLBodyElement;
  body.style.overflow = 'hidden';

  const remove = () => {
    if (!preloaderElement) {
      return;
    }

    preloaderElement.addEventListener('transitionend', function () {
      preloaderElement.className = `${className}-hidden`;
      if (preloaderElement.remove) {
        preloaderElement.remove();
      } else {
        const parentElement = preloaderElement.parentElement as HTMLElement;
        parentElement && parentElement.removeChild(preloaderElement);
      }
    });
    preloaderElement.classList.add(`${className}-hidden-add`);
  };

  // 预加载完成
  return (window.appBootstrap = () => {
    setTimeout(() => {
      remove();
      body.style.overflow = '';
    }, 100);
  });
};
