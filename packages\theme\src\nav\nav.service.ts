import { type Menu, MenuLinkType } from './model';
import { SettingsService } from '../settings/settings.service';
import type { RouteLocationNormalized } from 'vue-router';
import { reactive } from 'vue';

/**
 * 导航菜单服务类
 * <AUTHOR>
 */
export class NavService {
  static menus: Menu[] | undefined = undefined;

  static menuIdKey: string | ((current: RouteLocationNormalized) => string) = 'menuId';

  static activeMenus = reactive<string[]>([]);

  static openMenus = reactive<string[]>([]);

  // key is id
  private static ids: Record<string, Menu> = {};

  // key is menuLink
  private static links: Record<string, Menu> = {};

  // key is menuCode
  private static codes: Record<string, Menu> = {};

  private static _firstMenus = reactive<Menu[]>([]);

  private static _subMenus = reactive<Menu[]>([]);

  static get layout() {
    return SettingsService.layout;
  }

  static get topMenus() {
    if (this.layout.navMode === 'side') {
      return [];
    }
    if (this.layout.navMode === 'both' && this.layout.topLeftSider) {
      return this.subMenus;
    }
    return this.firstMenus;
  }

  static get sideMenus() {
    if (this.layout.navMode === 'side') {
      return this.firstMenus;
    }
    if (this.layout.navMode === 'both') {
      return this.layout.topLeftSider ? this.firstMenus : this.subMenus;
    }
    return [];
  }

  static get firstMenus() {
    return this._firstMenus;
  }

  static get subMenus() {
    return this._subMenus;
  }

  static get initialized() {
    return !!this.menus;
  }

  static clear() {
    this.menus = undefined;
    this.ids = {};
    this.links = {};
    this.codes = {};
    this._firstMenus.length = 0;
    this._subMenus.length = 0;
  }

  static getMenu(key: string) {
    return this.ids[key] || this.links[key] || this.codes[key];
  }

  static getMenuIdByKey(route: RouteLocationNormalized) {
    let menuId;
    if (this.menuIdKey) {
      if (typeof this.menuIdKey === 'string') {
        menuId = route.params[this.menuIdKey] as string;
      } else if (typeof this.menuIdKey === 'function') {
        menuId = this.menuIdKey(route);
      }
    }
    return menuId;
  }

  static getMenuFromRoute(route: RouteLocationNormalized) {
    const { path, matched } = route;
    let menu = this.links[path];
    if (!menu) {
      const match = matched
        .slice(0)
        .reverse()
        .find((m: { path: string | number }) => this.links[m.path]);
      match && (menu = this.links[match.path]);
    }

    if (!menu) {
      // maybe iframe...
      const menuId = this.getMenuIdByKey(route);
      if (menuId) {
        menu = this.getMenu(menuId);
      }
    }

    return menu;
  }

  static getChildren(id: string) {
    return this.ids[id].children;
  }

  static setMenus(value: Menu[]) {
    this.clear();
    this.menus = value;
    value.forEach((menu) => {
      const id = menu.id as string;
      if (this.ids[id]) {
        this.ids[id] = Object.assign(menu, this.ids[id]);
      } else {
        this.ids[id] = menu;
      }

      const code = menu.menuCode;
      if (this.codes[code]) {
        this.codes[code] = Object.assign(menu, this.codes[code]);
      } else {
        this.codes[code] = menu;
      }

      if (menu.parentId) {
        const parentMenu = this.ids[menu.parentId] || {};
        parentMenu.children = parentMenu.children || [];
        parentMenu.children.push(menu);
        this.ids[menu.parentId] = parentMenu;
      } else {
        this._firstMenus.push(menu);
      }

      if (menu.children && menu.children.length) {
        return;
      }

      if (menu.linkType === MenuLinkType.Url) {
        this.links[id] = this.ids[id];
      } else {
        if (menu.menuLink) {
          this.links[menu.menuLink] = this.ids[id];
        }
      }
    });
  }

  static setSubMenus(value?: Menu[]) {
    this._subMenus = value || [];
  }

  static setActiveMenu(current: RouteLocationNormalized) {
    let menu = this.getMenuFromRoute(current);
    this.activeMenus.length = 0;
    this.openMenus.length = 0;

    if (!menu) {
      return;
    }

    this.activeMenus.push(menu.id as string);

    while (menu.parentId) {
      this.openMenus.push(menu.parentId);
      menu = this.ids[menu.parentId];
    }

    const { navMode } = this.layout;
    if (navMode === 'top') {
      return;
    }

    if (navMode === 'both' && !menu.parentId) {
      this.activeMenus.push(menu.id as string);
      this.setSubMenus(menu.children);
    }
  }
}
