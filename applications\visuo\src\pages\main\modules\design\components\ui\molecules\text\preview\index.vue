<template>
  <div class="vis-preview">
    <div v-if="!ishover" class="vis-preview__content h-full flex justify-center items-center">预览</div>
    <div v-else class="w-full h-full" :style="{ ...alignVertical, ...textStyle }">
      <span class="w-full" :style="{ ...alignHorizontal, ...spanStyle }">太极河图数据可视化</span>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
