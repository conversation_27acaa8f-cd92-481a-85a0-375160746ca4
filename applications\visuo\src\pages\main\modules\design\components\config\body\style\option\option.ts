import { computed, defineComponent, ref } from 'vue';
import { useDocumentStore } from '@vis/document-core';
import { Block, WidgetBlock } from '@vis/document-core';
import { useDesignStore } from '../../../../../stores';
import VisConfigTabOption from './tab/tab.vue';
import VisConfigParagraphOption from './paragraph/paragraph.vue';
import { VisConfigInputOption } from './form';
/**
 * 组件配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-option',
  components: {
    VisConfigTabOption,
    VisConfigParagraphOption,
    VisConfigInputOption
  },
  setup() {
    const designStore = useDesignStore();
    const activeGraph = computed(() => designStore.active.value.graphs?.[0]);

    const docStore = useDocumentStore();

    // 获取当前选中的组件
    const activeBlock = computed(
      () => docStore.document.value.blocks.find((b) => b.id === (activeGraph.value as Block)?.decoration) as WidgetBlock
    );

    return {
      activeBlock
    };
  }
});
