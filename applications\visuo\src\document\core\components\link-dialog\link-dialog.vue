<template>
  <q-dialog
    :model-value="visible"
    @hide="onClose"
    :maximized="isFull"
    :position="options.position"
    :no-backdrop-dismiss="!options.clickHide"
    :class="{ 'hide-backdrop': !options.mask }"
    @show="handleShow"
  >
    <q-card
      class="dialog-content"
      :style="'width:' + options.width + '; height:' + options.height + '; max-width: none; max-height: none'"
    >
      <template v-if="options.isShowTitle">
        <q-card-section class="row items-center !py-2 dialog-title">
          <div v-if="options.title" class="text-dark font-medium text-lg">{{ options.title }}</div>
          <q-space />
          <q-btn icon="close" size="sm" rounded dense flat v-close-popup />
        </q-card-section>
        <q-separator />
      </template>
      <q-card-section
        class="no-padding no-margin"
        :style="options.isShowTitle ? 'height: calc(100% - 45px)' : 'height: 100%'"
      >
        <q-scroll-area class="fit">
          <iframe
            :src="options.link"
            :width="options.iframeWidth"
            :height="options.iframeHeight"
            class="no-border absolute top-0 left-0"
          ></iframe>
        </q-scroll-area>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script lang="ts" src="./link-dialog.ts"></script>
<style lang="scss" src="./link-dialog.scss"></style>
