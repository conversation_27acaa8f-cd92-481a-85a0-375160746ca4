import { ref } from 'vue';
import type { RootHttpApi } from '@hetu/http';
import { CacheService } from '@hetu/util';
import type { App, User, Config } from './model';
import { Layout } from './layout';

/**
 * 配置服务类
 * <AUTHOR>
 */
export class SettingsService {
  private static _config?: Config;

  private static _app: App = {
    year: new Date().getFullYear()
  };

  private static _layout = new Layout();

  private static _api: RootHttpApi;

  static isFetching = ref(false);

  static userCacheKey = 'user';

  /** 配置信息 */
  static get config() {
    return this._config;
  }

  /** 应用信息 */
  static get app() {
    return this._app;
  }

  /** 布局配置 */
  static get layout() {
    return this._layout;
  }

  /** API 配置 */
  static get api() {
    return this._api;
  }

  /** 当前登录用户信息 */
  static get user() {
    return CacheService.getNone<User>(this.userCacheKey);
  }

  static setConfig(value: Config) {
    this._config = value;
    value.app && this.setApp(value.app);
    value.layout && this.setLayout(value.layout);
    value.api && this.setApi(value.api);
  }

  static setApp(value: App) {
    Object.assign(this._app, value);
  }

  static setLayout(name: string | Layout, value?: any) {
    if (typeof name === 'string') {
      if (typeof value === 'object') {
        Object.assign(this.layout[name], value);
      } else {
        this.layout[name] = value;
      }
    } else {
      Object.keys(name).forEach((key) => this.setLayout(key, name[key]));
    }
  }

  static setApi(value: RootHttpApi) {
    this._api = value;
  }

  /**
   * 当前登录用户信息
   * @param user 用户信息
   */
  static setUser(user?: User) {
    if (user) {
      CacheService.set(this.userCacheKey, user);
    } else {
      CacheService.remove(this.userCacheKey);
    }
  }

  static setFetching(value: boolean) {
    this.isFetching.value = value;
  }
}
