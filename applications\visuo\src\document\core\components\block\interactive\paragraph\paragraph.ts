import { defineComponent, ref, computed, type PropType, onMounted, watch, nextTick } from 'vue';
import { Block, Paragraph, ParagraphOptions, WidgetBlock, TextAdapt } from '../../../../models';
import { useBase, useLink, useUiStyle, useWidget } from '../../../../hooks';
import { useDocumentStore } from '../../../../stores';

/**
 * 段落组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-paragraph',
  props: {
    widget: {
      type: Object as PropType<Paragraph>
    },
    block: {
      type: Object as PropType<Block>
    }
  },
  setup(props, ctx) {
    const docStore = useDocumentStore();

    const { init, getWidgetFieldData } = useBase();
    const { handleLink } = useLink();

    const widget = props.widget;

    const paragraph = computed(() => {
      return props.widget?.options as ParagraphOptions;
    });

    const loadWidgetData = async () => {
      return new Promise((resolve) => {
        getWidgetFieldData(
          new Promise((resolve) => {
            setTimeout(() => {
              console.log('ParagraphComponent: 重写加载段落数据', widget?.id);
              // 段落特有的数据加载逻辑
              resolve(true);
            }, 5000);
          })
        ).then((res) => {
          resolve(res);
        });
      });
    };

    const { getWidgetData, getWidgetStyle, getWidgetOptions } = useWidget();
    const { getTextStyle } = useUiStyle();

    onMounted(() => {
      //init();
    });

    //#region 获取数据

    const data = computed(() => getWidgetData(widget as WidgetBlock) || paragraph.value.content);

    //#endregion

    //#region 获取样式

    const style = ref<any>(getWidgetStyle(widget as WidgetBlock));

    // 字体样式
    const computedText = computed(() => {
      return props.block?.text;
    });

    //#endregion

    //#region 获取配置

    const options = ref<any>(getWidgetOptions(widget as WidgetBlock));

    //#endregion

    /**
     * textStyle加在text-content层，适配居中方式
     */
    const textStyle = computed(() => {
      return computedText.value ? getTextStyle(computedText.value) : {};
    });

    const isFixed = computed(() => {
      return computedText.value?.adapt === TextAdapt.Fixed;
    });

    /**
     * 计算超出样式
     */
    const adaptStyle = computed(() => {
      if (computedText.value?.adapt === TextAdapt.Ellipsis) {
        let lineNumber = 1;
        if (props.block?.height) {
          const { lineHeight = 0, fontSize = 0 } = computedText.value;
          const baseHeight = lineHeight || fontSize * 1.5;
          const [top, right, bottom, left] = props.block.stroke?.position || [0, 0, 0, 0];
          lineNumber = Math.floor((props.block.height - top - bottom) / baseHeight);

          lineNumber = Math.max(lineNumber, 1);
        }
        return {
          display: '-webkit-box',
          webkitBoxOrient: 'vertical',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          webkitLineClamp: `${lineNumber}`
        };
      } else if (computedText.value?.adapt === TextAdapt.Single) {
        return {
          whiteSpace: 'nowrap'
        };
      }
      return {};
    });

    // 打开链接
    const visible = ref(false);
    const isDesign = computed(() => docStore.mode.value === 'design');
    const isDialog = computed(() => !isDesign.value && paragraph.value.link.target === 'dialog');
    const link = computed(() => paragraph.value.link);

    const handleOpen = () => {
      if (isDesign.value || !link.value.enable) return;

      const { path, target } = handleLink(link.value);
      if (!path) return;

      if (target !== 'dialog') {
        window.open(path, target);
      } else {
        Object.assign(link.value, { path });
        visible.value = true;
      }
    };
    const onClose = () => {
      visible.value = false;
    };

    // 只有设计器内才会调整内容和适应方式
    if (isDesign.value) {
      const computedBlock = computed(() => {
        return props.block;
      });
      // 计算段落宽高（字号、行高、字距、缩进等均有可能引起高度变化）
      watch(
        [() => data.value, () => computedText.value],
        () => {
          nextTick(() => {
            if (!computedBlock.value) return;
            const ele = document.querySelector(`#${computedBlock.value.id} span`) as HTMLElement;
            if (!ele) return;

            switch (computedText.value?.adapt) {
              case TextAdapt.Single:
              case TextAdapt.Auto: {
                const { width, height } = ele.getBoundingClientRect();
                const [top, right, bottom, left] = computedBlock.value?.stroke?.position || [0, 0, 0, 0];

                computedBlock.value.width = width + left + right;
                computedBlock.value.height = height + top + bottom;
                break;
              }
              default:
                break;
            }
          });
        },
        { deep: true }
      );

      /**
       * 当段落宽高发生变化时，按照中心点位重新计算偏移
       */
      watch([() => computedBlock.value?.width, () => computedBlock.value?.height], (newVal, oldVal) => {
        if (!computedBlock.value?.transform.translate) return;
        if (!Array.isArray(newVal) || !Array.isArray(oldVal)) return;
        const [newWidth, newHeight] = newVal;
        const [oldWidth, oldHeight] = oldVal;
        if (!newWidth || !newHeight || !oldWidth || !oldHeight || (newWidth === oldWidth && newHeight === oldHeight))
          return;

        let [x, y] = computedBlock.value.transform.translate;
        x -= (newWidth - oldWidth) / 2;
        y -= (newHeight - oldHeight) / 2;
        computedBlock.value.transform.translate = [parseInt(`${x}`), parseInt(`${y}`)];
      });
    }

    return {
      data,
      style,
      options,
      paragraph,
      computedText,
      textStyle,
      isFixed,
      adaptStyle,
      loadWidgetData,

      visible,
      isDialog,
      link,
      handleOpen,
      onClose
    };
  }
});
