import { defineComponent, ref, type PropType, computed } from 'vue';
import { AttachmentService } from '@hetu/platform-shared';
import { type TreeDataType } from '@hetu/util';
import { MaterialGeo } from '../../../models';
import { MaterialGeoService } from '../../../services';
import { type QForm, debounce, useQuasar } from 'quasar';

/**
 * 地理坐标系表单
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-material-geo-info',
  props: {
    id: {
      type: String,
      required: true
    },
    tree: {
      type: Object as PropType<TreeDataType<MaterialGeo>>,
      required: true
    }
  },
  setup(props, { emit }) {
    const $q = useQuasar();
    const acl = MaterialGeoService.acl;

    const formRef = ref<QForm>();
    // 表单数据
    const formData = ref(new MaterialGeo());
    let geoData = new MaterialGeo();

    const loading = ref(false);
    const loadData = () => {
      if (props.id === 'new') return;

      const { cancel } = debounce(() => (loading.value = true), 100);
      MaterialGeoService.info(props.id)
        .then((data) => {
          if (!data) return;
          if (data.parentId === 'GEO_JSON_ROOT') {
            data.parentId = '';
          }
          formData.value = data;
          geoData = { ...data };
        })
        .finally(() => {
          cancel();
          loading.value = false;
        });
    };
    loadData();

    //#region 表单验证
    /**
     * 名称验证
     * @param value 值
     */
    const nameValidator = async (value: string) => {
      const id = formData.value.id || '';
      return await MaterialGeoService.nameCheck(id, value).then((res) => {
        if (res && res.status === 'error') {
          return res.message;
        }

        return true;
      });
    };

    /**
     * 编码验证
     * @param value 值
     */
    const codeValidator = async (value: string) => {
      const id = formData.value.id || '';
      return await MaterialGeoService.codeCheck(id, value).then((res) => {
        if (res && res.status === 'error') {
          return res.message;
        }

        return true;
      });
    };

    /** 表单验证 */
    const rules = {
      name: [
        (val: string) => !!val || '请输入区域名称',
        (val: string) => val.length <= 30 || '最大输入长度30',
        (val: string) => nameValidator(val)
      ],
      cityCode: [
        (val: string) => !!val || '请输入区域编码',
        (val: string) => val.length <= 50 || '最大输入长度50',
        (val: string) => /^\S.*\S$|(^\S{0,1}\S$)/.test(val) || '区域编码开头和结尾不能有空格',
        (val: string) => !/[\u4E00-\u9FA5]/.test(val) || '区域编码不能有中文',
        (val: string) => codeValidator(val)
      ],
      attachmentId: [(val: File) => !!val || !!formData.value.attachmentId || '请上传文件']
    };
    //#endregion 表单验证

    //#region 文件上传
    // 选中的 json 文件
    const fileData = ref(null);
    const fileName = computed(() => (formData.value.fileName ? formData.value.fileName + '.json' : ''));

    /** 上传 */
    const upload = (file: File) => {
      if (!file) return;

      const data = new FormData();
      data.append('file', file);
      data.append('moduleCode', 'geo');
      AttachmentService.uploadFile(data).then((res) => {
        if (res && res.status === 'success') {
          formData.value.attachmentId = res.data.id;
          formData.value.fileName = res.data.fileName;
          $q.notify({ message: '上传成功', type: 'positive', position: 'top' });
        }
      });
    };

    /** 删除文件 */
    const removeFile = () => {
      formData.value.attachmentId = '';
      formData.value.fileName = '';
    };
    //#endregion 文件上传

    //#region 上级区域
    const parentName = computed(() =>
      formData.value.parentId ? props.tree.getNodeByKey(formData.value.parentId).name : ''
    );
    const updateParent = (target = '') => {
      formData.value.parentId = target;
    };
    const filterParent = (node: MaterialGeo, filter: string) => {
      return node.id !== filter && !props.tree.getParentKeys(node.id).includes(filter);
    };
    //#endregion 上级区域

    const saveing = ref<boolean>(false);
    /** 保存 */
    const save = () => {
      formRef.value?.validate().then((valid) => {
        if (!valid) return false;

        saveing.value = true;
        MaterialGeoService.save(formData.value)
          .then((res) => {
            if (res && res.status === 'success') {
              $q.notify({ message: res.message, type: 'positive', position: 'top' });
              emit('onBack', true);
            }
          })
          .finally(() => (saveing.value = false));
      });
    };

    /** 重置 */
    const reset = () => {
      formData.value = { ...geoData };

      formRef.value?.resetValidation();
    };

    return {
      acl,

      formRef,
      formData,
      loading,

      rules,

      fileData,
      fileName,
      upload,
      removeFile,

      parentName,
      updateParent,
      filterParent,

      saveing,
      save,
      reset
    };
  }
});
