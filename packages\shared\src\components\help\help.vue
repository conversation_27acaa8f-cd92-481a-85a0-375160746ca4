<template>
  <q-btn class="ht-help !bg-font-primary !min-w-10 !min-h-10" text-color="white" round unelevated @click="help">
    <ht-icon name="help" class="q-icon" />
  </q-btn>
</template>

<script setup lang="ts">
import { SettingsService } from '@hetu/theme';

defineOptions({ name: 'ht-help' });

const help = () => {
  const links = SettingsService.app.links;
  if (links && links.help && links.help.link) window.open(links.help.link);
};
</script>
