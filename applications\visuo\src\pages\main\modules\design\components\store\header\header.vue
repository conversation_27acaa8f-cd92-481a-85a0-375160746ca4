<template>
  <div class="vis-store-header">
    <q-btn :ripple="false" class="vis-store-header-title">
      <span>{{ title }}</span>
      <ht-icon class="vis-icon" name="hticon-vis-arrow-d" />
      <q-menu class="vis-store-header-menu vis-menu w-100">
        <q-scroll-area style="height: 200px; max-height: 500px">
          <q-tree
            class="vis-store-header-tree"
            :nodes="simple"
            dense
            node-key="label"
            default-expand-all
            no-connectors
            v-model:expanded="expanded"
          >
            <template v-slot:default-header="{ node }">
              <q-icon :name="node.icon || 'o_description'" size="14px" class="text-gray-800" />
              <div class="vis-store-header-tree-label">{{ node.label }}</div>
            </template>
          </q-tree>
        </q-scroll-area>
      </q-menu>
    </q-btn>
    <q-separator class="vis-store-header__separator" />
  </div>
</template>

<script lang="ts" src="./header.ts"></script>
<style lang="scss" scoped src="./header.scss"></style>
