<template>
  <div class="ht-app-file-info">
    <div class="ht-app-file-info__header text-font-regular">文件信息</div>
    <div
      class="ht-app-file-info__icon column flex-center"
      :class="[
        fileInfo
          ? fileInfo.dataType
            ? 'text-folder'
            : iconColor
            ? iconColor.startsWith('text-')
              ? iconColor
              : 'text-' + iconColor
            : 'text-font-placeholder'
          : 'text-grey-lighter',
        fileInfo && 'bg-grey-light'
      ]"
    >
      <slot name="icon">
        <ht-icon v-if="fileIcon?.startsWith('hticon-')" :name="fileIcon?.replace('hticon-', '')" />
        <q-icon v-else :name="fileIcon" />
      </slot>
      <span v-show="!fileInfo" class="text-caption">请选择文件或文件夹</span>
    </div>

    <template v-if="fileInfo">
      <div class="text-font-primary break-all">{{ fileInfo.title }}</div>
      <q-list class="q-mt-md text-font-regular">
        <slot></slot>

        <q-item v-if="fileInfo.favorite">
          <q-item-section>动态消息:</q-item-section>
          <q-item-section side>
            <q-item-label :class="isCanSetupMsg && 'cursor-pointer'">
              <template v-if="isEmptyMsg">
                <template v-if="isCanSetupMsg">请选择 <q-icon size="18px" name="arrow_drop_down"></q-icon></template>
                <template v-else>无</template>
              </template>
              <template v-else-if="favoriteMsg">
                <q-chip
                  v-if="favoriteMsg.smsShow && favoriteMsg.sendSms"
                  size="sm"
                  outline
                  square
                  color="positive"
                  label="短信"
                />
                <q-chip
                  v-if="favoriteMsg.mailShow && favoriteMsg.sendMail"
                  size="sm"
                  outline
                  square
                  color="positive"
                  label="邮件"
                />
                <q-chip
                  v-if="favoriteMsg.noticeShow && favoriteMsg.sendNotice"
                  size="sm"
                  outline
                  square
                  color="warning"
                  label="站内信"
                />
              </template>
              <q-menu v-if="isCanSetupMsg">
                <q-list class="q-py-sm" dense>
                  <q-item
                    v-if="favoriteMsg?.smsShow"
                    :class="[favoriteMsg.sendSms && 'text-primary']"
                    clickable
                    @click="handleMsgSend('sendSms')"
                  >
                    <q-item-section v-show="favoriteMsg.sendSms" class="col-auto">
                      <q-icon class="inline-block" name="checked" />
                    </q-item-section>
                    <q-item-section>短信</q-item-section>
                  </q-item>
                  <q-item
                    v-if="favoriteMsg?.mailShow"
                    :class="[favoriteMsg.sendMail && 'text-primary']"
                    clickable
                    @click="handleMsgSend('sendMail')"
                  >
                    <q-item-section v-show="favoriteMsg.sendMail" class="col-auto">
                      <q-icon class="inline-block" name="checked" />
                    </q-item-section>
                    <q-item-section>邮件</q-item-section>
                  </q-item>
                  <q-item
                    v-if="favoriteMsg?.noticeShow"
                    :class="[favoriteMsg.sendNotice && 'text-primary']"
                    clickable
                    @click="handleMsgSend('sendNotice')"
                  >
                    <q-item-section v-show="favoriteMsg.sendNotice" class="col-auto">
                      <q-icon class="inline-block" name="checked" />
                    </q-item-section>
                    <q-item-section>站内信</q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </q-item-label>
          </q-item-section>
        </q-item>

        <q-item v-if="parent">
          <q-item-section>所在位置:</q-item-section>
          <q-item-section side>
            <label class="ht-link is-primary" @click="openParent">{{ parent.title }}</label>
          </q-item-section>
        </q-item>

        <q-item class="q-mt-sm q-pa-none">
          <q-item-section>类型:</q-item-section>
          <q-item-section side>
            {{ fileInfo.dataType ? '文件夹' : typeName || '文件' }}
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>创建人:</q-item-section>
          <q-item-section side>
            <q-skeleton v-if="userNames[0] === null" type="text" />
            <template v-else>{{ userNames[0] || '未知' }}</template>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>创建时间:</q-item-section>
          <q-item-section side>{{ fileInfo.createdTime }}</q-item-section>
        </q-item>

        <q-item>
          <q-item-section>最近修改人:</q-item-section>
          <q-item-section side>
            <q-skeleton v-if="userNames[1] === null" type="text" />
            <template v-else>{{ userNames[1] || '未知' }}</template>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>最近修改时间:</q-item-section>
          <q-item-section side>{{ fileInfo.updateTime }}</q-item-section>
        </q-item>
      </q-list>
    </template>

    <slot name="other"></slot>
  </div>
</template>

<style lang="scss" src="./file-info.scss"></style>
<script lang="ts" src="./file-info"></script>
