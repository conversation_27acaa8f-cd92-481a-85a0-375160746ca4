<template>
  <div v-if="isFetching" class="ht-router-progressbar" />
  <router-view v-if="isTransition" v-slot="{ Component }">
    <transition name="ht-transition--fade-transform-y" mode="out-in">
      <component :is="Component" />
    </transition>
  </router-view>
  <router-view v-else />
</template>

<script setup lang="ts">
import { SettingsService } from '@hetu/theme';

defineOptions({ name: 'ht-app-root' });

defineProps({ isTransition: { type: Boolean, default: true } });

const { isFetching } = SettingsService;
</script>
