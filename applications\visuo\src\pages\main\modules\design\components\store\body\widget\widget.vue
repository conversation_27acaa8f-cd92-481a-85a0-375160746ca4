<template>
  <div class="vis-store-widget">
    <div class="vis-store-widget-header">
      <div class="vis-store-widget-header-title">
        <div class="vis-store-widget-header-title-text">控件</div>
        <q-btn icon="close" flat class="vis-store-widget-header-title-btn" />
      </div>
      <div class="vis-store-widget-header-search">
        <q-input outlined v-model="keyWord" dense placeholder="搜索" class="vis-store-widget-header-input">
          <template #prepend>
            <q-icon name="search" flat size="16px" />
          </template>
          <template #append>
            <q-btn icon="close" flat size="8px" @click="keyWord = ''" v-if="keyWord" />
          </template>
        </q-input>
      </div>
    </div>
    <q-scroll-area class="h-[calc(100vh-180px)]">
      <draggable
        class="vis-store-body-widget"
        :list="widgetConfigs"
        :group="{ name: 'widget-component', pull: 'clone', put: false }"
        :sort="false"
        item-key="name"
        :clone="dragClone"
        @start="toggleDropbox(true)"
        @end="toggleDropbox(false)"
        @move="onMove"
      >
        <template #item="{ element }">
          <div class="item">
            {{ element.title }}
          </div>
        </template>
      </draggable>
    </q-scroll-area>
  </div>
</template>
<script lang="ts" src="./widget.ts"></script>
<style lang="scss" scoped src="./widget.scss"></style>
