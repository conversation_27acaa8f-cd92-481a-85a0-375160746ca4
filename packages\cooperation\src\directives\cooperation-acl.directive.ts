import { type Directive } from '@hetu/core';
import { CooperationACLService } from '../services';
import { type DirectiveBinding } from 'vue';
import type { CooperationACLType } from '../models';

const map = new Map();
const check = (el: HTMLElement, binding: DirectiveBinding) => {
  const acl = binding.value as CooperationACLType;
  const group = binding.arg as string;
  let auth = CooperationACLService.allow({ ...acl, group });

  if (!auth && acl && acl.regularResource) {
    auth = CooperationACLService.allow({
      ...acl,
      ...{ resource: acl.regularResource, group: '' }
    });
  }

  if (!auth) {
    const comment = document.createComment('v-ca');
    Object.defineProperty(comment, 'setAttribute', {
      value: () => undefined
    });

    if (el.parentNode) {
      map.set(el, comment);
      el.parentNode.replaceChild(comment, el);
    }
  } else {
    const comment = map.get(el);
    if (comment) {
      comment.parentNode?.replaceChild(el, comment);
      map.delete(el);
    }
  }
};

/**
 * 协作权限访问控制指令
 * <AUTHOR>
 */
export const CooperationACLDirective: Directive = {
  // cooperation auth
  name: 'ca',
  mounted: check,
  updated: check,
  beforeUnmount(el: HTMLElement) {
    map.has(el) && map.delete(el);
  }
};
