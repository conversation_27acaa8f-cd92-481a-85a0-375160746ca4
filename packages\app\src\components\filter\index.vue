<template>
  <q-dialog v-model="visible" persistent class="ht-filter">
    <q-card style="min-width: 650px; height: 580px; min-height: 580px">
      <q-toolbar class="row items-center">
        <div class="text-dark font-normal">筛选器: {{ fieldAlias ? fieldAlias : field.fieldAlias }}</div>
        <q-space />
        <q-btn icon="close" size="sm" rounded dense flat v-close-popup @click="onClose" />
      </q-toolbar>
      <q-separator />
      <q-tabs
        v-model="tab"
        class="bg-white"
        :style="{ width: `${filterExpressions.length * 100}px` }"
        indicator-color="primary"
        dense
      >
        <q-tab
          :name="filterType"
          :label="FilterTypeName[filterType]"
          v-for="filterType in filterTypes"
          :key="filterType"
        />
      </q-tabs>
      <q-separator />
      <template v-for="(filterExpression, index) in filterExpressions" :key="index">
        <component
          :is="`ht-filter-${filterExpression.filterType}`"
          v-if="tab === filterExpression.filterType"
          :filter="filterExpression"
          :fieldValues="fieldValues"
          :fieldName="fieldName"
          :dataType="field.dataType"
          @changeLimit="changeLimit"
          style="height: calc(100% - 195px)"
        ></component>
      </template>

      <div class="py-3 px-5">
        <div class="q-field__label text-body2 q-mb-sm text-dark inline">逻辑条件：</div>
        <q-btn-toggle
          v-model="filterRule"
          size="xs"
          no-caps
          unelevated
          toggle-color="primary"
          color="white"
          text-color="primary"
          :options="[
            { label: '并且', value: 'and' },
            { label: '或者', value: 'or' }
          ]"
          class="border border-solid border-primary"
        />
        <q-checkbox v-model="includeNull" label="包含Null值" :true-value="1" :false-value="0" size="xs" class="ml-4" />
      </div>
      <q-separator />
      <q-card-section class="text-right q-py-sm">
        <q-btn label="取消" type="reset" flat @click="onClose" />
        <q-btn unelevated label="确定" type="submit" color="primary" class="q-ml-sm" @click="onConfirm" />
      </q-card-section>
    </q-card>
  </q-dialog>
</template>
<script lang="ts" src="./index"></script>
<style lang="scss" src="./index.scss"></style>
