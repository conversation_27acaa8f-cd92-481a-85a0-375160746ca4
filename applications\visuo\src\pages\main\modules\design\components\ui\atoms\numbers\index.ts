import { defineComponent, ref, watch } from 'vue';
/**
 * 多值输入框
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-numbers',
  props: {
    modelValue: {
      type: Array<number>,
      required: true
    }
  },
  setup(props, { emit }) {
    const isMulti = ref(false);
    const value = ref<number>();
    const values = ref<number[]>(new Array(4).fill(undefined));

    const initData = () => {
      // 全部值相同时不是多选
      if (new Set(props.modelValue).size <= 1) {
        isMulti.value = false;
      } else {
        isMulti.value = true;
      }
      value.value = props.modelValue[0];
      values.value = props.modelValue;
    };
    initData();

    const onChange = () => {
      isMulti.value = !isMulti.value;

      if (!isMulti.value) {
        value.value = values.value[0];
      }
      values.value = new Array(4).fill(value.value);
    };

    const updateValue = () => {
      values.value = new Array(4).fill(value.value);
    };

    watch(
      () => values.value,
      () => {
        emit('update:modelValue', values.value);
        emit('change', values.value);
      },
      { deep: true }
    );

    return {
      isMulti,
      value,
      values,
      onChange,
      updateValue
    };
  }
});
