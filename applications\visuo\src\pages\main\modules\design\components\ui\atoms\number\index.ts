import { throttle } from 'quasar';
import { computed, defineComponent, onMounted, ref, watch, nextTick } from 'vue';

/**
 * 数字输入框
 * 支持min、max设置范围
 * 支持diasble设置禁用状态
 * 在图标上拖动可改变数值
 * 支持后缀符号跟随数字位置
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-number',
  props: {
    modelValue: {
      type: Number,
      required: true
    },
    icon: {
      type: String
    },
    // 后缀符号（如度数符号°）
    suffix: {
      type: String
    },
    // 数值框的最小值和最大值
    min: {
      type: Number
    },
    max: {
      type: Number
    },
    // 步长
    step: {
      type: Number,
      default: 1
    },
    // 节流控制
    throttle: {
      type: Number,
      default: 0
    },
    tooltip: {
      type: String
    },
    disabled: {
      type: Boolean
    },
    readonly: {
      type: Boolean
    }
  },
  // 指令阻止原生change事件
  directives: {
    'stop-change': {
      mounted(el) {
        const input = el.querySelector('input');
        input.addEventListener(
          'change',
          (e: Event) => {
            e.stopImmediatePropagation();
            e.preventDefault();
          },
          true
        );
      }
    }
  },
  setup(props, { emit }) {
    // 调整灵敏度（值越小，拖动变化越慢）
    const sensitivity = 0.5;

    const numberInput = ref();
    const suffixElement = ref();
    const numberValue = ref(props.modelValue);
    const suffixStyle = ref({ left: '0px' });

    watch(
      () => props.modelValue,
      (val) => {
        numberValue.value = val;
        nextTick(() => {
          updateSuffixPosition();
        });
      }
    );

    // 计算文本宽度的辅助函数
    const getTextWidth = (text: string, font: string): number => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      if (context) {
        context.font = font;
        return context.measureText(text).width;
      }
      return 0;
    };

    // 更新后缀符号位置
    const updateSuffixPosition = () => {
      if (!props.suffix || !numberInput.value) return;

      nextTick(() => {
        const inputElement = numberInput.value.$el.querySelector('input');
        if (!inputElement) return;

        const inputValue = String(numberValue.value);
        if (!inputValue) {
          // 如果没有输入值，将符号放在输入框开始位置
          const computedStyle = window.getComputedStyle(inputElement);
          const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;
          const prependWidth = props.icon ? 24 : 0;
          suffixStyle.value = { left: `${paddingLeft + prependWidth}px` };
          return;
        }

        const computedStyle = window.getComputedStyle(inputElement);
        const font = `${computedStyle.fontWeight} ${computedStyle.fontSize} ${computedStyle.fontFamily}`;

        // 计算文本宽度
        const textWidth = getTextWidth(inputValue, font);

        // 获取输入框的padding-left
        const paddingLeft = parseInt(computedStyle.paddingLeft) || 0;

        // 计算前置图标的宽度
        const prependElement = numberInput.value.$el.querySelector('.q-field__prepend');
        const prependWidth = prependElement ? prependElement.offsetWidth : 0;

        // 设置后缀位置（紧贴数字后面）
        const leftPosition = paddingLeft + prependWidth + textWidth + 8;
        suffixStyle.value = { left: `${leftPosition}px` };
      });
    };

    const handleUpdate = (val: string | number) => {
      emit('update:modelValue', Number(val));
      // 触发change事件
      if (`${val}` === `${props.modelValue}`) return;
      emit('change', Number(val), Number(props.modelValue));

      // 更新后缀位置
      nextTick(() => {
        updateSuffixPosition();
      });
    };

    const isIconFont = computed(() => {
      return props.icon?.startsWith('vis') || props.icon?.startsWith('hticon-vis');
    });

    // #region 拖动图标改变数值
    onMounted(() => {
      // 初始化后缀位置
      updateSuffixPosition();

      // 添加输入事件监听器来实时更新符号位置
      if (props.suffix) {
        const inputElement = numberInput.value.$el.querySelector('input');
        if (inputElement) {
          inputElement.addEventListener('input', updateSuffixPosition);
          inputElement.addEventListener('keyup', updateSuffixPosition);
          inputElement.addEventListener('paste', () => {
            setTimeout(updateSuffixPosition, 0);
          });
        }
      }

      // 查找到当前输入框的图标
      const dragHandle = numberInput.value.$el.querySelector('.drag-icon');

      const isDragging = ref(false);
      const startX = ref(0);
      const startValue = ref(0);

      // 鼠标按下时开始监听拖动
      dragHandle?.addEventListener('mousedown', (e: any) => {
        if (props.readonly) return;

        isDragging.value = true;
        startX.value = e.clientX;
        startValue.value = Number(numberValue.value) || 0;

        document.addEventListener('mousemove', handleDrag);
        document.addEventListener('mouseup', stopDrag);

        // 防止选中文本
        e.preventDefault();
      });

      // 鼠标移动时计算拖动的距离并更新数值
      const handleDrag = throttle((e: MouseEvent) => {
        if (!isDragging.value) return;

        // 保持鼠标样式
        document.body.classList.add('dragging');

        const deltaX = e.clientX - startX.value;

        const newValue = Number.isInteger(props.step)
          ? Math.ceil(startValue.value + deltaX * sensitivity)
          : parseFloat((startValue.value + deltaX * sensitivity * props.step).toFixed(2));

        // 确保数值在 min 和 max 范围内
        const min = props.min !== undefined ? props.min : -Infinity;
        const max = props.max !== undefined ? props.max : Infinity;
        numberValue.value = Math.min(max, Math.max(min, newValue));

        handleUpdate(numberValue.value);
      }, props.throttle);

      // 鼠标松开时停止监听
      const stopDrag = () => {
        document.body.classList.remove('dragging');

        isDragging.value = false;
        document.removeEventListener('mousemove', handleDrag);
        document.removeEventListener('mouseup', stopDrag);
      };
    });

    // #endregion

    const oldValue = ref(numberValue.value);
    const handleFocus = () => {
      oldValue.value = numberValue.value;
      numberInput.value && numberInput.value.select();
    };

    const handleBlur = () => {
      if (numberValue.value === undefined) {
        numberValue.value = Number(oldValue.value);
      } else if (props.min !== undefined && numberValue.value < props.min) {
        numberValue.value = Number(props.min);
      } else if (props.max !== undefined && numberValue.value > props.max) {
        numberValue.value = Number(props.max);
      }
      oldValue.value = numberValue.value;

      handleUpdate(numberValue.value);
    };

    return {
      numberInput,
      suffixElement,
      numberValue,
      suffixStyle,
      handleUpdate,
      updateSuffixPosition,
      isIconFont,

      handleFocus,
      handleBlur
    };
  }
});
