import { defineComponent } from 'vue';
import VisStorePageContainer from './page/page-container.vue';
import VisStoreWidget from './widget/widget.vue';
import { useDesignStore } from '../../../stores';

/**
 * 组件库面板主体
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-body',
  components: {
    VisStorePageContainer,
    VisStoreWidget
  },
  setup() {
    const { selectedMenu } = useDesignStore();

    return {
      selectedMenu
    };
  }
});
