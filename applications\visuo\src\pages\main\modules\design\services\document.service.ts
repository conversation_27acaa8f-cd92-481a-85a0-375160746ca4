import { createSingleClass } from '@hetu/core';
import { HttpApiService, responseData, responseReject, responseResult } from '@hetu/http';
import { CacheService } from '@hetu/util';
import { DocumentApi, Document, useDocumentStore } from '@vis/document-core';
import isString from 'lodash-es/isString';

export class DocumentServiceCtor<T extends DocumentApi = DocumentApi> extends HttpApiService<T> {
  httpApi = new DocumentApi() as T;

  httpModuleKey = 'document';

  private documentStore = useDocumentStore();

  info(id: string): Promise<Document | null> {
    return new Promise((resolve, reject) => {
      const doc = CacheService.getNone(`doc_${id}`) as Document;
      console.log(doc);
      resolve(doc);
      //   {
      //   id: 'doc1',
      //   name: '设计文件',
      //   order: 0,
      //   pages: [
      //     {
      //       id: 'page1',
      //       name: '页面1',
      //       order: 0,
      //       guide: {
      //         show: true,
      //         h: [],
      //         v: []
      //       },
      //       docId: 'doc1',
      //       widgets: [
      //         {
      //           id: 'container1',
      //           type: 'container',
      //           x: 0,
      //           y: 0,
      //           w: 500,
      //           h: 400,
      //           backgroundColor: '#8a848457',
      //           parentId: ''
      //         }
      //         // {
      //         //   id: 'container1',
      //         //   type: 'container',
      //         //   x: 0,
      //         //   y: 0,
      //         //   w: 500,
      //         //   h: 400,
      //         //   backgroundColor: '#8a8484',
      //         //   parentId: '',
      //         //   children: [
      //         //     {
      //         //       id: 'container4',
      //         //       type: 'container',
      //         //       x: 100,
      //         //       y: 100,
      //         //       w: 200,
      //         //       h: 200,
      //         //       backgroundColor: '#abc9c6',
      //         //       parentId: 'container1',
      //         //       children: [
      //         //         {
      //         //           id: 'text1',
      //         //           type: 'text',
      //         //           x: 20,
      //         //           y: 20,
      //         //           w: 50,
      //         //           h: 50,
      //         //           backgroundColor: 'red',
      //         //           parentId: 'container4',
      //         //           children: []
      //         //         }
      //         //       ]
      //         //     }
      //         //   ]
      //         // },
      //         // {
      //         //   id: 'container2',
      //         //   type: 'container',
      //         //   x: 550,
      //         //   y: 50,
      //         //   w: 300,
      //         //   h: 200,
      //         //   backgroundColor: '#a17676',
      //         //   children: [],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container3',
      //         //   type: 'container',
      //         //   x: 550,
      //         //   y: 300,
      //         //   w: 100,
      //         //   h: 100,
      //         //   backgroundColor: '#95a176',
      //         //   children: [],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-left',
      //         //   type: 'container',
      //         //   x: 50,
      //         //   y: 500,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text2',
      //         //       type: 'text',
      //         //       x: 20,
      //         //       y: 20,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-left',
      //         //       children: []
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-right',
      //         //   type: 'container',
      //         //   x: 300,
      //         //   y: 500,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text3',
      //         //       type: 'text',
      //         //       x: 20,
      //         //       y: 20,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-right',
      //         //       children: [],
      //         //       style: {
      //         //         right: '20px',
      //         //         top: '20px',
      //         //         transform: ''
      //         //       }
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-center',
      //         //   type: 'container',
      //         //   x: 550,
      //         //   y: 500,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text4',
      //         //       type: 'text',
      //         //       x: 75,
      //         //       y: 75,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-center',
      //         //       children: [],
      //         //       style: {
      //         //         left: 'calc(50% - 50px/2)',
      //         //         top: '75px',
      //         //         transform: ''
      //         //       }
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-space',
      //         //   type: 'container',
      //         //   x: 800,
      //         //   y: 500,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text5',
      //         //       type: 'text',
      //         //       x: 75,
      //         //       y: 75,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-space',
      //         //       children: [],
      //         //       style: {
      //         //         left: '75px',
      //         //         top: '75px',
      //         //         right: '75px',
      //         //         width: 'auto',
      //         //         transform: ''
      //         //       }
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-scale',
      //         //   type: 'container',
      //         //   x: 1050,
      //         //   y: 500,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text5',
      //         //       type: 'text',
      //         //       x: 75,
      //         //       y: 75,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-scale',
      //         //       children: [],
      //         //       style: {
      //         //         left: '37.5%',
      //         //         top: '75px',
      //         //         right: '37.5%',
      //         //         width: 'auto',
      //         //         transform: ''
      //         //       }
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-auto1',
      //         //   type: 'container',
      //         //   x: 50,
      //         //   y: 800,
      //         //   w: 200,
      //         //   h: 200,
      //         //   backgroundColor: '#ffffff',
      //         //   style: {
      //         //     display: 'flex',
      //         //     gap: '20px'
      //         //   },
      //         //   children: [
      //         //     {
      //         //       id: 'text6',
      //         //       type: 'text',
      //         //       x: 20,
      //         //       y: 20,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-auto1',
      //         //       children: [],
      //         //       style: {
      //         //         position: '',
      //         //         transform: '',
      //         //         width: 'auto',
      //         //         minWidth: '20px',
      //         //         flexGrow: 1
      //         //       }
      //         //     },
      //         //     {
      //         //       id: 'text7',
      //         //       type: 'text',
      //         //       x: 90,
      //         //       y: 20,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'yellow',
      //         //       parentId: 'container-auto1',
      //         //       children: [],
      //         //       style: {
      //         //         position: '',
      //         //         transform: ''
      //         //       }
      //         //     },
      //         //     {
      //         //       id: 'text8',
      //         //       type: 'text',
      //         //       x: 160,
      //         //       y: 20,
      //         //       w: 50,
      //         //       h: 50,
      //         //       backgroundColor: 'blue',
      //         //       parentId: 'container-auto1',
      //         //       children: [],
      //         //       style: {
      //         //         position: '',
      //         //         transform: ''
      //         //       }
      //         //     }
      //         //   ],
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-grid',
      //         //   type: 'container',
      //         //   x: 50,
      //         //   y: 1050,
      //         //   w: 500,
      //         //   h: 500,
      //         //   backgroundColor: '#ffffff',
      //         //   children: [
      //         //     {
      //         //       id: 'text9',
      //         //       type: 'text',
      //         //       x: 20,
      //         //       y: 20,
      //         //       w: 100,
      //         //       h: 100,
      //         //       backgroundColor: 'red',
      //         //       parentId: 'container-grid',
      //         //       children: [],
      //         //       style: {
      //         //         'grid-column': 2,
      //         //         'grid-row': 2,
      //         //         transform: '',
      //         //         position: '',
      //         //         width: 'auto',
      //         //         height: 'auto'
      //         //       }
      //         //     }
      //         //   ],
      //         //   option: {
      //         //     gridLayout: [
      //         //       {
      //         //         type: 'col',
      //         //         gap: 20,
      //         //         count: 3,
      //         //         background: 'rgba(255, 61, 0, 0.12)'
      //         //       },
      //         //       {
      //         //         type: 'row',
      //         //         gap: 10,
      //         //         count: 4,
      //         //         background: 'rgba(255, 61, 0, 0.12)'
      //         //       }
      //         //     ]
      //         //   },
      //         //   parentId: ''
      //         // },
      //         // {
      //         //   id: 'container-grid-all',
      //         //   type: 'container',
      //         //   x: 600,
      //         //   y: 1050,
      //         //   w: 220,
      //         //   h: 220,
      //         //   backgroundColor: '',
      //         //   style: {
      //         //     background:
      //         //       'linear-gradient(-90deg, rgb(193, 10, 10) 1px, transparent 1px) 0% 0% / 20px 20px, linear-gradient(0deg, rgb(214, 8, 8) 1px, transparent 1px) 0% 0% / 20px 20px rgb(126 83 83)'
      //         //   },
      //         //   children: [],
      //         //   parentId: ''
      //         // }
      //       ]
      //     }
      //   ]
      // } as any as Document
    });
  }

  /**
   * 加载font.config配置
   */
  async loadFontsConfig() {
    const baseUrl = document.baseURI;
    const fonts = await this.http.get(`${baseUrl}static/fonts/fonts.json`).then(responseResult);
    this.documentStore.fontFamilys.value = fonts?.fontFamilys || [];
    return fonts?.fontFamilys || [];
  }

  async setFontsStyle() {
    const fontFamilys = await this.loadFontsConfig();
    // 判断是否已添加过字体样式
    const fontStyle = document.getElementById('fonts');
    if (fontStyle) return;

    const fontFacesCSS = fontFamilys
      .filter((item: { name: string; url?: string } | string) => !isString(item))
      .map((font: { name: string; url?: string }) => {
        // 如果 font.url 存在且不为空，则使用它，否则使用本地路径
        const srcUrl = font.url ? `url('${font.url}')` : `url('./static/fonts/${font.name}.ttf')`;
        return `
            @font-face {
              font-family: '${font.name}';
              src: ${srcUrl};
            }
          `;
      })
      .join('');

    const styleElement = document.createElement('style');
    styleElement.type = 'text/css';
    styleElement.id = 'fonts';

    styleElement.appendChild(document.createTextNode(fontFacesCSS));

    document.head.appendChild(styleElement);
  }
}

export const DocumentService = createSingleClass(DocumentServiceCtor);
