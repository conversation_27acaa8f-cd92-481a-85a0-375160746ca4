{"name": "@hetu/stylelint-config", "description": "hetu stylelint config", "version": "5.0.0", "main": "./index.js", "devDependencies": {"postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "stylelint": "^15.4.0", "stylelint-config-property-sort-order-smacss": "^9.1.0", "stylelint-config-recommended": "^11.0.0", "stylelint-config-recommended-scss": "^9.0.1", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^32.0.0", "stylelint-config-standard-scss": "^7.0.1", "stylelint-order": "^6.0.3", "stylelint-prettier": "^3.0.0"}}