import { BaseModel, type HttpApi } from '@hetu/http';
import { SYSTEM_API_PREFIX } from '.';

/** 团队模块 KEY */
export const TEAM_MODULE_KEY = 'team';

/** 缓存团队信息的 KEY */
export const TEAM_CACHE_KEY = TEAM_MODULE_KEY;

/**
 * 团队API
 */
export class TeamApi implements HttpApi {
  details = `${SYSTEM_API_PREFIX}/team/details`;

  list = `${SYSTEM_API_PREFIX}/team/auth/list`;

  /** 域名判重 */
  codeCheck = `${SYSTEM_API_PREFIX}/team/code/check`;

  /** 创建团队 */
  create = `${SYSTEM_API_PREFIX}/team/create`;

  /** 保存 */
  save = `${SYSTEM_API_PREFIX}/team/save`;

  /** 退出 */
  leave = `${SYSTEM_API_PREFIX}/team/exit`;

  /** 查询团队下的成员列表(手机号、邮箱脱敏) */
  members = `${SYSTEM_API_PREFIX}/team/member`;

  /** 查询团队下的用户组列表 */
  groups = `${SYSTEM_API_PREFIX}/team/group/list`;
}

/**
 * 团队
 */
export class Team extends BaseModel {
  /** 名称 */
  name = '';

  /** 域名 */
  domain = '';

  /** logo 附件 Id */
  avatar = '';

  /** 主题 */
  theme = 'theme1';

  /** 联系人 */
  contactPerson = '';

  /** 联系电话 */
  contactNumber = '';

  /** 简介 */
  summary = '';

  /** 行业编码 */
  industryDictCode = '';

  /** 规模 */
  scale = '';

  /**
   * 是否修改过域名
   * - 0: 未修改
   * - 1: 修改过
   */
  changedDomain!: 0 | 1;

  /**
   * 是否为团队所有者
   * - 0: 否
   * - 1: 是
   */
  owner!: 0 | 1;

  /**
   * 租户类型
   * - 0: 个人
   * - 1: 团队
   */
  teamType!: 0 | 1;
}

export type Teams = Team[];
