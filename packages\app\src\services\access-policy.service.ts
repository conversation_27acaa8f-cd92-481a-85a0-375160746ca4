import { HttpApiService, type HttpApi, responseReject, type ResponseResult, responseResult } from '@hetu/http';
import { API_PREFIX } from '@hetu/platform-shared';
import { type WhiteIP } from '../models';
import { createSingleClass } from '@hetu/core';

const ACCESS_PREFIX = `${API_PREFIX}/system/ip/policy/`;

const saveApi = (category: string) => {
  return `${ACCESS_PREFIX}${category}/save`;
};

const listApi = (category: string) => {
  return `${ACCESS_PREFIX}${category}/list`;
};

/**
 * 访问策略API
 * <AUTHOR>
 */
class AccessPolicyApi implements HttpApi {
  /**  访问策略保存-看板 */
  dashboardSave = saveApi('dashboard');

  /**  根据业务主键查询策略-看板 */
  dashboardList = listApi('dashboard');

  /**  访问策略保存-驾驶舱 */
  cockpitSave = saveApi('cockpit');

  /**  根据业务主键查询策略-驾驶舱 */
  cockpitList = listApi('cockpit');

  /**  访问策略保存-ppt */
  pptSave = saveApi('ppt');

  /**  根据业务主键查询策略-ppt */
  pptList = listApi('ppt');
}

/**
 * 访问策略服务类
 */
export class AccessPolicyServiceCtor extends HttpApiService<AccessPolicyApi> {
  httpApi = new AccessPolicyApi();
  httpModuleKey = 'access-policy';

  /**
   * 访问策略保存
   * @param accessPolicy
   */
  save(accessPolicy: WhiteIP[], category: string): Promise<ResponseResult | null> {
    let url = '';
    switch (category) {
      case 'dashboard':
        url = this.api.dashboardSave;
        break;
      case 'cockpit':
        url = this.api.cockpitSave;
        break;
      case 'ppt':
        url = this.api.pptSave;
        break;
    }
    return this.http.post(url, accessPolicy).then(responseResult, responseReject);
  }
  /**
   * 访问策略列表
   * @param accessPolicy
   */
  list(dataId: string, category: string): Promise<ResponseResult | null> {
    let url = '';
    switch (category) {
      case 'dashboard':
        url = this.api.dashboardList;
        break;
      case 'cockpit':
        url = this.api.cockpitList;
        break;
      case 'ppt':
        url = this.api.pptList;
        break;
    }
    return this.http.get(url, { params: { dataId } }).then(responseResult, responseReject);
  }
}

export const AccessPolicyService = createSingleClass(AccessPolicyServiceCtor);
