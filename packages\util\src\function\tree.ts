import type { Records, Condition, Nilable } from '../type';

export const TREE_NODE_ID_KEY = 'id';
export const TREE_NODE_LABEL_KEY = 'title';
export const TREE_NODE_PARENT_KEY = 'parentId';
export const TREE_NODE_CHILDREN_KEY = 'children';

/**
 * 树查找
 * @param tree - tree
 * @param condition - 判断语句
 * @param childrenKey - 子节点 key
 */
export function treeFind<T extends Records>(
  tree: T[],
  condition: Condition<T>,
  childrenKey: keyof T = TREE_NODE_CHILDREN_KEY
): Nilable<T> {
  for (let i = 0, len = tree.length; i < len; ++i) {
    const item = tree[i];
    const found = condition(item);
    if (found) {
      return item;
    }
    if (item[childrenKey]) {
      const child = treeFind(item[childrenKey], condition, childrenKey);
      if (child) {
        return child;
      }
    }
  }
}

/**
 * 树排序
 * @param tree - tree
 * @param compareFn - 排序函数, 例如: `(a, b) => (a[sortKey] || 0) - (b[sortKey] || 0)`
 * @param childrenKey - 子节点 key
 */
export function treeSort<T extends Records>(
  tree: T[],
  compareFn: (a: T, b: T) => number,
  childrenKey: keyof T = TREE_NODE_CHILDREN_KEY
): T[] {
  tree = tree.slice().sort(compareFn);
  for (let i = 0, len = tree.length; i < len; ++i) {
    const item = tree[i];
    if (Array.isArray(item[childrenKey])) {
      (item[childrenKey] as T[]) = treeSort(item[childrenKey], compareFn, childrenKey);
    }
  }
  return tree as T[];
}

export type TreeDataType<T = Records> = T[] & {
  addNode(node: T): void;
  removeNode(node: T): void;
  getNodeByKey(key: string): T;
  getParentKeys(key: string): string[];
};

export type TreeOptions<T> = {
  /** 保留有父级ID但无对应父级数据的记录, 默认值 `false` */
  keepUnParent: boolean;
  /** 保留下级数组, 默认值 `false` */
  keepChildren: boolean;
  /** id key, 默认值 `id` */
  idKey: keyof T;
  /** 指向父级 id 的 key, 默认值 `parentId` */
  parentKey: keyof T;
  /** 子元素 key, 默认值 `children` */
  childrenKey: keyof T;
};

/**
 * 根据 `collection` 生成 Tree
 * @param collection - collection
 * @param options: 参数
 */
export function createTree<T extends Records>(collection: T[], options?: Partial<TreeOptions<T>>): TreeDataType<T> {
  const { keepUnParent, keepChildren, idKey, parentKey, childrenKey } = {
    keepUnParent: false,
    keepChildren: false,
    idKey: TREE_NODE_ID_KEY,
    parentKey: TREE_NODE_PARENT_KEY,
    childrenKey: TREE_NODE_CHILDREN_KEY,
    ...(options ?? {})
  };

  let tree: TreeDataType<T> = [] as unknown as TreeDataType<T>;
  const map = new Map<string, T>();
  const unReadyParent = new Map<string, number[]>();

  const defineChildren = (item: T, children: T[] = []) => {
    if (!keepChildren && !Object.getOwnPropertyDescriptor(item, childrenKey)) {
      Object.defineProperty(item, childrenKey, {
        get: () => children
      });
    } else if (!item[childrenKey]) {
      item[childrenKey] = children as T[keyof T];
    }

    return item;
  };

  collection.forEach((item) => {
    const id = item[idKey];
    const parentId = item[parentKey];

    let parent = map.get(id);
    if (parent) {
      // 由下级添加的不完整父级数据
      map.set(id, defineChildren(item, parent[childrenKey]));

      if (keepUnParent && unReadyParent.has(id)) {
        const indexs = unReadyParent.get(id) as number[];
        indexs.forEach((i) => {
          tree[i] = undefined as unknown as T;
        });
        unReadyParent.delete(id);
      }
    } else {
      map.set(id, item);
    }

    if (parentId) {
      if (keepUnParent) {
        const unReady = unReadyParent.has(parentId) || !map.get(parentId);
        if (unReady) {
          const index = tree.push(item);
          const indexs = unReadyParent.get(parentId) || [];
          indexs.push(index - 1);
          unReadyParent.set(parentId, indexs);
        }
      }

      parent = map.get(parentId) || ({} as T);
      defineChildren(parent);

      parent[childrenKey].push(item);
      map.set(parentId, parent);
    } else {
      tree.push(item);
    }
  });

  if (keepUnParent) {
    tree = tree.filter((item) => !!item) as TreeDataType<T>;
  }

  tree.addNode = (item: T) => {
    const id = item[idKey];
    const parentId = item[parentKey];
    const parent = map.get(parentId);
    if (parentId && parent) {
      defineChildren(parent);
      parent[childrenKey].push(item);
    } else {
      tree.push(item);
    }

    map.set(id, item);
  };

  tree.removeNode = (item: T) => {
    const id = item[idKey];
    const parent = map.get(item[parentKey]);
    const arr = (parent ? parent[childrenKey] : tree) as T[];

    const index = arr.findIndex((n: T) => n[idKey] === id);
    index !== -1 && arr.splice(index, 1);
    map.delete(id);
  };

  tree.getNodeByKey = (key: string) => map.get(key) as T;

  tree.getParentKeys = (key: string) => findParentIds<T>(tree, key, parentKey);

  return tree;
}

/**
 * 获取上级节点ID集合
 * @param tree (`TreeDataType | QTree`) 类型的值
 * @param id - id
 * @param parentKey - 指向父级 id 的 key, 默认值 `parentId`
 */
export function findParentIds<T extends Records>(
  tree: { getNodeByKey: (key: string) => T },
  id: string,
  parentKey: keyof T = TREE_NODE_PARENT_KEY
): string[] {
  const ids: string[] = [];
  let node = tree.getNodeByKey(id);
  while (node && node[parentKey]) {
    ids.push(node[parentKey]);
    node = tree.getNodeByKey(node[parentKey]);
  }
  return ids;
}

/**
 * 除去 `tree` 中不满足 `condition` 条件的节点 (以及其子节点)
 *
 * Pure function, not change origin data
 *
 * @param tree - tree 数据
 * @param condition - 条件
 * @param childrenKey - children key
 */
export function trimTree<T extends Records>(
  tree: T[],
  condition: Condition<T>,
  childrenKey: keyof T = TREE_NODE_CHILDREN_KEY
): T[] {
  tree = tree.slice();
  const result = [];
  for (let i = 0, len = tree.length; i < len; ++i) {
    const item = Object.assign({}, tree[i]);
    if (condition(item)) {
      result.push(item);
      if (item[childrenKey]) {
        item[childrenKey] = trimTree(item[childrenKey].slice(), condition, childrenKey) as any;
      }
    }
  }
  return result;
}
