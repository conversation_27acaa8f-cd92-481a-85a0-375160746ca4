<template>
  <ht-app-file-container :group="group" :dense="dense">
    <ht-app-file
      v-for="folder in folders"
      :key="folder.id"
      icon="hticon-folder-fill"
      icon-color="text-folder"
      :icon-size="iconSize"
      :file="folder"
      :category="category"
      :acl="folderAcl(folder)"
      :multi="multi"
      :deleteType="deleteType"
      :favorite="favorite && !checkReadonly(folder) && folder.id !== 'DEMO'"
      :showToolBar="!checkReadonly(folder) && folder.id !== 'DEMO'"
      :dense="dense"
      @clickBody.stop="clickBody(folder)"
      @rename="updateTitle"
    >
      <template #tools="{ fileAcl }">
        <slot name="tools" :folder="folder" :folderAcl="fileAcl"></slot>
      </template>
      <slot />
    </ht-app-file>

    <!-- 当根节点新建文件夹无权限时报错, 在 `新建文件夹` 之前添加一个 dom 节点可避免,
      原因: 在 `transition-group` 下直接使用 `v-ca` 有异常, 异常原因未知 -->
    <span class="hidden" />
    <div
      v-if="!checkReadonly(openFolder) && openFolder.id !== 'DEMO'"
      key="create-folder"
      v-ca:[openFolder.privilegeId]="openFolderAcl.createDirectory"
      class="ht-app-file no-transition"
      :class="{ 'ht-app-file--dense': dense }"
      @click.stop="create"
    >
      <div class="ht-app-file__body" :class="$q.dark.isActive ? 'text-font-placeholder' : 'text-grey-lighter'">
        <ht-icon class="ht-app-file__icon" name="folder-add-fill"></ht-icon>
      </div>
      <div class="ht-app-file__title text-center q-mb-sm">
        <q-input
          v-if="isCreate"
          v-model="title"
          :maxlength="50"
          input-class="text-center"
          ref="titleInputRef"
          @blur="save"
          @keyup.enter="save"
        />
        <template v-else>新建文件夹</template>
      </div>
    </div>
  </ht-app-file-container>
</template>
<script lang="ts" setup>
import { computed, ref, inject, type PropType } from 'vue';
import type { ACLResourceInstance } from '@hetu/acl';
import { type QInput, useQuasar } from 'quasar';
import { Catalog, DirectoryACLResourceCode, type FileInfo } from '../../models';
import { CatalogService } from '../../services';
import { type FileTreeNode } from '../../utils';
import { FILE_PAGE_STORE_KEY, useFilePageStore } from '../../stores';
import type { ResponseResult } from '@hetu/http';

defineOptions({ name: 'ht-app-folder' });

type ACLType = ACLResourceInstance<DirectoryACLResourceCode>;

const props = defineProps<{
  /** 文件夹列表 */
  folders: FileTreeNode<FileInfo & { privilegeId?: string }>[];
  category: string;
  iconSize?: string;
  multi?: boolean;
  acl?: PropType<((file?: FileInfo) => ACLType) | ACLType>;
  group?: string | number;
  /** 是否支持星标 */
  favorite?: boolean;
  readonly?: PropType<((file: FileInfo) => boolean) | boolean>;
  deleteType?: string;
  dense?: boolean;
}>();

const emit = defineEmits(['opened']);

const $q = useQuasar();
const store = inject(FILE_PAGE_STORE_KEY) as ReturnType<typeof useFilePageStore<FileInfo>>;
const { open, setOpen } = store;

const folderAcl = (folder?: FileInfo) =>
  (typeof props.acl === 'function' ? (props.acl as (file?: FileInfo) => ACLType)(folder) : props.acl || {}) as ACLType;
const openFolder = computed(() => (open.value || {}) as FileInfo & { privilegeId?: string });
const openFolderAcl = computed(() => folderAcl(open.value));

//#region ------------------------ 基础操作 ------------------------
const isCreate = ref(false);
const title = ref('');
const titleInputRef = ref<QInput | null>(null);

const checkReadonly = (file: FileInfo) => {
  if (typeof props.readonly === 'function') {
    return (props.readonly as (file: FileInfo) => boolean)(file);
  }

  return props.readonly as unknown as boolean;
};

/** 打开文件夹 */
const clickBody = (folder: FileTreeNode<FileInfo>) => {
  setOpen(folder);
  emit('opened', folder);
};

/** 新建 */
const create = () => {
  isCreate.value = true;
  title.value = '新建文件夹';
  setTimeout(() => titleInputRef.value?.select(), 1);
};

/** 保存 */
const save = () => {
  if (!isCreate.value) {
    return;
  }
  if (!title.value) {
    $q.notify({
      position: 'top',
      type: 'warning',
      message: '取消新建文件夹'
    });
    isCreate.value = false;
    return;
  }

  const parentId = open.value ? open.value.id : '';
  const catalog = new Catalog(title.value, parentId, props.category);
  CatalogService.save(catalog).then((res) => {
    if (res && res.status === 'success') {
      catalog.id = res.data?.id;
      $q.notify({
        position: 'top',
        type: 'positive',
        message: '保存成功'
      });
      CatalogService.detail(props.category, catalog.id).then((data) => {
        store.tree?.addNode(data);
        isCreate.value = false;

        store.setOpen(store.tree?.getNode(parentId));
        store.setAction('search');
      });
    }
  });
};

/**
 * 修改标题
 * @param fileInfo
 */
const updateTitle = (title: string, folder: FileInfo, done: (result: ResponseResult<any> | null) => void) => {
  CatalogService.update(folder.id, title).then(done);
};
//#endregion
</script>
