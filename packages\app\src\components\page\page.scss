$ht-page-prefix-cls: '#{$ht-prefix}-app-page';

.#{$ht-page-prefix-cls} {
  height: 100%;

  &__sider {
    @apply border-r-1 border-r-solid border-grey-extra-light;

    width: 180px;
    padding: 40px 8px;

    .text-h5 {
      height: 40px;
      line-height: 40px;
    }
  }

  &__menu {
    @apply flex items-center px-2.5 h-9 rounded cursor-pointer duration-300 
        text-font-secondary hover:text-font-primary  hover:bg-grey-light;

    &.is-active {
      @apply text-primary bg-primary-lighter;
    }

    i {
      font-size: 1rem;
    }

    & + & {
      @apply mt-1;
    }
  }

  &__main {
    margin-right: 8px;
    padding: 40px 24px 40px 32px;

    .q-scrollarea__content {
      @apply flex flex-col;
    }

    &-header {
      @apply text-font-primary max-w-100%;

      height: 40px;
      line-height: 40px;

      .q-field {
        @apply w-56 xl:w-85;
      }
    }

    &-body {
      min-height: 100%;
      overflow-x: hidden;
    }
  }

  &--dense .#{$ht-page-prefix-cls} {
    &__sider {
      padding: 24px 8px;
    }

    &__main {
      margin-right: 4px;
      padding: 24px 12px 24px 16px;
    }
  }
}

.body--dark .#{$ht-page-prefix-cls} {
  &__main-header {
    color: inherit;
  }
}
