import { type CookieOptions, CookieService } from '@hetu/util';
import type { TokenStore, TokenModel } from '.';

/**
 * `cookie` storage
 */
export class CookieStorageStore implements TokenStore {
  get(key: string): TokenModel {
    return JSON.parse(CookieService.get(key) || '{}') || {};
  }

  set(key: string, value: TokenModel | null, cookieOptions?: CookieOptions | (() => CookieOptions)): boolean {
    CookieService.put(key, JSON.stringify(value), this.getCookieOptions(cookieOptions));
    return true;
  }

  remove(key: string, cookieOptions?: CookieOptions | (() => CookieOptions)): void {
    CookieService.remove(key, this.getCookieOptions(cookieOptions));
  }

  private getCookieOptions(options?: CookieOptions | (() => CookieOptions)) {
    return typeof options === 'function' ? options() : options;
  }
}
