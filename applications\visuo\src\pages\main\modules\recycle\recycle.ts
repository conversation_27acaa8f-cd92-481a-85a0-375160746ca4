// import { defineComponent, ref, computed, watch, nextTick, onMounted } from 'vue';
// // import { GroupManager } from '@moveable/helper';

// import { deepFlat } from '../design/components/content/draw/helper/types';
// import { VueSelecto } from 'vue3-selecto';
// import Moveable from 'vue3-moveable';

// export default defineComponent({
//   name: 'vis-test',
//   components: {
//     Moveable,
//     VueSelecto
//   },
//   props: {},
//   setup(props) {
//     const groupManager = new GroupManager([]);
//     const targets = ref<Array<HTMLElement>>([]);
//     const moveableRef = ref<any>();
//     const selectoRef = ref<any>();
//     const cubes = [];
//     for (let i = 0; i < 30; ++i) {
//       cubes.push(i);
//     }
//     const setSelectedTargets = (nextTargetes: any) => {
//       selectoRef.value?.setSelectedTargets(deepFlat(nextTargetes));
//       targets.value = nextTargetes;
//     };
//     const onClick = () => {
//       targets.value = document.querySelectorAll('.cube') as unknown as HTMLElement[];
//       //   const nextGroup = groupManager.group(targets.value, true);
//       //   if (nextGroup) {
//       //     targets.value = nextGroup;
//       //   }
//     };
//     const onClick$0 = () => {
//       //   const nextGroup = groupManager.ungroup(targets.value);
//       //   if (nextGroup) {
//       //     targets.value = nextGroup;
//       //   }
//     };
//     const onDrag = (e: any) => {
//       e.target.style.transform = e.transform;
//     };
//     const onRenderGroup = (e: any) => {
//       e.events.forEach((ev: any) => {
//         ev.target.style.cssText += ev.cssText;
//       });
//     };
//     const onDragStart = (e: any) => {
//       const moveable = moveableRef.value as Moveable;
//       const target = e.inputEvent.target;
//       const flatted = deepFlat(targets.value);
//       if (
//         target.tagName === 'BUTTON' ||
//         moveable.isMoveableElement(target) ||
//         flatted.some((t: any) => t === target || t.contains(target))
//       ) {
//         e.stop();
//       }
//     };
//     const onSelectEnd = (e: any) => {
//       const { isDragStart, added, removed, inputEvent } = e;
//       const moveable = moveableRef.value as Moveable;
//       if (isDragStart) {
//         inputEvent.preventDefault();
//         moveable.waitToChangeTarget().then(() => {
//           moveable.dragStart(inputEvent);
//         });
//       }
//       let nextChilds;
//       if (isDragStart) {
//         nextChilds = groupManager.selectCompletedChilds(targets.value, added, removed);
//       } else {
//         nextChilds = groupManager.selectSameDepthChilds(targets.value, added, removed);
//       }
//       e.currentTarget.setSelectedTargets(nextChilds.flatten());
//       setSelectedTargets(nextChilds.targets());
//     };
//     onMounted(() => {
//       const elements = selectoRef.value?.getSelectableElements();

//       groupManager.set([], elements);
//     });
//     return {
//       onClick,
//       onClick$0,
//       moveableRef,
//       targets,
//       onDrag,
//       onRenderGroup,
//       selectoRef,
//       window,
//       onDragStart,
//       onSelectEnd,
//       cubes
//     };
//   }
// });
