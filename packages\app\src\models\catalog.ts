import { BaseModel } from '@hetu/http';

/**
 * 目录
 */
export class Catalog extends BaseModel {
  id = '';

  /**
   * 目录名称
   */
  catalogName = '';

  /**
   * 父级ID
   */
  parentId = '';

  /**
   * 目录类别
   */
  category = '';

  dataType = 1;

  constructor(catalogName?: string, parentId?: string, category?: string) {
    super();
    catalogName && (this.catalogName = catalogName);
    parentId && (this.parentId = parentId);
    category && (this.category = category);
  }
}
