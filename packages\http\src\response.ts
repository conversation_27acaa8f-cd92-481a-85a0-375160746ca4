import { type AxiosResponse } from 'axios';

export type HttpResponse<T = any> = AxiosResponse<T>;

/**
 * 请求响应结果(业务)
 * <AUTHOR>
 */
export interface ResponseResult<T = any> {
  /** 响应状态 */
  status: 'success' | 'error';

  /** 返回数据值 */
  data: T;

  /** 响应消息 */
  message: string;

  /** 响应编码 */
  code: string | number;

  [key: string]: any;
}

/**
 * 取 `AxiosResponse` 的 `data` 值 ( `response.data` )
 * @param response - HTTP 请求响应结果
 */
export const responseResult = <T = any>(response: HttpResponse<ResponseResult<T> | null>) => response.data;

/**
 * 取 `ResponseResult` 的业务 `data` 值 ( `response.data?.data` )
 * @param response - HTTP 请求响应结果
 */
export const responseData = <T = any>(response: HttpResponse<ResponseResult<T> | null>) => {
  const result = responseResult(response);
  return result ? result.data : null;
};

/**
 * 被拒绝
 */
export const responseReject = () => null;
