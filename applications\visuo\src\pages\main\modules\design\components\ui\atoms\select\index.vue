<template>
  <q-select
    ref="selectRef"
    class="vis-select vis-field--mini rounded-borders overflow-hidden whitespace-nowrap px-2"
    :class="{ 'vis-select--bg': editable }"
    v-model="value"
    @update:modelValue="handleUpdate"
    :options="propOptions"
    popup-content-class="vis-select-popup hide-scrollbar"
    :popup-content-style="computedStyles"
    borderless
    dense
    options-dense
    emit-value
    map-options
    :use-input="editable"
    :hide-selected="editable"
    :fill-input="editable"
    input-style="width:100%"
    dropdown-icon="keyboard_arrow_down"
    @click.stop="handleClick"
    @keydown.up.stop="handleClick"
    @keydown.down.stop="handleClick"
    @keydown.enter.stop="handleClick"
    @popup-show="isMenuOpen = true"
    @popup-hide="isMenuOpen = false"
    @new-value="handleNewValue"
    :disable="disabled"
    :readonly="readonly"
  >
    <template v-if="$slots.before" #before><slot name="before"></slot></template>
    <template v-if="$slots.prepend" #prepend><slot name="prepend"></slot></template>
    <template v-if="$slots.append" #append><slot name="append"></slot></template>
    <template v-if="$slots.after" #after><slot name="after"></slot></template>
    <template v-if="$slots.default" #default><slot name="default"></slot></template>
    <template v-if="$slots.selected" #selected><slot name="selected"></slot></template>
    <template v-if="$slots.option" #option="scope"><slot name="option" v-bind="scope"></slot></template>
    <template v-if="$slots['no-option']" #no-option><slot name="no-option"></slot></template>
  </q-select>
</template>
<script lang="ts" src="./index.ts"></script>
