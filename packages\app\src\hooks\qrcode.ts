import { ref } from 'vue';
import { fileDownload } from '@hetu/platform-shared';
import { useQuasar } from 'quasar';
import { LazyService } from '@hetu/util';

export function useQRCode() {
  const $q = useQuasar();
  const qrcodeCanvasRef = ref<HTMLCanvasElement>();

  const getPlugin = (): Promise<QRCode | undefined> => {
    let qrcode: QRCode = (window as any).QRCode;
    if (qrcode) return Promise.resolve(qrcode);

    return LazyService.loadScript('./static/libs/qrcode/qrcode.js').then((result) => {
      if (!result || result.status === 'error') {
        $q.notify({ message: '二维码加载失败，请重试', position: 'top', type: 'negative' });
        return;
      }
      qrcode = (window as any).QRCode;
      return qrcode;
    });
  };

  let logoImg: HTMLImageElement;
  const logoSize = 32;
  const getLogoImg = () => {
    if (logoImg) return Promise.resolve(logoImg);

    return new Promise<HTMLImageElement>((resolve) => {
      const image = new Image(logoSize, logoSize);
      image.src = './static/img/logo.png';
      image.onload = () => {
        logoImg = image;
        resolve(logoImg);
      };
    });
  };

  /** 渲染二维码 */
  const renderQRCode = async (url: string, size = 160) => {
    const context = qrcodeCanvasRef.value?.getContext('2d');

    if (!context) return;

    const slotSize = (size - logoSize - 4) / 2;

    if (url) {
      // 渲染二维码
      const qrcode = await getPlugin();
      if (!qrcode) return;

      qrcode.toCanvas(qrcodeCanvasRef.value!, url, { width: size });
      // 挖空中间凹槽区域, 且凹槽区和 Logo 间隔 2px
      context.clearRect(slotSize, slotSize, logoSize + 4, logoSize + 4);
      const logo = await getLogoImg();
      logo && context.drawImage(logo, slotSize + 2, slotSize + 2, logoSize, logoSize);
    } else {
      context.clearRect(0, 0, size, size);
    }
  };

  /** 保存二维码图片 */
  const downloadQRCode = (name = '二维码') => {
    qrcodeCanvasRef.value?.toBlob((blob: any) => fileDownload(blob!, 'image/png', name), 'image/png', 1);
  };

  return { qrcodeCanvasRef, renderQRCode, downloadQRCode };
}
