import { WidgetConfig, useLayout, useWidgetStore } from '@vis/document-core';
import { useDesignStore } from '../../../../stores';
import { computed, defineComponent, ref } from 'vue';
import draggable from 'vuedraggable';
import { useGraph } from '@vis/page-main/modules/design/hooks';
import { throttle } from 'quasar';

/**
 * 组件库面板主体
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-widget',
  components: {
    draggable
  },
  setup() {
    const designStore = useDesignStore();
    const widgetStore = useWidgetStore();

    const canvasState = computed(() => designStore.canvasState.value);

    const widgetConfigs = widgetStore.configs;

    const { getFrameByPosition, flattenPageFrames, activeGraphs, handGridItemPosition, getRowColByPosition } =
      useGraph();

    const { isGrid } = useLayout();

    /**
     * 切换显示添加组件的容器
     */
    const toggleDropbox = (isStart: boolean) => {
      designStore.canvasState.value.dropbox = !designStore.canvasState.value.dropbox;
      if (isStart) {
        // 开始拖拽时计算所有的容器
        flattenPageFrames();
        activeGraphs();
      } else {
        designStore.canvasState.value.frame = undefined;
      }
    };

    const dragClone = (config: WidgetConfig) => {
      return config;
    };

    let lastXY = [0, 0];

    /**
     * 添加组件时计算放置的容器
     * @param e
     */
    const onMove = throttle((e: { originalEvent: DragEvent }) => {
      const { offsetX, offsetY } = e.originalEvent;
      if (offsetX && offsetY && (offsetX !== lastXY[0] || offsetY !== lastXY[1])) {
        // 计算缩放偏移后的位置
        const zoom = designStore.rulerState.value.zoom;
        const infiniteCanvasRef = designStore.infiniteCanvasRef;
        const translateX = Math.round(infiniteCanvasRef.value?.getScrollLeft() || 0) * zoom;
        const translateY = Math.round(infiniteCanvasRef.value?.getScrollTop() || 0) * zoom;

        const x = Math.round((offsetX + translateX) / zoom);
        const y = Math.round((offsetY + translateY) / zoom);

        // 计算添加到哪个容器内
        const frame = getFrameByPosition(x, y);
        designStore.canvasState.value.frame = frame;

        if (frame) {
          if (isGrid(frame)) {
            handGridItemPosition(frame);
            // 计算鼠标在frame中的哪个格子里
            canvasState.value.gridRowCol = getRowColByPosition(frame, x, y);
          }
        }
      }
      lastXY = [offsetX, offsetY];
    }, 300);

    const keyWord = ref('');

    const search = () => {
      console.log(keyWord.value);
    };

    return {
      widgetConfigs,
      toggleDropbox,
      dragClone,
      keyWord,

      onMove
    };
  }
});
