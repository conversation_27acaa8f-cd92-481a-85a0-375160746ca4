import { HttpTokenInterceptor } from '@hetu/auth';
import { HttpClient, type HttpRequestInterceptorType } from '@hetu/http';
import { HttpAppCodeInterceptor } from './app-code.interceptor';
import { HttpDomainInterceptor } from './domain.interceptor';
import { AppHttpRequestInterceptor } from './default.interceptor';

export * from './app-code.interceptor';
export * from './default.interceptor';
export * from './domain.interceptor';
export * from './auth-config';

/** `http` 默认请求拦截器配置: 包含 `token` */
export const HTTP_REQUEST_INTERCEPTORS = [AppHttpRequestInterceptor, HttpTokenInterceptor, HttpAppCodeInterceptor];

/** `http` 默认请求拦截器配置: 包含 `token` & `domain`  */
export const HTTP_ALL_REQUEST_INTERCEPTORS = [...HTTP_REQUEST_INTERCEPTORS, HttpDomainInterceptor];

/**
 * 设置请求拦截器
 * @param interceptors 拦截器
 */
export function setupRequestInterceptors(
  ...interceptors: Array<HttpRequestInterceptorType | HttpRequestInterceptorType[]>
) {
  // 添加请求拦截器
  interceptors.flat().forEach((interceptor) => HttpClient.addRequestInterceptor(interceptor));
}
