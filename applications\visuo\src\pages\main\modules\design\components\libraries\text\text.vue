<template>
  <q-editor
    ref="editorRef"
    v-model="editor"
    :toolbar="[]"
    :min-height="'12px'"
    :readonly="!isEditable"
    @update:model-value="handleEditor"
    @dblclick="onSelectText"
    @focus="onFocus"
    @blur="onBlur"
    class="vis-text-editor no-border q-editor__hidebar h-full"
    :content-class="['no-padding', 'max-h-full', 'hide-scrollbar']"
    :style="textStyle"
    :content-style="adaptStyle"
    toolbar-bg="white"
    square
    dense
  >
  </q-editor>
  <div class="caret" v-if="isCaret"><span></span></div>
</template>

<script lang="ts" src="./text.ts"></script>
<style lang="scss" src="./text.scss"></style>
