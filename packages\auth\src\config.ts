import type { CookieOptions } from '@hetu/util';
import { TokenStoreType, type TokenStore } from './store';

/**
 * 配置
 */
export interface AuthConfig {
  /** 存储类型/对象 */
  store?: TokenStoreType | TokenStore;
  /** 存储 Key 值, 默认为 `token` */
  storeKey?: string;

  /** 发送 token 参数名, 默认 `access-token` */
  sendKey?: string;

  /**
   * 发送 token 模板, 默认为 `${token}`
   *
   * 使用 `${token}` 表示 token 占位符, 例如:
   *
   * - `Bearer ${token}`
   */
  sendTemplate?: string;

  /** 发送 token 参数位置, 默认为 `header` */
  sendPlace?: 'header' | 'body' | 'url';

  /** 登录页路由地址, 默认为 `/login` */
  loginUrl?: string;

  /** 不进行 token 检查的 url 的正则列表, 默认为 `[/\/login/, /assets\//, /static\//, /passport\//];` */
  ignores?: RegExp[];

  /** 允许匿名登录 key, 若请求参数中带有此 key, 则忽略 token 检查, 默认值为 `tdp-allow-anonymous` */
  allowAnonymousKey?: string;

  /** `cookie` 参数配置, `cookie` storage 模式有效 */
  cookieOptions?: CookieOptions | (() => CookieOptions);
}

/** 默认配置 */
const authConfig: AuthConfig = {
  store: TokenStoreType.Local,
  storeKey: 'token',
  sendKey: 'access-token',
  sendTemplate: '${token}',
  sendPlace: 'header',
  loginUrl: '/login',
  ignores: [/\/login/, /assets\//, /static\//, /passport\//],
  allowAnonymousKey: 'ht-allow-anonymous'
};

export const setupAuthConfig = (config: AuthConfig) => Object.assign(authConfig, config);

export const useAuthConfig = () => authConfig;
