<template>
  <div class="vis-input flex h-full items-center" :class="[{ disabled: computedOptions.disabled }, { row: isRow }]">
    <!-- 标签 -->
    <div v-if="computedOptions.label?.show" class="vis-input__label" :style="labelStyle">
      {{ computedOptions.label.text }}
    </div>
    <!-- 输入框 -->
    <div class="vis-input__content" :class="[{ 'not-editable': isDesign }, { 'h-full flex-1': !isRow }]">
      <q-input
        ref="inputRef"
        v-model="inputValue"
        @update:modelValue="onValueChange"
        :placeholder="computedOptions.placeholder"
        flat
        borderless
        class="h-full px-2 relative items-center"
        input-class="!h-full"
        :input-style="inputStyle"
        :clearable="computedOptions.clearable"
        @clear="handleClear"
        :rules="lazyRules"
        lazy-rules
      >
        <template #before v-if="prefixType === 'text'">
          <span :style="prefixTextStyle">{{ computedOptions.style.prefix?.text }}</span>
        </template>
        <template #prepend v-if="computedOptions.query?.show || prefixType === 'icon'">
          <!-- 图标待解析 -->
          <span v-if="prefixType === 'icon'"></span>
          <q-select
            v-if="computedOptions.query?.show"
            v-model="queryValue"
            @update:modelValue="onValueChange"
            :options="InputQueryTypeOptions"
            borderless
            dense
            options-dense
            emit-value
            map-options
            :disable="computedOptions.query.locked"
          />
        </template>
        <template #append v-if="suffixType === 'icon'">
          <!-- 图标待解析 -->
          <span></span>
        </template>
        <template #after v-if="suffixType === 'text'">
          <span :style="suffixTextStyle">{{ computedOptions.style.suffix?.text }}</span>
        </template>
      </q-input>
    </div>
  </div>
</template>
<script lang="ts" src="./input.ts"></script>
<style lang="scss" src="./input.scss"></style>
