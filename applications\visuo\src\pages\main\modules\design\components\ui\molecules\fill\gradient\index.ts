import { defineComponent, ref, onMounted, computed, type PropType, watch } from 'vue';
import { FillPaints, ColorStop, useFill, Color, FillType, SystemPalette } from '@vis/document-core';
import { cloneDeep } from 'lodash-es';
import { useQuasar } from 'quasar';
import { useGraph } from '../../../../../hooks';

export default defineComponent({
  name: 'vis-fill-gradient',
  props: {
    modelValue: {
      type: Object as PropType<FillPaints>,
      required: true
    }
  },
  setup(props, { emit }) {
    const { formatRotate } = useGraph();

    // 旋转角度 默认水平
    const rotation = ref(props.modelValue.rotation || 90);
    const rotationShow = computed(() => {
      return ['gradient_linear', 'gradient_angular'].includes(props.modelValue.type);
    });
    const onChangeRotate = (value: number) => {
      rotation.value = formatRotate(value);
    };

    /**
     * 计算渐变点
     * @returns
     */
    const calcStops = () => {
      if (!props.modelValue.stops?.length) {
        const { r, g, b, a } = props.modelValue.color;
        return [new ColorStop(0, { r, g, b, a: 1 }), new ColorStop(1, { r, g, b, a: 0 })];
      }
      // 旋转角度赋值
      if (props.modelValue.rotation) {
        rotation.value = Number(props.modelValue.rotation);
      }

      return props.modelValue.stops || [];
    };
    const stops = ref(calcStops());

    const activeIndex = ref(0);

    const { sortByPosition, gradientBackground, getPaletteColors, getRgbaFromString } = useFill();
    const sliderBg = computed(() => {
      return gradientBackground(FillType.Linear, stops.value, 90);
    });

    const calcColor = (color: Color) => {
      if (!color) return '';

      const { r, g, b, a } = color;
      return `rgba(${r},${g},${b},${a})`;
    };
    const currentColor = ref(calcColor(stops.value[0].color));
    /**
     * 调色板改变color值
     * @param color
     * @returns
     */
    const updateColor = (color: string | null) => {
      if (!color) return;

      const { r, g, b, a } = getRgbaFromString(color);
      stops.value[activeIndex.value].color = { r, g, b, a };
    };

    const palette = computed(() => getPaletteColors(props.modelValue.type));

    /**
     * 根据渐变设置计算渐变背景样式
     * @param item
     * @returns
     */
    const calcBackground = (item: { stops: ColorStop[]; rotation: number }) => {
      return gradientBackground(props.modelValue.type, item.stops, item.rotation);
    };

    /**
     * 选择渐变色后设置当前渐变数据
     * @param item
     */
    const setGradient = (item: { stops: ColorStop[]; rotation: number }) => {
      stops.value = item.stops;
      rotation.value = item.rotation;
    };

    onMounted(() => {
      const gradient = document.querySelector('.gradient-box');
      const gradientPoints = document.querySelector('.gradient-points');

      if (!gradient || !gradientPoints) return;

      // 初始化轨道点击事件
      gradient.addEventListener('click', function (e: Event) {
        if (e.target === gradient.querySelector('.gradient-points')) {
          // 点击轨道添加点
          const rect = gradientPoints.getBoundingClientRect();

          const position = parseFloat((((e as MouseEvent).clientX - rect.left) / rect.width).toFixed(4));
          addStop(position);
        }
      });

      // 全局键盘事件监听
      document.addEventListener('keydown', function (e: KeyboardEvent) {
        if ((e.target as HTMLElement).tagName === 'INPUT') return;
        // 是否为填充（防止触发画布内删除组件操作，及其他输入框无法删除文字操作）
        if ((e.target as HTMLElement).closest('.vis-fill__types')) {
          // 检查是否按下Delete或Backspace键且有选中的点
          if (e.key === 'Delete' || e.key === 'Backspace') {
            e.preventDefault();
            e.stopPropagation();
            deleteStop();
          }
        }
      });

      /**
       * 添加点
       * @param position
       */
      const addStop = (position: number, color: Color = new Color()) => {
        stops.value.push(new ColorStop(position, color));
        activeIndex.value = stops.value.length - 1;

        // 添加完点之后设置其颜色
        setColor();
      };

      // 拖拽功能
      gradient.addEventListener('mousedown', function (e: Event) {
        e.preventDefault();
        e.stopPropagation();
        // 鼠标按下设置选中点
        const element = e.target as HTMLElement;
        if (!element.classList.contains('gradient-point')) return;
        const index = Array.prototype.indexOf.call(element.parentNode?.children, element);
        activeIndex.value = index;
        currentColor.value = calcColor(stops.value[index].color);

        const startX = (e as MouseEvent).clientX;
        const startLeft = parseFloat(element.style.left);

        // 滑动
        const moveHandler = (e: MouseEvent) => {
          const rect = gradientPoints.getBoundingClientRect();

          let newLeft = startLeft + parseFloat(((e.clientX - startX) / rect.width).toFixed(4)) * 100;
          newLeft = Math.max(0, Math.min(newLeft, 100));

          element.style.left = `${newLeft}%`;

          stops.value[index].position = parseFloat((newLeft / 100).toFixed(4));
        };

        const upHandler = () => {
          document.removeEventListener('mousemove', moveHandler);
          document.removeEventListener('mouseup', upHandler);
        };

        document.addEventListener('mousemove', moveHandler);
        document.addEventListener('mouseup', upHandler);
      });
    });

    const $q = useQuasar();
    /**
     * 删除点
     * @param point
     * @returns
     */
    const deleteStop = () => {
      // 确保渐变点至少有两个
      if (stops.value.length <= 2) {
        $q.notify({
          type: 'negative',
          message: '至少保留两个渐变点',
          position: 'top'
        });
        return;
      }

      if (activeIndex.value > -1) {
        stops.value.splice(activeIndex.value, 1);
        activeIndex.value--;
        activeIndex.value = Math.max(0, activeIndex.value);
      }
    };

    const handleUpdate = (obj: Object) => {
      emit('update:modelValue', Object.assign({}, props.modelValue, obj));
    };

    /**
     * 监听旋转角度变化
     */
    watch(
      () => rotation.value,
      (val) => {
        handleUpdate({ rotation: val });
      },
      { immediate: true }
    );
    /**
     * 监听渐变点变化
     */
    watch(
      () => stops.value,
      (val) => {
        currentColor.value = calcColor(val[activeIndex.value]?.color);
        handleUpdate({ stops: val });
      },
      {
        deep: true,
        immediate: true
      }
    );

    /**
     * 设置颜色
     */
    const setColor = () => {
      const sorted = sortByPosition(cloneDeep(stops.value));

      const stop = stops.value[activeIndex.value];
      sorted.forEach((p, i) => {
        if (p.position === stop.position) {
          stop.color = sorted[i - 1].color;
        }
      });
    };

    const reverse = () => {
      stops.value = stops.value.map((stop) => {
        return {
          position: 1 - stop.position,
          color: stop.color
        };
      });
    };

    return {
      rotation,
      rotationShow,
      onChangeRotate,
      sliderBg,
      calcColor,
      stops,
      activeIndex,
      deleteStop,

      currentColor,
      updateColor,
      palette,
      SystemPalette,
      calcBackground,
      setGradient,

      reverse
    };
  }
});
