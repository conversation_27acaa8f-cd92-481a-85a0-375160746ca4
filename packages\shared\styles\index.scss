// Import icon libraries
@import '../assets/fonts/iconfont.css';
@import '@quasar/extras/material-icons/material-icons.css';
@import '@quasar/extras/material-icons-outlined/material-icons-outlined.css';

// Import Quasar css
@import 'quasar/src/css/index.sass';

// Import Hetu theme css
@import '@hetu/theme/styles/index';

body {
  @include scrollbar;
}

code,
kbd,
pre,
samp {
  font-family: $typography-font-family;
}

// #region: transition
$ht-transition-prefix-cls: '#{$ht-prefix}-transition';
.#{$ht-transition-prefix-cls} {
  &--fade {
    &-enter-active,
    &-leave-active {
      transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
    }

    &-enter-from,
    &-leave-active {
      opacity: 0;
    }

    &-transform {
      &-enter-active,
      &-leave-active {
        transition: all 0.35s;
      }

      &-enter-from {
        transform: translateX(-30px);
        opacity: 0;
      }

      &-leave-to {
        transform: translateX(30px);
        opacity: 0;
      }
    }

    &-transform-y {
      &-enter-active,
      &-leave-active {
        transition: all 0.25s;
      }

      &-enter-from,
      &-leave-to {
        transform: translateY(-30px);
        opacity: 0;
      }
    }
  }

  &-list {
    &-enter-from {
      opacity: 0;
    }

    &-leave-to {
      display: none;
    }

    &-move:not(.no-transition),
    &-enter-active {
      transition: opacity, transform 1s;
    }
  }

  &-all {
    transition: $ht-all-transition;
  }
}

// #endregion

// #region: link
$ht-link-prefix-cls: '#{$ht-prefix}-link';
.#{$ht-link-prefix-cls} {
  @apply inline-flex items-center justify-center cursor-pointer duration-300;

  @each $type, $typeColor in $type-color-map {
    &.is-#{$type} {
      color: $typeColor;

      &:hover {
        color: color.mix($typeColor, #fff, 80%);
      }

      &.is-disabled {
        color: color.mix($typeColor, #fff, 50%);
      }
    }
  }

  &:hover {
    @apply text-primary;
  }

  &.is-disabled {
    @apply text-font-placeholder cursor-not-allowed;
  }

  .q-icon {
    font-size: 1.286em;
  }
}

// #endregion

// #region: field
$ht-field-prefix-cls: '#{$ht-prefix}-field';
.#{$ht-field-prefix-cls} {
  &-search.q-field {
    .q-field__control {
      &::before {
        display: none;
      }
    }

    .q-field__control,
    .q-field__marginal {
      height: 36px;
    }

    .q-field__native::placeholder,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__prepend,
    .q-field__append,
    .q-field__input {
      @apply text-font-placeholder;
    }

    .q-field__native {
      @apply text-font-regular;
    }

    &.q-field--standout {
      .q-field__control {
        @apply bg-grey-light;
      }

      &.q-field--highlighted {
        .q-field__control {
          @apply bg-primary-lighter shadow-none;

          &::before {
            display: none;
          }
        }

        .q-field__native,
        .q-field__prefix,
        .q-field__suffix,
        .q-field__prepend,
        .q-field__append,
        .q-field__input {
          @apply text-font-regular;
        }
      }
    }
  }
}

// #endregion: field

$ht-theme-prefix-cls: '#{$ht-prefix}-theme';
.#{$ht-theme-prefix-cls} {
  @for $i from 1 through $ht-theme-count {
    &#{$i} {
      background: map.get($ht-theme, $i);
    }
  }
}

.#{$ht-prefix}-error-message-box {
  .q-dialog__message {
    flex: 1;
    overflow: auto;
  }

  pre {
    @apply bg-grey-light;

    min-width: 100%;
  }

  &.q-dark {
    pre {
      background-color: $dark-page;
    }
  }
}

@keyframes badge-processing {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

// #region: quasar
.q-scrollarea {
  &__thumb {
    &.q-scrollarea__thumb--v {
      width: 6px;
    }

    &.q-scrollarea__thumb--h {
      height: 6px;
    }
  }

  &.is-flex-content &__content {
    display: flex;
    flex-direction: column;
  }
}

.q-splitter {
  .q-splitter__separator {
    color: $separator-color;
  }

  &--dark .q-splitter__separator {
    @apply bg-black;

    color: $separator-dark-color;
  }

  &.is-primary-active {
    .q-splitter__separator {
      @apply hover:bg-primary hover:text-primary;

      transition: $generic-hover-transition;
    }

    &.q-splitter--active > .q-splitter__separator {
      @apply bg-primary text-primary;
    }
  }
}

.no-helper > .q-focus-helper {
  display: none;
}

.is-merge-separator {
  > .q-separator,
  > :not(.q-separator):last-of-type {
    + .q-separator {
      display: none;
    }
  }
}

// #endregion
