/**
 * 部门
 */
export class Dept {
  id = '';
  /** 父级Id */
  parentId = '';
  /** 部门名称 */
  deptName = '';
  /** 父级名称 */
  parentName = '';
  orderNo = 1;

  [key: string]: any;

  constructor(id?: string, deptName?: string) {
    id !== undefined && (this.id = id);
    deptName !== undefined && (this.deptName = deptName);
  }
}

export interface DeptTreeNode extends Dept {
  children?: DeptTreeNode[];
}
