<template>
  <div
    class="ht-app-page__main col column relative-position q-gutter-y-md"
    :class="showPageUploader && ($q.dark.isActive ? 'bg-dark-light' : 'bg-primary-lighter')"
    @dragenter="dragenter"
  >
    <div class="ht-app-page__main-header row items-center">
      <ht-app-breadcrumbs
        class="col"
        :title="activeMenu.menuName"
        :search="breadcrumbs.search"
        :list="breadcrumbs.list"
        @backup="breadcrumbsBackup"
      />
      <div class="col row flex-center gt-sm">
        <q-input
          v-model="query.search"
          class="ht-field ht-field--medium"
          :class="!$q.dark.isActive && 'ht-field--light'"
          placeholder="搜索"
          dense
          rounded
          standout
          @keyup.enter="search"
          @blur="search"
        >
          <template v-slot:prepend>
            <q-icon name="search" size="20px" />
          </template>
        </q-input>
      </div>
      <div class="col">
        <div class="row justify-end" :class="`q-gutter-x-${$q.screen.gt.md ? 'md' : 'sm'}`">
          <ht-app-sort :order="query.order" :columns="sortColumns" @sort="sort" />
          <label class="ht-link" v-acl="acl.upload" @click="pickFiles">
            <q-icon class="gt-md" name="cloud_upload" size="xs" />
            <span class="q-ml-xs">上传</span>
          </label>
          <template v-if="hasActive">
            <label class="ht-link gt-sm" v-acl="acl.moveTool" @click="move()">
              <q-icon class="gt-md" name="open_with" size="xs" />
              <span class="q-ml-xs">移动</span>
            </label>
            <label class="ht-link is-negative gt-sm" v-acl="acl.deleteTool" @click="del()">
              <q-icon class="gt-md" name="delete_outline" size="xs" />
              <span class="q-ml-xs">删除</span>
            </label>
          </template>
          <q-checkbox
            v-if="multi"
            v-model="checkAll"
            v-acl="acl.checkAll"
            class="gt-sm"
            size="xs"
            @update:model-value="selectAll"
          >
            全选
          </q-checkbox>
        </div>
      </div>
    </div>
    <q-scroll-area class="col" ref="scrollAreaRef">
      <div class="ht-app-page__main-body col column q-gutter-y-sm">
        <!-- 文件夹 -->
        <ht-app-folder
          :folders="folders"
          :category="category"
          :acl="acl"
          :favorite="false"
          :group="folderGroup"
          :multi="multi"
          deleteType="delete"
          @opened="folderOpened"
        >
          <template #tools="{ folder }">
            <q-item v-ca:[folder.privilegeId]="acl.moveDirectory" clickable v-close-popup @click="move(folder)">
              <q-item-section class="col-auto">
                <q-icon name="open_with" />
              </q-item-section>
              <q-item-section>移动</q-item-section>
            </q-item>
            <q-separator class="q-my-xs" />
          </template>
        </ht-app-folder>

        <!-- 文件 -->
        <ht-app-file-container v-if="files.length" :group="fileGroup">
          <ht-app-file
            v-for="file in files"
            :key="file.uid || file.id"
            class="ht-material-media"
            :file="file"
            :category="category"
            :acl="acl"
            :showToolBar="false"
            :multi="multi"
            deleteType="delete"
            @click="clickFile"
          >
            <q-card class="ht-material-media__cover ht-hoverable flex flex-center" flat>
              <template v-if="file.attachmentId">
                <q-img v-if="isImage" class="fit" :src="downloadUrl(file.attachmentId, 'scale')" fit="contain" />
                <video v-else class="fit" :src="downloadUrl(file.attachmentId)"></video>
                <label
                  class="ht-material-media__type ht-hover--visible absolute-top-left"
                  :class="$q.dark.isActive ? 'bg-black/45' : 'bg-grey-light'"
                >
                  {{ file.extension }}
                </label>
                <div
                  class="ht-hover--visible absolute-bottom row justify-around"
                  :class="$q.dark.isActive ? 'bg-black/45' : 'bg-grey-light'"
                >
                  <q-btn
                    :icon="isImage ? 'zoom_in' : 'play_circle'"
                    size="sm"
                    flat
                    dense
                    @click.stop="togglePreview(file.attachmentId)"
                  >
                    <q-tooltip>{{ isImage ? '查看大图' : '播放' }}</q-tooltip>
                  </q-btn>
                  <q-btn v-acl="acl.move" icon="open_with" size="sm" flat dense @click.stop="move(file)">
                    <q-tooltip>移动</q-tooltip>
                  </q-btn>
                  <q-btn v-acl="acl.delete" icon="delete_outline" size="sm" flat dense @click.stop="del(file)">
                    <q-tooltip>删除</q-tooltip>
                  </q-btn>
                </div>
              </template>

              <!-- 上传进度 -->
              <q-circular-progress
                v-else-if="file.percent !== undefined"
                :value="file.percent"
                show-value
                :thickness="0.22"
                font-size="12px"
                size="50px"
                color="primary"
              >
                {{ file.percent }}%
              </q-circular-progress>
            </q-card>
          </ht-app-file>
        </ht-app-file-container>

        <div v-else-if="!loading" v-acl="acl.upload" class="flex flex-center col">
          <div class="text-center cursor-pointer" @click="pickFiles">
            <q-icon class="q-mb-md text-primary" size="lg" :name="isImage ? 'crop_original' : 'video_camera_back'" />
            <div class="text-font-regular">
              将文件拖到空白处或<span class="text-primary">点击上传</span>
              <div class="text-font-placeholder">支持拓展名：{{ accept }}...</div>
            </div>
          </div>
        </div>
      </div>
    </q-scroll-area>

    <div
      v-acl="acl.upload"
      v-show="showPageUploader"
      class="absolute-full"
      @dragleave="dragleave"
      @dragover="stopAndPreventDrag"
      @drop.stop="drop"
    >
      <q-file
        v-model="uploadDatas"
        class="hidden"
        ref="uploaderRef"
        :accept="accept"
        multiple
        @update:model-value="upload"
      />
    </div>
  </div>

  <template v-if="preview">
    <!-- 预览图片 -->
    <q-carousel
      v-if="isImage"
      v-model="previewId"
      class="!bg-black !bg-opacity-60"
      control-type="unelevated"
      control-color="grey-7"
      animated
      arrows
      infinite
      fullscreen
    >
      <q-carousel-slide v-for="file in files" :key="file.id" :name="file.attachmentId">
        <q-img class="fit" :src="downloadUrl(file.attachmentId)" fit="scale-down" />
      </q-carousel-slide>

      <template v-slot:control>
        <q-carousel-control position="top-right" :offset="[16, 16]">
          <q-btn icon="close" :color="'grey-7'" size="sm" round unelevated @click="togglePreview()" />
        </q-carousel-control>
      </template>
    </q-carousel>

    <!-- 预览视频 -->
    <div
      v-else
      tabindex="1"
      class="fixed-full z-top !bg-black !bg-opacity-60 row flex-center"
      @click="togglePreview()"
      @keyup.esc.stop="togglePreview()"
    >
      <q-btn
        class="absolute-top-right q-mt-md q-mr-md"
        icon="close"
        :color="'grey-7'"
        size="sm"
        round
        unelevated
        @click.stop="togglePreview()"
      ></q-btn>
      <video class="col-8" :autoplay="true" :controls="true" :src="downloadUrl(previewId)"></video>
    </div>
  </template>
</template>
<script src="./media.ts" lang="ts"></script>
