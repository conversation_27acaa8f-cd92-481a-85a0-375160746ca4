<template>
  <div ref="editorRef" :style="style"></div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { LoaderService } from '../../services';

defineOptions({ name: 'ht-monaco-editor' });
const props = defineProps({
  modelValue: {
    type: String
  },
  value: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 200
  },
  theme: {
    type: String,
    default: 'vs-light'
  },
  language: {
    type: String
  },
  options: {
    type: Object,
    required: true
  },
  srcPath: {
    type: String
  },
  /**
   * 在编辑器实例创建之前调用
   *
   * 注意不要将此函数改为通过 Vue 抛出事件，某些代码需要顺序执行
   */
  onBeforeCreateEditor: {
    type: Function
  }
});

const emit = defineEmits([
  'mount',
  'unmount',
  'contextMenu',
  'blur',
  'blurText',
  'configuration',
  'position',
  'selection',
  'update:modelValue',
  'change',
  'modelDecorations',
  'modelLanguage',
  'modelOptions',
  'afterDispose',
  'focus',
  'focusText',
  'layout',
  'scroll',
  'keydown',
  'keyup',
  'mouseDown',
  'mouseLeave',
  'mouseMove',
  'mouseUp'
]);

const style = computed(() => `height: ${props.height + 'px'}`);

let editor: any;

watch(
  () => props.options,
  () => {
    if (editor) {
      editor.updateOptions(props.options);
    }
  },
  {
    deep: true
  }
);

watch(
  () => props.value,
  (newVal: string) => {
    if (editor) {
      if (newVal !== editor.getValue()) {
        editor.setValue(newVal);
      }
    }
  }
);

watch(
  () => props.modelValue,
  (newVal: string | undefined) => {
    if (editor) {
      if (newVal !== editor.getValue()) {
        editor.setValue(newVal);
      }
    }
  }
);

watch(
  () => props.language,
  (newVal: string | undefined) => {
    if (editor) {
      (window as any).monaco.editor.setModelLanguage(editor.getModel(), newVal);
    }
  }
);

watch(
  () => props.theme,
  (newVal: string) => {
    if (editor) {
      (window as any).monaco.editor.setTheme(newVal);
    }
  }
);

onMounted(() => {
  LoaderService.amd().then((amdRequire) => {
    if (props.srcPath) {
      amdRequire.config({
        paths: { vs: props.srcPath }
      });
    }
    amdRequire(['vs/editor/editor.main'], function () {
      initMonaco((window as any).monaco);
    });
  });
});

onBeforeUnmount(() => {
  emit('unmount', editor && editor.getValue());
  editor && editor.dispose();
});

const editorRef = ref();

const initMonaco = (monaco: any) => {
  if (typeof props.onBeforeCreateEditor === 'function') {
    props.onBeforeCreateEditor(monaco);
  }

  const options = Object.assign(
    {
      value: props.modelValue || props.value,
      theme: props.theme,
      language: props.language
    },
    {
      selectOnLineNumbers: false,
      lineDecorationsWidth: 1,
      lineNumbersMinChars: 3,
      lineNumbers: 'on',
      formatOnType: true,
      minimap: { enabled: true }
    },
    props.options
  );
  editor = monaco.editor.create(editorRef.value, options);

  /** 格式化代码 */
  setTimeout(() => {
    const formatAction = editor.getAction('editor.action.formatDocument');
    formatAction.run();
  }, 500);

  editor.formatDocument = () => editor.getAction['editor.action.formatDocument'].run();
  emit('mount', editor);

  editor.onContextMenu(function (event: any) {
    return emit('contextMenu', event);
  });
  editor.onDidBlurEditorWidget(function () {
    return emit('blur');
  });
  editor.onDidBlurEditorText(function () {
    return emit('blurText');
  });
  editor.onDidChangeConfiguration(function (event: any) {
    return emit('configuration', event);
  });
  editor.onDidChangeCursorPosition(function (event: any) {
    return emit('position', event);
  });
  editor.onDidChangeCursorSelection(function (event: any) {
    return emit('selection', event);
  });
  editor.onDidChangeModel(function (event: any) {
    return emit('update:modelValue', event);
  });
  editor.onDidChangeModelContent(function (event: any) {
    const value = editor.getValue();

    if (props.value !== value || !value) {
      emit('update:modelValue', value, event);
    }
  });
  editor.onDidChangeModelDecorations(function (event: any) {
    return emit('modelDecorations', event);
  });
  editor.onDidChangeModelLanguage(function (event: any) {
    return emit('modelLanguage', event);
  });
  editor.onDidChangeModelOptions(function (event: any) {
    return emit('modelOptions', event);
  });
  editor.onDidDispose(function (event: any) {
    return emit('afterDispose', event);
  });
  editor.onDidFocusEditorWidget(function () {
    return emit('focus');
  });
  editor.onDidFocusEditorText(function () {
    return emit('focusText');
  });
  editor.onDidLayoutChange(function (event: any) {
    return emit('layout', event);
  });
  editor.onDidScrollChange(function (event: any) {
    return emit('scroll', event);
  });
  editor.onKeyDown(function (event: any) {
    return emit('keydown', event);
  });
  editor.onKeyUp(function (event: any) {
    return emit('keyup', event);
  });
  editor.onMouseDown(function (event: any) {
    return emit('mouseDown', event);
  });
  editor.onMouseLeave(function (event: any) {
    return emit('mouseLeave', event);
  });
  editor.onMouseMove(function (event: any) {
    return emit('mouseMove', event);
  });
  editor.onMouseUp(function (event: any) {
    return emit('mouseUp', event);
  });
};

const getMonaco = () => {
  return editor;
};

const focus = () => {
  editor.focus();
};
</script>
