<template>
  <div class="vis-config-status-text">
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__label">颜色</div>
        <div class="vis-form-field__content">
          <vis-fill v-model="fontStyle.color" :showEyes="false" :minusWidth="0" />
        </div>
      </div>
      <!-- 字体 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">字体</div>
        <div class="vis-form-field__content">
          <vis-select v-model="fontStyle.fontFamily" class="w-full" :options="fontFamilys" :popHeight="240">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: fontStyle.fontWeight, fontFamily: opt }">{{ opt }}</span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
      </div>

      <!-- 粗细 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">粗细</div>
        <div class="vis-form-field__content">
          <vis-select v-model="fontStyle.fontWeight" :options="fontWeights" class="w-full">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: opt.value, fontFamily: fontStyle.fontFamily }">
                    {{ opt.label }}
                  </span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
      </div>

      <!-- 字号 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">字号</div>
        <div class="vis-form-field__content">
          <vis-select v-model="fontStyle.fontSize" :options="fontSizes" editable />
        </div>
      </div>

      <!-- 阴影位置 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">阴影位置</div>
        <div class="vis-form-field__content">
          <vis-number v-model="fontStyle.textEffects.offset.x" icon="hticon-vis-letter-x"></vis-number>
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label"></div>
        <div class="vis-form-field__content">
          <vis-number v-model="fontStyle.textEffects.offset.y" icon="hticon-vis-letter-y"></vis-number>
        </div>
      </div>

      <!-- 阴影效果 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">效果</div>
        <div class="vis-form-field__content">
          <vis-number v-model="fontStyle.textEffects.blur" icon="hticon-vis-blur" :min="0"></vis-number>
        </div>
      </div>
      <!-- 阴影颜色 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">阴影颜色</div>
        <div class="vis-form-field__content">
          <vis-fill :minusWidth="0" v-model="fontStyle.textEffects.color" :onlyColor="true" :hideTitle="true" />
        </div>
      </div>
      <!-- 字间距 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">字间距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="fontStyle.letterSpacing" icon="vis-resizing-w" :min="0" />
        </div>
      </div>

      <!-- 行高 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">行高</div>
        <div class="vis-form-field__content">
          <vis-number v-model="fontStyle.lineHeight" icon="vis-resizing-h" :min="0" />
        </div>
      </div>

      <!-- 文字修饰 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">文字修饰</div>
        <div class="vis-form-field__content">
          <div class="flex-1 flex btn-bg">
            <q-btn
              v-for="(item, index) in fontStyles"
              :key="item.value"
              dense
              flat
              size="xs"
              class="rounded-borders overflow-hidden flex-1"
              :class="[
                { 'vis-btn-active': fontStyle[fontStyleKeys[index]] },
                { 'q-mr-xs': index === fontStyles.length - 1 }
              ]"
              :label="item.label"
              @click="setFontStyle(fontStyleKeys[index])"
            >
              <template v-if="item.icon">
                <ht-icon v-if="isIconFont(item.icon)" :name="item.icon" class="vis-icon" />
                <q-icon v-else :name="item.icon" />
              </template>
              <q-tooltip v-if="item.tip">{{ item.tip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./text.ts"></script>
