// #region: hover
$hover-prefix-cls: '#{$ht-prefix}-hover';
.#{$hover-prefix-cls} {
  &--show,
  &--hide,
  &--visible,
  &--hidden {
    transition: opacity $generic-hover-transition;
  }

  &--show {
    display: none;
    opacity: 0;
  }

  &--hide {
    display: inherit;
    opacity: 1;
  }

  &able:hover &--show,
  &able.is-hover &--show {
    display: inherit;
    opacity: 1;
  }

  &able:hover &--hide,
  &able.is-hover &--hide {
    display: none;
    opacity: 0;
  }

  &--visible {
    visibility: hidden;
  }

  &--hidden {
    visibility: visible;
  }

  &able:hover &--visible,
  &able.is-hover &--visible {
    visibility: visible;
    opacity: 1;
  }

  &able:hover &--hidden,
  &able.is-hover &--hidden {
    visibility: hidden;
    opacity: 0;
  }

  &--shadow {
    transition: $shadow-transition;

    &:not(:hover, .is-hover) {
      box-shadow: none;
    }
  }
}

// #endregion

// #region: btn
$btn-prefix-cls: '#{$ht-prefix}-btn';

.#{$btn-prefix-cls} {
  &--outline,
  &-group--outline {
    &::before {
      content: '';
      display: block;
      position: absolute;
      inset: 0;
      transition: border-color 0.36s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid $separator-color;
      border-radius: inherit;
    }
  }

  &--dashed {
    &::before {
      border-style: dashed;
    }
  }

  &--small,
  &-group--small .q-btn {
    min-height: 32px;
    padding-right: 8px;
    padding-left: 8px;
    font-size: 12px;
  }

  &--mini,
  &-group--mini .q-btn {
    min-height: 24px;
    padding: 0 5px;
    font-size: 12px;
  }
}

.body--dark {
  .#{$btn-prefix-cls} {
    &--outline,
    &-group--outline {
      &::before {
        border-color: $separator-dark-color;
      }
    }
  }
}

// #endregion

// #region: tab
$tabs-prefix-cls: '#{$ht-prefix}-tabs';

.#{$tabs-prefix-cls} {
  &--small .q-tab {
    min-height: 32px;

    &__label {
      font-size: 12px;
    }
  }

  &--mini .q-tab {
    min-height: 24px;

    &__label {
      font-size: 12px;
    }
  }

  &--small,
  &--mini {
    &.q-tabs--scrollable.q-tabs__arrows--outside.q-tabs--horizontal {
      padding-right: 24px;
      padding-left: 24px;
    }

    .q-tab__indicator {
      height: 1px;
    }

    .q-tabs__arrow {
      min-width: inherit;
      font-size: 18px;
    }
  }
}

.q-tabs--dense .q-tab {
  min-height: 36px;
}

// #endregion

// #region: field
$field-prefix-cls: '#{$ht-prefix}-field';

.#{$field-prefix-cls} {
  &__label {
    @each $size in map-keys($ht-field-label-size) {
      &--#{$size} {
        width: map.get($ht-field-label-size, #{$size});
      }
    }
  }

  &.q-field:not(.q-field--dark) {
    .q-field__native::placeholder,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__prepend,
    .q-field__append,
    .q-field__input {
      @apply text-font-placeholder;
    }

    .q-field__native {
      @apply text-font-regular;
    }

    &.q-field--highlighted {
      .q-field__native,
      .q-field__prefix,
      .q-field__suffix,
      .q-field__prepend,
      .q-field__append,
      .q-field__input {
        @apply text-font-regular;
      }
    }
  }

  &.q-field--dark {
    &.q-field--standout {
      &.q-field--highlighted {
        .q-field__native,
        .q-field__prefix,
        .q-field__suffix,
        .q-field__prepend,
        .q-field__append,
        .q-field__input {
          color: #fff;
        }

        .q-field__control {
          background: rgb(255 255 255 / 7%);

          &::before {
            opacity: 1;
          }
        }
      }
    }
  }

  &--light,
  &--lighter,
  &--transparent-lighter {
    .q-field__control {
      &::before {
        display: none;
      }
    }

    &.q-field--standout {
      &.q-field--highlighted {
        .q-field__control {
          @apply shadow-none;

          &::before {
            display: none;
          }
        }
      }
    }
  }

  &--light,
  &--lighter {
    &.q-field--standout {
      &.q-field--highlighted {
        .q-field__control {
          @apply bg-primary-lighter;
        }
      }
    }
  }

  &--light {
    &.q-field--standout {
      .q-field__control {
        @apply bg-grey-light;
      }
    }
  }

  &--transparent-lighter {
    &.q-field--standout {
      .q-field__control {
        @apply bg-transparent;
      }
    }
  }

  &--large {
    .q-field__control,
    .q-field__marginal {
      height: 48px;
    }

    &.q-field--auto-height .q-field__control,
    &.q-field--auto-height .q-field__native {
      min-height: 48px;
    }
  }

  &--medium {
    .q-field__control,
    .q-field__marginal {
      height: 36px;
    }

    &.q-field--auto-height .q-field__control,
    &.q-field--auto-height .q-field__native {
      height: auto;
      min-height: 36px;
    }
  }

  &--small {
    font-size: 12px;

    .q-field__marginal {
      font-size: 16px;
    }

    &:not(.q-textarea) .q-field__control,
    &:not(.q-textarea) .q-field__marginal {
      height: 32px;
    }

    &.q-field--auto-height .q-field__control,
    &.q-field--auto-height .q-field__native {
      height: auto;
      min-height: 32px;
    }

    .q-field__label {
      font-size: 12px;
    }

    &.q-field--float .q-field__label {
      transform: translateY(-50%) scale(0.8333);
    }
  }

  &--mini {
    &,
    .q-field__marginal {
      font-size: 12px;
    }

    &:not(.q-textarea) {
      .q-field__control,
      .q-field__marginal {
        height: 24px;
      }
    }

    &.q-field--auto-height .q-field__control,
    &.q-field--auto-height .q-field__native {
      min-height: 24px;
    }

    &.q-textarea .q-field__native,
    .q-field__native,
    .q-field__prefix,
    .q-field__suffix,
    .q-field__input {
      padding: 4px 0;
    }

    .q-field__label {
      font-size: 12px;
    }

    &.q-field--standout {
      .q-field__control {
        padding: 0 4px;
      }
    }

    &.q-field--with-bottom {
      padding-bottom: 16px;
    }

    .q-field__bottom {
      min-height: inherit;
      padding: 4px 4px 0;
    }
  }

  &--hide-spin {
    /* 隐藏 input[type='number'] 右侧的增加和减少按钮 */
    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
      appearance: none;
      margin: 0;
    }

    input[type='number'] {
      appearance: textfield;
    }
  }
}

// #endregion

// #region: table
$table-prefix-cls: '#{$ht-prefix}-table';
.#{$table-prefix-cls} {
  &--edit {
    .q-field--with-bottom {
      padding-bottom: 0;

      .q-field__bottom {
        transform: translateY(0);
        pointer-events: none;

        .q-field__messages {
          width: 100%;
          text-align: right;
        }
      }
    }

    .q-field--error {
      .q-placeholder {
        color: var(--q-negative);
      }
    }
  }

  &--sticky {
    &-header {
      thead tr th {
        position: sticky;
        z-index: 1;
        background-color: inherit;
      }

      thead tr:first-child th {
        top: 0;
      }
    }

    &-bottom .q-table__bottom {
      position: sticky;
      z-index: 1;
      bottom: 0;
      background-color: inherit;
    }
  }
}

// #endregion

// #region: tree
$tree-prefix-cls: '#{$ht-prefix}-tree';

.#{$tree-prefix-cls} {
  .q-tree__node-header {
    padding: 0;
    border-radius: 0;

    > .q-tree__arrow {
      display: none;
    }

    .q-tree__arrow {
      color: $grey;
    }
  }

  .q-tree__node--selected {
    @apply bg-primary-lighter;

    .q-focus-helper {
      display: none;
    }
  }

  .q-tree__children,
  .q-tree__node--child {
    padding-left: 0;
  }

  &.q-tree--dark {
    .q-tree__node--selected {
      @apply bg-primary;
    }
  }
}

// #endregion

// #region: router progressbar
$router-progressbar-prefix-cls: '#{$ht-prefix}-router-progressbar';

.#{$router-progressbar-prefix-cls} {
  position: fixed;
  z-index: 9999;
  width: 100vw;
  height: 3px;
  overflow: hidden;
  background: rgb(221 221 221 / 40%);

  &::after {
    content: ' ';
    display: block;
    width: 50vw;
    height: 100%;
    transform-origin: top left;
    animation: gradcolours 5s steps(1) infinite, loadthird 1s infinite linear;
  }
}

@keyframes loadthird {
  0% {
    transform: translateX(-50vw);
  }

  100% {
    transform: translateX(100vw);
  }
}

@keyframes gradcolours {
  0% {
    background: linear-gradient(
      90deg,
      rgb(98 151 164 / 0%) 0%,
      rgb(30 144 255 / 100%) 30%,
      rgb(30 144 255 / 100%) 50%,
      rgb(30 144 255 / 100%) 70%,
      rgb(98 151 164 / 0%) 100%
    );
  }

  20% {
    background: linear-gradient(
      90deg,
      rgb(132 190 190 / 0%) 0%,
      rgb(255 215 0 / 100%) 30%,
      rgb(255 215 0 / 100%) 50%,
      rgb(255 215 0 / 100%) 70%,
      rgb(132 190 190 / 0%) 100%
    );
  }

  40% {
    background: linear-gradient(
      90deg,
      rgb(233 135 36 / 0%) 0%,
      rgb(255 140 0 / 100%) 30%,
      rgb(255 140 0 / 100%) 50%,
      rgb(255 140 0 / 100%) 70%,
      rgb(233 135 36 / 0%) 100%
    );
  }

  60% {
    background: linear-gradient(
      90deg,
      rgb(175 201 78 / 0%) 0%,
      rgb(144 238 144 / 100%) 30%,
      rgb(144 238 144 / 100%) 50%,
      rgb(144 238 144 / 100%) 70%,
      rgb(175 201 78 / 0%) 100%
    );
  }

  80% {
    background: linear-gradient(
      90deg,
      rgb(232 128 152 / 0%) 0%,
      rgb(255 69 0 / 100%) 30%,
      rgb(255 69 0 / 100%) 50%,
      rgb(255 69 0 / 100%) 70%,
      rgb(232 128 152 / 0%) 100%
    );
  }
}

// #endregion

// #region: sticky
.#{$ht-prefix}-sticky-bottom {
  position: sticky;
  z-index: 1;
  bottom: 0;
  background-color: inherit;
}

// #endregion
