import { ROUTE_DOMAIN_KEY, MYSPACE_CODE, ROUTE_SPACE_PATH_PREFIX } from '../models';

/** 根据路径获取空间路由路径前缀 */
export const getSpacePathPrefix = (prefix?: string) => {
  if (prefix) {
    return `${prefix.endsWith('/') ? prefix : prefix + '/'}${ROUTE_SPACE_PATH_PREFIX}`;
  }
  return `/${ROUTE_SPACE_PATH_PREFIX}`;
};

/** 根据路径获取空间路由路径 */
export const getSpaceRoutePath = (path: string, prefix?: string) => {
  const arr = path.split('');
  return `${getSpacePathPrefix(prefix)}/:${ROUTE_DOMAIN_KEY}${arr.pop() === '/' ? arr.join('') : path}`;
};

/** 根据路径获取指定空间路由路径, 默认为个人空间 */
export const getSpacePath = (path: string, space = MYSPACE_CODE) =>
  getSpaceRoutePath(path).replace(`:${ROUTE_DOMAIN_KEY}`, space || MYSPACE_CODE);
