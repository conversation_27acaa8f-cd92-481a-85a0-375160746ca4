<template>
  <div class="vis-button-group flex-1 flex rounded-borders">
    <q-btn
      v-for="(item, index) in options"
      :key="item.value"
      dense
      flat
      size="xs"
      class="rounded-borders overflow-hidden flex-1"
      :class="[
        { 'vis-btn-active': modelValue === item.value },
        { '!mr-0': index !== options.length - 1 },
        `icon-${item.value}`
      ]"
      :label="item.label"
      @click.stop="handleUpdate(item.value)"
      @mouseenter="handleHover(item.value)"
      @mouseleave="handleLeave"
    >
      <template v-if="item.icon">
        <ht-icon v-if="isIconFont(item.icon)" :name="item.icon" class="vis-icon" />
        <q-icon v-else :name="item.icon" />
      </template>
      <q-tooltip v-if="item.tip" :offset="[0, 4]">{{ item.tip }}</q-tooltip>
    </q-btn>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
