import { Breadcrumbs } from './breadcrumbs';
import { File, FileContainer } from './file';
import { Folder } from './folder';
import { Layout } from './layout';
import { Page } from './page';
import { Sort } from './sort';
import { FileInfo } from './file-info';
import { Recycle } from './recycle';
import { WorkLog } from './worklog';
import { DeleteAllFavorite } from './delete-all-favorite';

import { HtAppRelease } from './release';
import { HtAppReleaseHistory } from './release-history';
import { HtAccessPolicy } from './access-policy';
import { HtAppMove } from './move';
import { HtAppMoveSpace } from './move-space';
import { ColorPicker } from './color-picker';
import { IconPicker } from './icon-picker';
import { HtTestResponse } from './test-response';
import HtFilter from './filter/index.vue';
import { HtAppCalculatedField } from './calculated-field';

export {
  HtAppRelease,
  HtAppReleaseHistory,
  HtAccessPolicy,
  HtAppMove,
  HtAppMoveSpace,
  HtAppCalculatedField,
  HtTestResponse,
  HtFilter
};

export const components = [
  Breadcrumbs,
  File,
  FileContainer,
  Folder,
  Layout,
  Page,
  Sort,
  FileInfo,
  Recycle,
  WorkLog,
  DeleteAllFavorite,
  ColorPicker,
  IconPicker
];
