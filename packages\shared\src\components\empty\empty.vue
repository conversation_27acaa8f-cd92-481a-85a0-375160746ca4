<template>
  <div class="ht-empty col column flex-center text-font-secondary">
    <img :style="{ width: size }" :src="'./static-next/svg/empty.svg'" />
    <div v-if="description" class="q-pt-md">{{ description }}</div>
    <slot name="description"></slot>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'ht-empty' });

withDefaults(
  defineProps<{
    description?: string;
    size?: string;
  }>(),
  {
    description: '',
    size: '160px'
  }
);
</script>
