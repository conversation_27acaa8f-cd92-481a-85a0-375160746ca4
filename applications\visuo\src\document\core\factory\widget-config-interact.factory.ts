import { WidgetConfigEvent, type IWidgetConfigInteract, WidgetType } from '../models';

/**
 * 组件配置交互工厂类
 * 用于管理不同组件的交互事件配置
 * <AUTHOR>
 */
export class WidgetConfigInteractFactory {
  private static readonly MOUSE_EVENTS = {
    click: new WidgetConfigEvent('点击事件'),
    mouseover: new WidgetConfigEvent('悬停事件'),
    mouseout: new WidgetConfigEvent('移出事件')
  };

  /**
   * 段落组件配置
   */
  readonly [WidgetType.Paragraph]: IWidgetConfigInteract = {
    events: {
      ...WidgetConfigInteractFactory.MOUSE_EVENTS
    }
  };

  /**
   * 标题组件配置
   */
  readonly [WidgetType.Title]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    }
  };

  /**
   * 标题组件配置
   */
  readonly [WidgetType.Tab]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    }
  };

  /**
   * 文本输入组件配置
   */
  readonly [WidgetType.Input]: IWidgetConfigInteract = {
    events: {
      click: WidgetConfigInteractFactory.MOUSE_EVENTS.click
    }
  };

  /**
   * 获取组件配置
   * @param widgetName 组件名称
   * @returns 组件交互配置
   */
  getWidgetConfig(widgetType: WidgetType): IWidgetConfigInteract | undefined {
    return this[widgetType];
  }
}
