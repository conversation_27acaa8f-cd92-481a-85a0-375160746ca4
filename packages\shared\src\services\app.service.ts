import { createSingleClass, useAppContext } from '@hetu/core';
import { type HttpApi, HttpApiService, responseData, responseReject } from '@hetu/http';
import { type App, SYSTEM_API_PREFIX, TOKEN_APP_KEY } from '../models';
import { inject } from 'vue';

class AppApi implements HttpApi {
  list = `${SYSTEM_API_PREFIX}/account/product/list`;
}

class AppServiceCtor extends HttpApiService<AppApi> {
  httpApi = new AppApi();

  httpModuleKey = 'app';

  loaded = false;

  /** 应用列表 */
  apps: App[] = [];

  get appKey() {
    return useAppContext(() => inject(TOKEN_APP_KEY) as string);
  }

  async getApps(reload = false): Promise<App[]> {
    if (!reload && this.loaded) {
      return this.apps;
    }
    const apps = await this.http.get(this.api.list).then(responseData, responseReject);
    this.apps = apps || [];
    this.loaded = true;

    return this.apps;
  }

  async can(appKey?: string) {
    const apps = await this.getApps();
    const result = apps.find((app) => app.moduleCode === (appKey ?? this.appKey));
    return !!result;
  }
}

export const AppService = createSingleClass(AppServiceCtor);
