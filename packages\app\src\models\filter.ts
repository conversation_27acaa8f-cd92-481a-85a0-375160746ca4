import { isNumber } from 'lodash-es';
import type { AxisField } from './axis-field';

export enum FilterType {
  /** 值范围 */
  Range = 'range',

  /** 相对日期 */
  Scope = 'scope',

  /** 通配符 */
  Query = 'query',

  /** 选定值 */
  Normal = 'normal'
}

export const FilterTypeName = {
  [FilterType.Range]: '值范围',
  [FilterType.Scope]: '相对日期',
  [FilterType.Query]: '通配符',
  [FilterType.Normal]: '选定值'
};

/**
 * 根据字段的数据类型返回支持的筛选器类型
 * @param dataType
 * @returns
 */
export const getFilterTypes = (field: AxisField) => {
  switch (field.dataType) {
    case 'date':
    case 'date_1':
    case 'date_2':
    case 'datetime_1':
      return field.dataTransformation &&
        field.dataTransformation.transformationConfig &&
        field.dataTransformation.transformationConfig.dateType
        ? [FilterType.Normal, FilterType.Query]
        : [FilterType.Normal, FilterType.Range, FilterType.Scope];
    case 'time_1':
      return field.dataTransformation &&
        field.dataTransformation.transformationConfig &&
        field.dataTransformation.transformationConfig.dateType
        ? [FilterType.Normal, FilterType.Query]
        : [FilterType.Normal, FilterType.Range];
    case 'integer':
    case 'int':
    case 'number':
    case 'double':
      return [FilterType.Normal, FilterType.Range];
    case 'string':
    case 'area_province':
    case 'area_city':
    case 'area_district':
    case 'area_root':
      return [FilterType.Normal, FilterType.Query];
  }
};

export class Limit {
  startRow = 1;
  rowsLimit?: number = undefined;
  constructor(startRow?: number, rowsLimit?: number) {
    isNumber(startRow) && (this.startRow = startRow);
    isNumber(rowsLimit) && (this.rowsLimit = rowsLimit);
  }
}
