// 样式变量
$component-height: 24px;
$label-font-size: 10px;
$label-color: #666;
$title-font-weight: 500;
$icon-color: text-gray-900;
$border-radius: 4px;
$popup-border-radius: 8px;
$primary-margin: 12px;
$secondary-margin: 8px;
$field-gap: 8px;
$primary-font-size: 12px;
$icon-font-size: 16px;
$input-bg: rgb(246 247 250);
$input-hover-color: rgb(230 231 233);
$input-active-bg: #fff;
$input-active-border: rgb(156 192 254);
$tab-active-bg: rgb(237 243 255);
$select-active-bg: rgb(237 243 255 / 100%);
$select-active-color: rgb(58 123 255 / 100%);
$box-shadow: 0 0 6px 0 rgb(233 233 235 / 100%);
$color-silder-width: 12px;

// 公共mixins
@mixin component-base {
  height: $component-height;
  min-height: $component-height;
  line-height: $component-height !important;
}

@mixin form-field-label {
  margin-bottom: 4px;
  color: $label-color;
  font-size: $label-font-size;
}

@mixin form-field-content {
  @apply flex items-center;

  gap: $field-gap;
}

@mixin form-field-vertical {
  display: flex;
  flex: 0 0 100%;
  align-items: center;
}

// 轮廓样式
@mixin form-field-outline {
  &:hover {
    outline: 1px solid $input-hover-color;
  }

  &:focus-within {
    outline: 1px solid $input-active-border;
  }
}

// 公共样式
%no-spin {
  input[type='number'] {
    appearance: textfield;

    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      appearance: none;
      margin: 0;
    }
  }
}

%vis-btn-active {
  .q-btn.vis-btn-active {
    background-color: $input-active-bg;
    box-shadow: inset 0 0 0 2px $input-bg !important;
    color: #000;

    .vis-icon {
      color: #000;
    }
  }
}

%primary-icon {

  // .q-icon,
  .vis-icon {
    color: $label-color;
    font-size: $icon-font-size;
  }
}

%field-base {
  @include component-base;
  @apply py-0;

  font-size: $primary-font-size;
}

// 全局样式
.no-spin {
  @extend %no-spin;
}

.no-outline {
  outline: none !important;

  &:hover,
  &:focus-within {
    outline: none !important;
  }
}

body.dragging,
body.dragging .drag-handle {
  cursor: ew-resize !important;
}

.q-focus-helper {
  display: none;
}

// 通用字段样式
.q-field--dense .q-field__control,
.q-field--dense .q-field__marginal,
.q-field__control-container,
.q-field__control,
.q-field--auto-height.q-field--dense .q-field__control,
.q-field--auto-height.q-field--dense .q-field__native,
.q-field__native,
.q-field__input,
.q-field__marginal {
  @extend %field-base;
}

.q-textarea .q-field__control-container {
  height: auto;
}

.q-btn {
  @apply p-0;
  @include component-base;

  font-size: $primary-font-size;

  &,
  &::before,
  &::after,
  &:hover,
  &:focus,
  &:active,
  &--active {
    box-shadow: none !important;
  }

  &:hover {
    background-color: $input-bg;
  }
}

.q-checkbox {
  @include component-base;

  .q-checkbox__inner {
    @apply w-6 min-w-6 h-6;

    &::before {
      border-radius: $border-radius !important;
    }

    .q-checkbox__bg {
      border-width: 1px;

      &:hover {
        box-shadow: none;
      }
    }
  }

  .q-checkbox__label {
    margin-left: 2px;
    font-size: $primary-font-size;
  }
}

.#{$vis-prefix}-btn-hover-effect {
  &:hover {
    box-shadow: -4px 0 0 $input-bg, 4px 0 0 $input-bg !important;
  }
}

.#{$vis-prefix}-field--mini {
  font-size: $primary-font-size;
}

.#{$vis-prefix}-number {
  @extend %no-spin;

  .q-field__control .q-field__prepend {
    cursor: ew-resize;
    user-select: none;
  }

  &.no-drag {
    .q-field__control .q-field__prepend {
      cursor: default;
    }
  }
}

.#{$vis-prefix}-numbers,
.#{$vis-prefix}-select--bg,
.#{$vis-prefix}-button-group,
.#{$vis-prefix}-number,
.#{$vis-prefix}-input {
  background-color: $input-bg;
}

.#{$vis-prefix}-numbers,
.#{$vis-prefix}-select,
.#{$vis-prefix}-number,
.#{$vis-prefix}-input {
  @include form-field-outline;
}

.#{$vis-prefix}-btn {
  outline: 1px solid $input-hover-color !important;

  &:hover {
    outline: 1px solid $input-hover-color !important;
  }

  &:focus-within {
    outline: 1px solid $input-active-border !important;
  }
}

.#{$vis-prefix}-select {
  outline: 1px solid $input-hover-color;

  &--bg {
    outline: none;
  }
}

// 下拉框和菜单样式
.#{$vis-prefix}-select-popup,
.#{$vis-prefix}-menu {
  border-radius: $popup-border-radius;
  font-size: $icon-font-size !important;

  .q-tree,
  .q-list {
    @apply px-3 py-2;
  }

  .q-tree__children {
    padding-left: 8px;
  }

  .q-tree__node-header,
  .q-item {
    @apply my-1 px-2 py-0 min-h-6 lh-6;

    border-radius: $border-radius;
    font-size: $primary-font-size;

    [class*='hticon-'],
    .q-icon {
      font-size: $icon-font-size;
    }

    .vis-icon--active {
      color: $select-active-color;
    }

    &--active {
      background: $select-active-bg;
      color: $select-active-color;

      &::after {
        // content: '\e613';
        // font-family: 'hticon-vis';
        content: 'check';
        margin-left: $secondary-margin;
        font-family: 'Material Icons';
        font-size: $primary-font-size;
        font-weight: normal;
      }
    }

    &:not(&--active):hover {
      background-color: $input-hover-color;
    }

    &__section {
      &--avatar {
        min-width: 14px;
      }
    }
  }
}

.#{$vis-prefix}-select-popup {
  @apply px-3 py-2;

  max-height: var(--popup-max-height, 'unset');
  box-shadow: $box-shadow;
}

// 标签页样式
.#{$vis-prefix}-tabs {
  @apply text-gray-700;
  @extend %primary-icon;

  .q-tabs__content {
    gap: 4px;
  }

  .q-tab {
    @include component-base;
    @apply p-0 m-0;

    border-radius: $border-radius;

    &__label {
      font-size: $primary-font-size;
    }

    &:hover {
      background-color: $input-bg;
    }

    &--active {
      background-color: $tab-active-bg;
      color: $primary;

      .q-icon {
        color: $primary;
      }
    }
  }
}

// 设计区域样式
.#{$vis-design-prefix} {
  @extend %vis-btn-active;

  .q-field__native,
  .q-field__input {
    @apply py-0;
  }

  .q-select {
    @apply min-w-0 w-full;

    .q-field__input {
      @apply min-w-0 w-full;
    }

    input {
      width: 100% !important;
      min-width: 0 !important;
      max-width: 100% !important;
    }
  }

  .q-separator {
    background-color: rgb(229 229 233) !important;
  }

  .original-color {
    color: initial !important;
  }

  &-center {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #efefef;

    .canvas-warp {
      position: absolute;
      z-index: 99999999;
      top: 20px;
      left: 20px;
      width: calc(100% - 20px);
      height: calc(100% - 20px);

      .sortable-ghost {
        display: none;
      }
    }

    .empty {
      @apply absolute left-50% -translate-x-2/4 bottom-96px;

      &-text {
        @apply text-center text-gray-700;

        div {
          @apply text-xs text-gray-400 my-2;
        }
      }

      &-card {
        @apply flex items-center justify-center;

        .q-card {
          @apply flex items-center mx-1 my-4 py-4 px-3 rounded-md cursor-pointer;

          background: rgba($dark, 0.05);

          .q-icon {
            @apply text-3xl text-gray-600;
          }

          .card-text {
            @apply pt-3 pb-2;
          }

          &__section {
            @apply text-xs;
          }

          &:hover {
            background: rgba($dark, 0.1);

            .card-text {
              @apply text-primary;

              .q-icon {
                @apply text-primary;
              }
            }
          }
        }
      }
    }
  }
}

// 表单样式
.#{$vis-prefix}-form-inline {
  @apply flex flex-nowrap;

  align-items: flex-end;
  margin-bottom: $field-gap;

  .q-separator {
    background-color: rgb(255 255 255 / 100%) !important;
  }

  .#{$vis-prefix}-form-inline {
    margin-bottom: 0;
  }

  .btn-field {
    margin-left: $field-gap !important;
  }

  &__content {
    @include component-base;

    width: 100%;

    &--transparent {
      outline: 1px solid $input-hover-color !important;
      box-shadow: none !important;
    }

    &--minus-0 {
      @include form-field-content;

      align-items: flex-end;
      width: 100%;
    }

    &--minus-28 {
      @include form-field-content;

      align-items: flex-end;
      width: calc(100% - 28px);
    }

    &--minus-32 {
      @include form-field-content;

      align-items: flex-end;
      width: calc(100% - 32px);
    }

    &--minus-60 {
      @include form-field-content;

      align-items: flex-end;
      width: calc(100% - 60px);
    }
  }

  .#{$vis-prefix}-form-field {
    @apply w-full;

    flex-shrink: 1;
    align-items: flex-end;
    min-width: 0;
    margin: 0;

    &__label {
      @include form-field-label;
    }

    &__content {
      @include form-field-content;

      .q-btn:hover {
        background-color: $input-hover-color;
      }
    }

    &--narrow {
      width: calc((100% - 54px));
    }

    &--width-50 {
      width: 50%;
    }

    &--width-42 {
      width: 42%;
    }

    &.btn-group-field {
      width: 50px;
      border-radius: $border-radius;
      background-color: $input-bg;

      .img-icon {
        width: 20px;
        height: 12px;
      }
    }
  }
}

// 配置卡片和弹出层共用样式
%config-popup-base {
  @extend %primary-icon;

  .q-btn {
    @include component-base;
    @apply p-0 m-0 min-h-0 w-6;

    color: $icon-color;

    &:hover {
      background-color: $input-hover-color;
    }

    &.active {
      background-color: $tab-active-bg;
      color: $primary;

      .q-icon {
        color: $primary;
      }
    }
  }
}

// 配置卡片样式
.#{$vis-prefix}-config {
  &-card {
    @extend %config-popup-base;
    @apply flex flex-col;

    .q-btn {
      &:hover {
        background-color: $input-bg;
        box-shadow: none !important;
      }
    }

    &__header {
      @include component-base;
      @apply flex justify-between;

      margin: $secondary-margin $primary-margin;
      font-size: $primary-font-size;
      font-weight: $title-font-weight;
    }

    &__title {
      font-weight: normal;
    }

    &__body {
      padding: 0 $primary-margin;

      .q-separator {
        margin: 0 (-1 * $primary-margin) 0;
      }

      .q-btn {
        width: 24px;
        min-width: 24px;
      }

      .auto-layout {
        gap: 2px;
        justify-content: space-between;

        .img-icon {
          width: 16px;
          height: 16px;
        }
      }

      .delete-btn {
        @apply position-absolute top-0 right-0;
      }
    }
  }

  &-data {
    &__match {
      @extend %config-popup-base;

      .vis-icon--active {
        color: $select-active-color;
      }
    }
  }
}

// 弹出层样式
.#{$vis-prefix}-popup {
  @extend %config-popup-base;
  @extend %vis-btn-active;

  border-radius: $popup-border-radius;
  box-shadow: $box-shadow;

  .q-toolbar {
    @apply px-3;
  }

  .q-btn {
    &:hover {
      background-color: $input-bg;
      box-shadow: none !important;
    }
  }

  &__title {
    @apply min-h-0 h-10;

    font-size: $primary-font-size;
    font-weight: $title-font-weight;
  }

  &__content {
    @apply p-3;
  }

  .#{$vis-prefix}-form-inline {
    @apply flex flex-wrap mb-0;

    gap: $field-gap;

    &__content {
      width: 100%;
    }

    .#{$vis-prefix}-form-field {
      @include form-field-vertical;

      &__label {
        width: 80px;
        font-size: $primary-font-size;
      }

      &__content {
        flex: 1;

        .btn-bg {
          border-radius: $border-radius;
          background-color: $input-bg;
        }
      }
    }
  }
}

.vis-design-notify {
  font-size: 12px;
  min-height: 32px;
  margin-bottom: 42px;

  .q-icon {
    font-size: 16px;
    margin-right: 8px;
  }
}