{"name": "hetu-framework", "description": "太极河图", "version": "2026.1.0", "private": true, "root": true, "scripts": {"serve": "vite", "serve:app": "node scripts/service.js --command serve --target", "build": "pnpm type-check & vite build", "build:app": "node scripts/service.js --command build --target", "build:app:report": "node scripts/service.js --command build --analyze --target", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:stylelint": "stylelint \"{applications,packages}/**/*.{vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "type-check": "vue-tsc --noEmit -p tsconfig.json --composite false", "format": "prettier --write", "commit": "git-cz", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "preinstall": "node ./scripts/preinstall.js", "prepare": "husky install", "test:gzip": "npx http-server dist --cors --gzip -c-1", "test:br": "npx http-server dist --cors --brotli -c-1"}, "dependencies": {"@hetu/acl": "workspace:*", "@hetu/auth": "workspace:*", "@hetu/core": "workspace:*", "@hetu/http": "workspace:*", "@hetu/platform-app": "workspace:*", "@hetu/platform-boot": "workspace:*", "@hetu/platform-cooperation": "workspace:*", "@hetu/platform-material": "workspace:*", "@hetu/platform-shared": "workspace:*", "@hetu/theme": "workspace:*", "@hetu/util": "workspace:*", "@quasar/extras": "^1.16.6", "@quasar/quasar-ui-qiconpicker": "^2.0.7", "@vueuse/core": "^10.4.1", "lodash-es": "^4.17.21", "quasar": "^2.14.0", "vue": "^3.4.5", "vue-router": "^4.2.5", "vue-types": "^5.1.1"}, "devDependencies": {"@commitlint/cli": "~17.3.0", "@commitlint/config-conventional": "~17.3.0", "@hetu/cli": "workspace:*", "@hetu/eslint-config": "workspace:*", "@hetu/stylelint-config": "workspace:*", "@hetu/tsconfig": "workspace:*", "@hetu/vite-plugins": "workspace:*", "@quasar/app-vite": "^2.0.0-alpha.0", "@quasar/vite-plugin": "^1.5.0", "@rushstack/eslint-patch": "^1.4.0", "@types/lodash-es": "~4.17.9", "@types/minimist": "^1.2.2", "@types/node": "^18.17.15", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-vue": "^5.0.0", "chalk": "^4.1.2", "commitizen": "^4.3.0", "conventional-changelog-cli": "^2.2.2", "cross-env": "^7.0.3", "enquirer": "^2.4.1", "execa": "^4.1.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "minimist": "^1.2.8", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "rollup-plugin-visualizer": "^5.9.3", "sass": "^1.33.0", "stylelint": "^15.10.3", "typescript": "~5.0.4", "unocss": "^0.52.7", "vite": "^4.4.9", "vite-plugin-chunk-split": "^0.4.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^2.9.8", "vite-plugin-virtual-mpa": "^1.9.1", "vue-tsc": "^1.8.27"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "@hetu/cli/commitizen.js"}}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"subject-case": [0, "always", "upper-case"]}}, "packageManager": "pnpm@7.0.0", "engines": {"node": ">=18", "pnpm": ">=7"}}