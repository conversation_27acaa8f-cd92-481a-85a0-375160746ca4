<template>
  <div class="vis-config-style" v-if="activeGraph || activeBlock">
    <div class="vis-config-style-header">
      <div class="vis-config-style-title">
        <span v-if="activeGraph && activeGraph.type !== GraphType.Frame">{{ activeTitle }}</span>
        <q-btn v-else class="vis-btn-hover-effect" :label="activeTitle" size="12px" :ripple="false" flat dense>
          <q-icon right size="12px" name="expand_more" />
          <q-menu dense style="width: 250px" class="vis-menu !max-h-none">
            <q-scroll-area style="height: 500px">
              <q-list dense>
                <template v-for="(group, key, index) in prepareSize" :key="index">
                  <q-item-label header class="!text-xs !p-2 font-bold">{{ group.text }}</q-item-label>
                  <q-item v-for="(size, i) in group.size" :key="i" @click="onFrameSize(size)" v-close-popup clickable>
                    <q-item-section>{{ size.text }}</q-item-section>
                    <q-item-section side>{{ size.width }} × {{ size.height }}</q-item-section>
                  </q-item>
                  <q-separator v-if="index < Object.keys(prepareSize).length - 1" class="!my-1" />
                </template>
              </q-list>
            </q-scroll-area>
          </q-menu>
        </q-btn>
      </div>

      <q-tabs
        v-model="tab"
        dense
        active-color="primary"
        indicator-color="transparent"
        align="left"
        narrow-indicator
        class="vis-config-style-tabs vis-tabs"
      >
        <q-tab :ripple="false" flat dense name="property">
          <ht-icon class="vis-icon" name="vis-property" />
          <q-tooltip>常规属性</q-tooltip>
        </q-tab>
        <q-tab v-if="activeGraph && activeGraph.type !== GraphType.Frame" :ripple="false" flat dense name="data">
          <ht-icon class="vis-icon" name="vis-data" />
          <q-tooltip>数据属性</q-tooltip>
        </q-tab>
        <q-tab :ripple="false" flat dense name="option">
          <ht-icon class="vis-icon" name="vis-icon-style" />
          <q-tooltip>组件属性</q-tooltip>
        </q-tab>
        <q-tab v-if="activeGraph && activeGraph.type !== GraphType.Frame" :ripple="false" flat dense name="style">
          <ht-icon class="vis-icon" name="hticon-vis-switch-chart" />
        </q-tab>
      </q-tabs>
    </div>
    <q-scroll-area
      class="vis-config-style-scroll"
      ref="scrollAreaRef"
      @scroll="handleScroll"
      :style="{ '--q-scrollarea-content-padding-bottom': isBottom ? '300px' : '0px' }"
    >
      <vis-config-property v-if="tab === 'property'" />
      <vis-config-option v-if="tab === 'option'" />
      <vis-config-data v-if="tab === 'data'" />
    </q-scroll-area>
  </div>
</template>
<script lang="ts" src="./style.ts"></script>
<style lang="scss" src="./style.scss" scoped></style>
