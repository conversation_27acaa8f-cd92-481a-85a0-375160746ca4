.#{$vis-prefix}-textbox {
  @keyframes blink {

    0%,
    50% {
      opacity: 1;
    }

    51%,
    100% {
      opacity: 0;
    }
  }

  .q-editor {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    padding: var(--border-width, initial);

    &__content {
      display: inline;
    }
  }

  .caret {
    display: flex;
    position: absolute;
    top: 0;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

    span {
      display: inline-block;
      width: 1px;
      height: 18px;
      animation: blink 1s steps(1) infinite;
      background: black;
      vertical-align: middle;
    }
  }
}