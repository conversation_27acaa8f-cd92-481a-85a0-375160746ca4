.vis-graph {
  .#{$vis-prefix}-input {
    background: none;
    padding: var(--border-width, initial);

    &:focus-within {
      outline: none;
    }

    &__content {
      &.not-editable {
        // 设计器内,不允许编辑
        pointer-events: none;

        .q-input {
          &::after {
            content: '';
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            position: absolute;
            background: transparent;
            z-index: 9;
          }
        }
      }

      .q-field__control {
        height: 100%;
        line-height: unset !important;

        &-container {
          height: 100%;
          line-height: unset !important;
        }

        .q-field__marginal {
          height: 100%;
          font-size: 16px;
        }
      }

      .q-field--with-bottom {
        padding-bottom: 0;
      }

      .q-field__bottom {
        padding-top: 4px;
        font-size: 10px;
      }

    }
  }
}