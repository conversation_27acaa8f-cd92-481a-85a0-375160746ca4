<template>
  <div class="vis-design-ruler">
    <div class="vis-design-ruler-box" @click="onRestore" ref="box" />
    <div class="vis-design-ruler-horizontal">
      <vue-guides
        ref="horizontalGuidesRef"
        type="horizontal"
        :selectedRangesText="true"
        :selectedRangesTextOffset="[0, 10]"
        :selectedRanges="horizontalGuidesSelectedRanges"
        :zoom="zoom"
        :unit="unit"
        :mainLineSize="3"
        longLineSize="0%"
        shortLineSize="0%"
        textAlign="center"
        :textOffset="[0, 10]"
        font="8px sans-serif"
        :displayDragPos="true"
        :defaultScrollPos="defaultScrollPos"
        :defaultGuidesPos="defaultGuidesPos"
        :displayGuidePos="true"
        :rulerStyle="{
          left: '20px',
          width: 'calc(100% - 20px)',
          height: '20px'
        }"
        :backgroundColor="theme.rule.bg"
        :textColor="theme.rule.color"
        :lineColor="theme.rule.color"
        :selectedBackgroundColor="theme.rule.selectedbg"
        :selectedRangesTextColor="theme.rule.selectedColor"
        @changeGuides="onChange"
      />
    </div>
    <div class="vis-design-ruler-vertical">
      <vue-guides
        ref="verticalGuidesRef"
        type="vertical"
        :selectedRangesText="true"
        :selectedRangesTextOffset="[10, 0]"
        :selectedRanges="verticalGuidesSelectedRanges"
        :zoom="zoom"
        :unit="unit"
        :mainLineSize="3"
        longLineSize="0%"
        shortLineSize="0%"
        textAlign="center"
        :textOffset="[10, 0]"
        font="8px sans-serif"
        :displayDragPos="true"
        :defaultScrollPos="defaultGuidesPos"
        :defaultGuidesPos="defaultScrollPos"
        :displayGuidePos="true"
        :rulerStyle="{
          top: '20px',
          height: 'calc(100% - 20px)',
          width: '20px'
        }"
        :backgroundColor="theme.rule.bg"
        :textColor="theme.rule.color"
        :lineColor="theme.rule.color"
        :selectedBackgroundColor="theme.rule.selectedbg"
        :selectedRangesTextColor="theme.rule.selectedColor"
        @changeGuides="onChange"
      />
    </div>
  </div>
</template>
<script lang="ts" src="./ruler.ts"></script>
<style lang="scss" src="./ruler.scss"></style>
