import type { TokenStore, TokenModel } from '.';

/**
 * `localStorage` storage
 */
export class LocalStorageStore implements TokenStore {
  get(key: string) {
    return JSON.parse(localStorage.getItem(key) || '{}') || {};
  }

  set(key: string, value: TokenModel): boolean {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  }

  remove(key: string) {
    localStorage.removeItem(key);
  }
}
