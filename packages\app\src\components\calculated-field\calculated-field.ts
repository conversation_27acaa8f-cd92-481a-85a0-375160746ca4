import { computed, defineComponent, nextTick, ref, watch } from 'vue';
import { QEditor, QInput, debounce } from 'quasar';
import { FieldTypeService } from '../../services';
import { type DatasetFieldType } from '../../models';
interface Field {
  fieldAlias: string;
  fieldDatatype: string;
  fieldOriginal: string;
}

export default defineComponent({
  name: 'ht-app-calculated-field',
  setup() {
    const visible = ref(false);

    //#region 计算字段名称
    const fieldNameRef = ref<QInput>();
    const calculatedFieldName = ref('');
    const nameRules = [
      (val: any) => !!val || '必填项',
      /** 只允许中英文、数字下划线 */
      (val: any) => /^[a-zA-Z0-9\u4e00-\u9fa5_]{1,30}$/.test(val) || '只允许中英文、数字下划线'
    ];

    //#endregion

    //#region 左侧字段列表

    const filter = ref('');
    const filterRef = ref<QInput>();
    const fieldList = ref<Field[]>([]);

    const fieldTree = computed(() => {
      const treeMap = fieldList.value.reduce((map, i) => {
        const { fieldDatatype } = i;
        let parent = map[fieldDatatype];
        if (!parent) {
          map[fieldDatatype] = parent = {
            fieldDatatype,
            fieldOriginal: fieldDatatype,
            children: []
          };
        }
        parent.children.push(i);
        return map;
      }, {} as Record<string, { fieldDatatype: string; fieldOriginal: string; children: Field[] }>);
      return Object.values(treeMap);
    });

    const fieldTypeMap = ref<Record<string, DatasetFieldType>>({
      dim: { fieldName: '维度', fieldDatatype: 'dim', fieldType: 'dim' },
      measure: { fieldName: '度量', fieldDatatype: 'measure', fieldType: 'dim' }
    });

    const setFiledTypeMap = (fieldType: DatasetFieldType) => {
      fieldTypeMap.value[fieldType.fieldDatatype] = fieldType;
    };

    const getFiledType = async () => {
      const res = await FieldTypeService.getFieldTypes();
      res?.data?.forEach((fieldType) => {
        setFiledTypeMap(fieldType);
        fieldType.childList?.forEach(setFiledTypeMap);
      });
    };

    getFiledType();
    //#endregion

    //#region 编辑器输入监听

    const formatReg = /\[([^\]\[\<]+)\]/g;

    const editorChange = (value: string) => {
      Array.from(value.matchAll(formatReg)).forEach((i) => {
        // 对 [] 内的内容进行判断，
        // 如果是字段列表的fieldAlias 或fieldOriginal 则替换为带span格式
        const field = fieldMap.value[i[1]];
        if (field) {
          editor.value = value.replace(i[0], createFieldEl(field));
          nextTick(() => {
            const selection = window.getSelection();
            const focusNode = selection?.focusNode;
            const focusParentNode = focusNode?.parentNode;
            // 如果光标在不可编辑区域内，则调整光标在span后
            if (isUneditableNode(focusParentNode)) {
              setCaretByNode(focusParentNode, true);
            } else if (selection && selection.focusOffset > 0) {
              const range = document.createRange();
              const idx = selection.focusOffset - 1;
              range.setStart(focusNode!, idx);
              range.setEnd(focusNode!, idx);
              selection.removeAllRanges();
              selection.addRange(range);
            }
          });
        }
      });
      debounceSaveStatus();
    };
    // 字段的fieldAlias 和fieldOriginal 均为key
    const fieldMap = computed(() => {
      return fieldList.value.reduce((obj, field) => {
        obj[field.fieldAlias] = field;
        obj[field.fieldOriginal] = field;
        if (field.fieldOriginal.includes('.')) {
          const name = field.fieldOriginal.split('.')[1];
          name && (obj[name] = field);
        }
        return obj;
      }, {} as Record<string, Field>);
    });
    //#endregion

    //#region 拖拽
    const onDrag = (event: DragEvent, field: Field) => {
      event.dataTransfer?.setData('text/plain', JSON.stringify(field));
    };

    const drop = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      const data = e.dataTransfer?.getData('text/plain');
      if (!data) return;
      const selection = window.getSelection();
      const focusNode = selection?.focusNode;
      const contentEl = editorRef.value?.getContentEl();
      // 如果光标在不可编辑区域内，则根据焦点的偏左偏右设置插入在span前或后
      if (!focusNode || isUneditableNode(focusNode.parentNode)) {
        setCaretByNode(focusNode?.parentNode as Node, true);
      }
      // 如果点击的是只有一个字符的可编辑文本，且点击位置是文本开头，则插入一个空格，否则插入内容会将该字符覆盖
      if (
        focusNode?.nodeType === Node.TEXT_NODE &&
        selection?.focusOffset === 0 &&
        focusNode.textContent?.length === 1 &&
        !isUneditableNode(focusNode.parentNode)
      ) {
        editorRef.value?.runCmd('insertText', ' ');
      }
      // 谷歌edge等浏览器中，在文本中间连续拖拽两个不可编辑时，第二次拖拽会将后边的文本删除
      // 此处记录文本内容，后续恢复
      let textNode: Node | null = null;
      let text: string | null = '';
      if (
        selection?.focusNode === contentEl &&
        isUneditableNode(contentEl?.childNodes[selection!.focusOffset - 1]) &&
        contentEl?.childNodes[selection!.focusOffset]?.nodeType === Node.TEXT_NODE
      ) {
        textNode = contentEl?.childNodes[selection!.focusOffset];
        text = textNode.textContent;
      }

      try {
        editorRef.value?.runCmd('insertHTML', createFieldEl(JSON.parse(data) as Field));

        removeExcessiveNode();
        // 插入后默认光标在不可编辑span中，将其设置为span后
        resetCaret();

        // 若文本节点被删除，则插回
        if (text && !textNode?.parentNode) {
          window.getSelection()?.getRangeAt(0).insertNode(document.createTextNode(text));
          selection?.collapseToStart();
        }
        debounceSaveStatus();
        if (isFirefox) {
          // 火狐浏览器下，在有不可编辑内容的区域中设置选区会导致光标消失，且鼠标点击区域内依旧不会出现光标
          // 只有鼠标点击编辑区域外再点回编辑区域会出现光标
          // 所以拖拽完成后聚焦到外部
          filterRef.value?.focus();
          filterRef.value?.blur();
        }
      } catch (e) {
        console.error('插入异常 :>> ', e);
      }
    };

    //#endregion

    //#region 编辑器
    const editorRef = ref<QEditor>();
    const editor = ref('');
    /** 编辑器错误提示 */
    const editorError = ref('');
    watch(editor, (val) => {
      if (val.length > 0) {
        editorError.value = '';
      }
    });

    /** 编辑器校验 */
    const checkEditor = () => {
      if (editor.value.length === 0) {
        editorError.value = '请输入计算字段内容';
        return false;
      }
      return true;
    };

    /** 节点是否为不可编辑节点 */
    const isUneditableNode = (node: Node | HTMLElement | null | undefined) => {
      if (!node) return false;
      return (node as HTMLElement).contentEditable === 'false';
    };

    /** 根据字段信息创建不可编辑节点 */
    const createFieldEl = (field: Field) => {
      return `<span class="text-primary q-px-xs" contenteditable="false" data-field="${field.fieldOriginal}">${field.fieldAlias}</span>`;
    };

    /** 清除多余的节点 */
    const removeExcessiveNode = () => {
      // 1. 在可编辑text节点内插入不可编辑span节点会导致不可编辑span节点后意外插入一个同样式的span标签
      // 将其删除
      // 2. 较低版本的火狐会在拖拽后生成br，将其删除
      editorRef.value?.getContentEl().childNodes.forEach((i) => {
        if ((i.nodeName === 'SPAN' && (i as HTMLElement).contentEditable !== 'false') || i.nodeName === 'BR') {
          i.remove();
        }
      });
    };
    /** 仅文本粘贴，去除样式 */
    const onPaste = (evt: any) => {
      if (evt.target?.nodeName === 'INPUT') return;
      let text, onPasteStripFormattingIEPaste;
      evt.preventDefault();
      evt.stopPropagation();
      if (evt.originalEvent && evt.originalEvent.clipboardData.getData) {
        text = evt.originalEvent.clipboardData.getData('text/plain');
        editorRef.value?.runCmd('insertText', text);
      } else if (evt.clipboardData && evt.clipboardData.getData) {
        text = evt.clipboardData.getData('text/plain');
        editorRef.value?.runCmd('insertText', text);
      } else if ((window as any).clipboardData && (window as any).clipboardData.getData) {
        if (!onPasteStripFormattingIEPaste) {
          onPasteStripFormattingIEPaste = true;
          editorRef.value?.runCmd('ms-pasteTextOnly', text);
        }
        onPasteStripFormattingIEPaste = false;
      }
    };
    /** 是否为火狐 */
    const isFirefox = navigator.userAgent?.indexOf('Firefox') >= 0;

    //#endregion

    //#region 光标
    const caret = computed(() => editorRef.value?.caret);
    const saveCaret = () => {
      if (!caret.value) return;
      editorRef.value?.caret.save(getSelection()?.getRangeAt(0) as Range);
    };
    /** 编辑器点击，设置光标 */
    const editorClick = (e: Event) => {
      const target = e.target as HTMLElement;
      const focusNode = window.getSelection()?.focusNode;
      if (focusNode?.parentNode !== target) return;
      if (isUneditableNode(target)) {
        setCaretByNode(target, true);
      } else if (isUneditableNode(window.getSelection()?.focusNode?.parentNode)) {
        setCaretByNode(window.getSelection()?.focusNode?.parentNode, true);
      }
    };

    /** 设置光标到编辑器内容的末尾 */
    const setCaretToEditorEnd = () => {
      nextTick(() => {
        const range = document.createRange();
        const selection = window.getSelection();
        // 将光标移动到内容的末尾
        range.selectNodeContents(editorRef.value?.getContentEl() as Node);
        range.collapse(false);
        selection?.removeAllRanges();
        selection?.addRange(range);
        saveStatus();
      });
    };
    /** 根据节点设置光标，默认设置到节点后。设置autoDirection则根据点击位置设置到节点前或后 */
    const setCaretByNode = (node: Node | null | undefined, autoDirection = false) => {
      if (!node) return;
      const range = document.createRange();
      const selection = window.getSelection();
      if (
        autoDirection &&
        isUneditableNode(node) &&
        selection?.focusNode?.nodeType === Node.TEXT_NODE &&
        selection.focusNode.textContent &&
        selection?.focusOffset <= selection.focusNode.textContent.length / 2
      ) {
        // 如果点击的是字段，且点击的前半段，则设置光标到字段前方
        if (isFirefox) {
          // 火狐浏览器将光标设置到不可编辑前时光标会消失，所以将光标设置到前一个文本节点的最后
          let preNode = node.previousSibling;
          if (!preNode || isUneditableNode(preNode)) {
            // 最前方无文本或前一个是不可编辑，插入零宽字符
            preNode = document.createTextNode('\u200b');
            editorRef.value?.getContentEl().insertBefore(preNode, node);
            // 插入到零宽字符之前;
            range.setStartBefore(preNode);
            range.setEndBefore(preNode);
          } else {
            range.setStart(preNode, preNode.textContent?.length || 0);
            range.setEnd(preNode, preNode.textContent?.length || 0);
          }
        } else {
          // 非火狐
          range.setStartBefore(node);
          range.setEndBefore(node);
        }
      } else {
        // 默认设置到元素后方
        if (isFirefox && isUneditableNode(node) && (!node.nextSibling || isUneditableNode(node.nextSibling))) {
          // 火狐且是最后一个节点或者后边是不可编辑或则插入零宽
          const tNode = document.createTextNode('\u200b');
          editorRef.value?.getContentEl().insertBefore(tNode, node.nextSibling);
        }
        range.setStartAfter(node);
        range.setEndAfter(node);
      }
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    };

    /** 若光标在不可编辑节点内则重置光标到节点后 */
    const resetCaret = () => {
      const focus = window.getSelection()?.focusNode;
      if (isUneditableNode(focus)) {
        setCaretByNode(focus);
      } else if (isUneditableNode(focus?.parentElement)) {
        setCaretByNode(focus?.parentNode);
      }
      saveCaret();
    };
    //#endregion

    //#region 确定及弹窗
    const onConfirm = async () => {
      if (!fieldNameRef.value?.validate() || !checkEditor()) return;
      const contentStr = getEditContent(editorRef.value?.getContentEl() as HTMLElement);
      const data = { name: calculatedFieldName.value, value: contentStr, tokens: editor.value };
      if (confirmCb && (await confirmCb(data)) === false) return;
      resResolve(data);
      visible.value = false;
      clearHistory();
    };
    const getEditContent = (el: HTMLElement) => {
      let str = '';
      el.childNodes.forEach((i) => {
        if (i.nodeType === Node.TEXT_NODE) {
          let text = i.textContent;
          // 替换 ASCII码值为160 的不间断空格non-breaking space 对应浏览器里的是&nbsp  为正常空格
          text = i.textContent?.replace(/\u00A0/g, ' ') || '';
          // 清除零宽字符
          text = i.textContent?.replace(/\u200b/g, '') || '';
          str += text;
        } else if (isUneditableNode(i)) {
          const field = (i as HTMLElement).dataset?.field;
          field && (str += field);
        } else if (i.childNodes.length > 0) {
          str += getEditContent(i as HTMLElement);
        }
      });
      // 只清除前后空格
      return str.replace(/(^\s)|(\s$)/g, '');
    };

    /** 更新展示的字段别名 */
    const resetFieldNameByFieldList = (el: HTMLElement) => {
      el.childNodes.forEach((i) => {
        if (isUneditableNode(i)) {
          const fieldOriginal = (i as HTMLElement).dataset?.field;
          if (!fieldOriginal) return;
          const field = fieldMap.value[fieldOriginal];
          if (field && i.textContent !== field.fieldAlias) {
            i.textContent = field.fieldAlias;
          }
        } else if (i.childNodes.length > 0) {
          resetFieldNameByFieldList(i as HTMLElement);
        }
      });
    };

    let resResolve: Function;
    let confirmCb: Function | undefined = undefined;
    /** 编辑计算字段 */
    const handleEdit = (
      list: Field[],
      computeField?: { name: string; value: string; tokens: string },
      cb?: Function
    ) => {
      clearHistory();
      editorError.value = '';
      fieldList.value = list || [];
      calculatedFieldName.value = computeField?.name || '';
      editor.value = computeField?.tokens || '';
      confirmCb = cb;
      visible.value = true;
      setTimeout(() => {
        resetFieldNameByFieldList(editorRef.value?.getContentEl() as HTMLElement);
        removeExcessiveNode();
        setCaretToEditorEnd();
        saveCaret();
      }, 100);
      return new Promise((resolve) => {
        resResolve = resolve;
      });
    };

    const close = () => {
      visible.value = false;
      resResolve?.(false);
      clearHistory();
    };
    //#endregion

    //#region 历史记录
    interface History {
      editorValue: string;
      rangeNodeIndex: number;
      rangeOffset?: number;
    }

    const historyList: History[] = [];
    const redoList: History[] = [];
    const clearHistory = () => {
      historyList.length = 0;
      redoList.length = 0;
    };

    const saveStatus = () => {
      const contentEl = editorRef.value?.getContentEl()!;
      const range = window.getSelection()?.getRangeAt(0)!;
      let rangeNodeIndex, rangeOffset;
      if (range.startContainer === contentEl) {
        rangeNodeIndex = range.startOffset;
      } else {
        rangeNodeIndex = Array.from(contentEl.childNodes!).findIndex((i) => i === range.startContainer);
        rangeOffset = range.startOffset;
      }
      historyList.push({ editorValue: contentEl.innerHTML, rangeNodeIndex, rangeOffset });
      redoList.length = 0;
    };

    const debounceSaveStatus = debounce(saveStatus, 10);

    const restoreHistory = (history: History) => {
      const { editorValue, rangeNodeIndex, rangeOffset } = history;
      editor.value = editorValue;
      nextTick(() => {
        const contentEl = editorRef.value!.getContentEl();
        const focusNode = rangeOffset ? contentEl.childNodes[rangeNodeIndex] : contentEl;
        const offset = rangeOffset || rangeNodeIndex;
        if (!focusNode) return;
        const selection = window.getSelection();
        const range = document.createRange();
        range.setStart(focusNode, offset);
        range.setEnd(focusNode, offset);
        selection?.removeAllRanges();
        selection?.addRange(range);
      });
    };

    const undo = () => {
      if (historyList.length < 2) return;
      const history = historyList.pop()!;
      restoreHistory(historyList[historyList.length - 1]);
      redoList.push(history);
    };

    const redo = () => {
      if (!redoList.length) return;
      const history = redoList.pop()!;
      restoreHistory(history);
      historyList.push(history);
    };

    /** 苹果设备 */
    const isAppleDevice = /Mac|iPod|iPhone|iPad/.test(navigator.userAgent);

    /** 自定义撤销恢复操作 */
    const onKeydown = (event: KeyboardEvent) => {
      // 苹果系产品，拦截 cmd + z / cmd + shift + z
      // 其他产品，拦截 ctrl + z / ctrl + shift + z
      const ctrlKey = isAppleDevice ? event.metaKey : event.ctrlKey;
      if (!ctrlKey) return;
      if (event.code === 'KeyZ') {
        event.preventDefault();
        undo();
        return;
      }
      if (event.code === 'KeyY') {
        event.preventDefault();
        redo();
        return;
      }
    };
    //#endregion

    return {
      visible,
      fieldNameRef,
      calculatedFieldName,
      nameRules,
      filter,
      filterRef,
      fieldTree,
      fieldTypeMap,
      editorRef,
      editor,
      editorError,
      createFieldEl,
      editorChange,
      onDrag,
      drop,
      onPaste,
      editorClick,
      onConfirm,
      close,
      onKeydown,
      handleEdit
    };
  }
});
