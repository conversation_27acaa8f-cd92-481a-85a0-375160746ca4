import { useFill, type Color, SystemPalette, FillType } from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';

export default defineComponent({
  name: 'vis-fill-color',
  props: {
    modelValue: {
      type: Object as PropType<Color>,
      required: true
    }
  },
  setup(props, { emit }) {
    const { getPaletteColors, getRgbaFromString } = useFill();

    const computedModel = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    /**
     * 颜色值处理，纯色确保为rgba
     * @param color
     * @returns
     */
    const calcColor = (color: Color) => {
      const { r, g, b, a } = color;
      return `rgba(${r},${g},${b},${a})`;
    };

    const colorValue = ref(calcColor(props.modelValue));

    const colorUpdate = (color: string | null) => {
      if (!color) return;

      const { r, g, b, a } = getRgbaFromString(color);

      handleUpdate({ r, g, b, a });
    };

    const handleUpdate = (val: Color) => {
      emit('update:modelValue', val);
    };

    const palette = ref(getPaletteColors(FillType.Solid));

    watch(
      () => props.modelValue,
      (val) => {
        colorValue.value = calcColor(val);
      },
      { deep: true }
    );

    return {
      computedModel,
      colorValue,
      colorUpdate,
      handleUpdate,
      palette,
      SystemPalette
    };
  }
});
