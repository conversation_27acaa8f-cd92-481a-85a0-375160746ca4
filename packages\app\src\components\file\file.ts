import { ACLService, type ACLResourceInstance } from '@hetu/acl';
import type { ResponseResult } from '@hetu/http';
import { QInput, useQuasar } from 'quasar';
import { defineComponent, ref, type PropType, computed, inject } from 'vue';
import type { DirectoryACLResourceCode, FileACLResourceCode, FileInfo } from '../../models';
import { FILE_PAGE_STORE_KEY, useFilePageStore } from '../../stores';
import { CatalogService, CatalogFileService, FavoriteService } from '../../services';

type ACLType = ACLResourceInstance<FileACLResourceCode> | ACLResourceInstance<DirectoryACLResourceCode>;

export default defineComponent({
  name: 'ht-app-file',
  props: {
    acl: Object as PropType<ACLType | ((file: FileInfo) => ACLType)>,
    icon: String,
    iconSize: String,
    iconColor: String,
    multi: Boolean,
    category: {
      type: String,
      required: true
    },
    file: {
      type: Object as PropType<FileInfo & { privilegeId?: string }>,
      required: true
    },
    /** 是否支持星标 */
    favorite: <PERSON>olean,
    /** 是否支持重命名 */
    rename: {
      type: Boolean,
      default: true
    },
    deleteType: {
      type: String as PropType<'recycle' | 'delete'>,
      default: 'recycle'
    },
    showToolBar: {
      type: Boolean,
      default: true
    },
    dense: Boolean
  },
  emits: ['click', 'clickBody', 'clickTitle', 'toggleFavorite', 'rename'],
  setup(props, { emit }) {
    const $q = useQuasar();
    const store = inject(FILE_PAGE_STORE_KEY) as ReturnType<typeof useFilePageStore<FileInfo>>;
    const fileAcl = computed(() => (typeof props.acl === 'function' ? props.acl(props.file) : props.acl));
    const fileBaseAcl = computed(() => {
      const acl = fileAcl.value;
      if (!acl) return {};

      let { edit, saveFavorite, deleteFavorite, toolbar, recycle: del } = acl;
      if (props.deleteType === 'delete') {
        del = acl.delete;
      }

      if (props.file.dataType) {
        edit = acl.editDirectory;
        saveFavorite = acl.saveFavoriteDir;
        deleteFavorite = acl.deleteFavoriteDir;
        toolbar = acl.toolbarDir;
        del = acl.deleteDirectory;
      }

      return { edit, saveFavorite, deleteFavorite, toolbar, del };
    });

    //#region ------------------------ 基础操作 ------------------------
    const { setHover, hoverId } = store;

    const onClick = () => {
      setActive();
      emit('click', props.file, isActive.value);
    };

    const onClickBody = (e: Event) => {
      emit('clickBody', e, props.file, isActive.value);
    };

    const onClickTitle = (e: Event) => emit('clickTitle', e, props.file, isActive.value);

    const toggleTools = (visible: boolean) => {
      hoverId.value = visible ? props.file.id : '';
      setHover(visible ? props.file : undefined);
    };
    //#endregion --------------------- 基础操作 ------------------------

    //#region ------------------------ 选中处理 ------------------------
    const { active } = store;

    /** 是否选中 */
    const isActive = computed(() =>
      Array.isArray(active.value)
        ? active.value.find((f) => f.id === props.file.id)
        : active.value?.id === props.file.id
    );

    const setActive = () => {
      if (!props.multi) {
        active.value = (active.value as FileInfo)?.id === props.file.id ? undefined : props.file;
        return;
      }

      const value = active.value as FileInfo[];
      if (value && value.length) {
        const index = value.findIndex((f) => f.id === props.file.id);
        index === -1 ? value.push(props.file) : value.splice(index, 1);
      } else {
        active.value = [props.file];
      }
    };
    //#endregion --------------------- 选中处理 ------------------------

    //#region ------------------------   星标   ------------------------
    const favoriteing = ref(false);

    /**
     * 添加星标
     */
    const saveFavorite = async () => {
      if (!ACLService.can(fileBaseAcl.value?.saveFavorite)) {
        return;
      }

      const file = props.file;
      favoriteing.value = true;

      try {
        const res = await (file.dataType
          ? FavoriteService.catalogSave(file.id, props.category)
          : FavoriteService.fileSave(file.id, props.category));

        if (res && res.status === 'success') {
          $q.notify({
            position: 'top',
            type: 'positive',
            message: res.message
          });
          file.favorite = 1;
          emit('toggleFavorite', file);
        }
        // eslint-disable-next-line no-empty
      } catch (e) {}

      favoriteing.value = false;
    };

    /** 取消星标 */
    const deleteFavorite = () => {
      if (!ACLService.can(fileBaseAcl.value?.deleteFavorite)) {
        return;
      }

      const file = props.file;
      $q.dialog({
        title: '提示',
        message: `确认取消星标?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(async () => {
        favoriteing.value = true;

        try {
          const res = await (file.dataType
            ? FavoriteService.catalogDelete([file.id], props.category)
            : FavoriteService.fileDelete([file.id], props.category));

          if (res && res.status === 'success') {
            $q.notify({
              position: 'top',
              type: 'positive',
              message: res.message
            });
            file.favorite = 0;

            store.setAction('resetNode');
            emit('toggleFavorite', file);
          }
          // eslint-disable-next-line no-empty
        } catch (e) {}

        favoriteing.value = false;
      });
    };

    /**
     * 切换星标
     * @param file
     */
    const toggleFavorite = () => {
      props.file.favorite ? deleteFavorite() : saveFavorite();
    };
    //#endregion -----------------------   星标   -----------------------

    //#region ------------------------ 修改标题 ------------------------
    const title = ref('');
    const editId = ref('');
    const titleInputRef = ref<QInput | null>(null);

    const editTitle = async () => {
      editId.value = props.file.id;
      title.value = props.file.title;
      setTimeout(() => titleInputRef.value?.select(), 8);
    };

    const updateTitle = () => {
      const file = props.file;
      if (title.value.trim() === '' || file.title === title.value.trim()) {
        editId.value = '';
        return;
      }

      emit('rename', title.value, file, (result: ResponseResult | null) => {
        if (result && result.status === 'success') {
          $q.notify({
            position: 'top',
            type: 'positive',
            message: '修改成功'
          });
          file.title = title.value;
        }

        editId.value = '';
      });
    };
    //#endregion --------------------- 修改标题 ------------------------

    /** 删除文件或文件夹 */
    const delFile = () => {
      $q.dialog({
        title: '提示',
        message: `确定删除 [ ${[props.file.title || '']} ] ?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(async () => {
        const srv = props.file.dataType ? CatalogService : CatalogFileService;
        const result = await (props.deleteType === 'delete'
          ? srv.delete(props.category, [props.file.id])
          : srv.recycle(props.category, [props.file.id]));
        if (result && result.status === 'success') {
          $q.notify({ position: 'top', type: 'positive', message: result.message });
          store.tree.removeNode(props.file.id);

          store.setOpen(store.tree?.getNode(props.file.parentId));
          store.setAction('search');
        }
      });
    };

    return {
      fileAcl,
      fileBaseAcl,

      hoverId,
      setHover,
      onClick,
      onClickBody,
      onClickTitle,
      toggleTools,

      isActive,
      setActive,

      favoriteing,
      toggleFavorite,

      title,
      editId,
      titleInputRef,
      editTitle,
      updateTitle,
      delFile
    };
  }
});
