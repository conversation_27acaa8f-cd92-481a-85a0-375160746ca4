<template>
  <q-dialog class="ht-app-move-space" ref="dialogRef" @hide="onDialogHide" v-close-popup>
    <q-card class="q-dialog-plugin" :style="{ maxWidth: '700px', width: '700px' }">
      <q-card-section class="q-dialog__title ellipsis">
        {{ titleName }}
        <q-tooltip>{{ titleName }} </q-tooltip>
      </q-card-section>
      <div class="q-mx-md">
        <q-card flat bordered class="row">
          <div class="col flex justify-between">
            <q-scroll-area class="w-full" style="height: 398px">
              <div class="col q-pa-sm">
                <q-input outlined class="ht-field ht-field--small q-pb-sm" v-model="search.team" placeholder="搜索空间">
                  <template v-slot:prepend>
                    <q-icon name="search" />
                  </template>
                </q-input>
                <q-list dense>
                  <q-item
                    class="q-mb-sm"
                    active-class="active-team"
                    dense
                    clickable
                    v-ripple
                    v-for="team in teams"
                    :key="team.id"
                    :active="team.domain === moveModel.targetDomain"
                    @click="selectTeam(team.domain)"
                  >
                    <q-item-section avatar>
                      <q-avatar size="28px" color="primary" text-color="white" rounded>
                        <q-img v-if="team && team.avatar" :src="avatar(team.avatar)">
                          <template v-slot:error>
                            <span class="row flex-center full-height">{{ team.name.toUpperCase().charAt(0) }}</span>
                          </template>
                        </q-img>
                        <span v-else>{{ team.name.toUpperCase().charAt(0) }}</span>
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>{{ team.name }}</q-item-section>
                    <q-item-section class="text-12px" side v-if="team.domain === domain">当前空间</q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-scroll-area>
          </div>
          <q-separator :vertical="true" />

          <q-scroll-area class="w-full col" style="height: 398px">
            <div class="col">
              <q-input
                outlined
                class="ht-field ht-field--small q-pa-sm"
                v-model="search.folder"
                placeholder="搜索文件夹"
              >
                <template v-slot:prepend>
                  <q-icon name="search" />
                </template>
              </q-input>
              <q-scroll-area class="q-pa-sm" :style="{ height: '350px' }">
                <q-card-section class="q-py-none">
                  <q-tree
                    class="text-center"
                    :nodes="getTargets"
                    node-key="id"
                    label-key="title"
                    children-key="directorys"
                    :filter="search.folder"
                    default-expand-all
                  >
                    <template v-slot:default-header="{ node }">
                      <div v-if="checkDisable(node)" class="col row items-center q-tree__node--disabled">
                        {{ node.title }}
                      </div>
                      <div
                        v-else
                        class="col row items-center cursor-pointer"
                        :class="node.id === targetId && 'text-primary'"
                        @click="selected(node)"
                      >
                        {{ node.title }}
                      </div>
                    </template></q-tree
                  >
                </q-card-section>
              </q-scroll-area>
            </div>
          </q-scroll-area>
        </q-card>
      </div>
      <q-card-actions class="relative-position q-mx-md q-px-none" align="right">
        <q-checkbox
          dense
          class="absolute-left"
          v-if="domain !== moveModel.targetDomain"
          v-model="moveModel.createCopy"
          :true-value="1"
          :false-value="0"
        >
          <span :class="{ 'text-primary': moveModel.createCopy }">创建并移动副本</span>
        </q-checkbox>
        <q-btn label="取消" flat @click="onDialogCancel" />
        <q-btn label="确定" color="primary" :loading="loading" flat @click="save" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" src="./move-space"></script>
<style lang="scss">
.#{$ht-prefix}-app-move-space {
  .active-team {
    background: rgb(48 141 236 / 10%);
    color: #308dec;
  }

  .q-list--dense {
    & > .q-item {
      padding: 4px 16px;
      border-radius: 4px;
    }
  }

  .q-item__section--avatar {
    min-width: auto;
  }
}
</style>
