/* --------- 覆盖内置变量 ------------ */

/* ------------- end ---------------- */

/* ------------ quasar -------------- */
@import './quasar.variables';

/* ------------- end ---------------- */

/* ------------- 其它 --------------- */
@import './mixins';
@import '@hetu/theme/styles/variables';

/* ------------- end ---------------- */

/* ------------ 添加变量 ------------- */

// #region: base

// 渐变背景
$ht-background-color-gradual: linear-gradient(-135deg, #ff6b45, $primary);

// 渐变背景透明
$ht-background-color-gradual-opacity: linear-gradient(-135deg, rgb(0 111 121 / 80%), rgb(29 29 190 / 80%));

// 一级
$ht-background-color-base: #f0f2f5;

// 二级背景色
$ht-background-color-light: #f5f7fa;

// 三级背景色
$ht-background-color-lighter: #fafafa;

// loading-background
$ht-loading-background: rgb(255 255 255 / 35%);
$ht-color-primary-opacity-1: rgba($primary, 0.1);
$ht-all-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);

// #region: end

// #region: layout
$ht-layout-header-height: 56px;
$ht-layout-sider-width: 200px;

// #region: end

// #region: theme
$ht-theme-count: 8;
$ht-theme-1: $primary;
$ht-theme-2: #13c2c2;
$ht-theme-3: #52c41a;
$ht-theme-4: #2f54eb;
$ht-theme-5: #d4b106;
$ht-theme-6: #f5222d;
$ht-theme-7: #fa8c16;
$ht-theme-8: #eb2f96;
$ht-theme: (
  1: $ht-theme-1,
  2: $ht-theme-2,
  3: $ht-theme-3,
  4: $ht-theme-4,
  5: $ht-theme-5,
  6: $ht-theme-6,
  7: $ht-theme-7,
  8: $ht-theme-8
);
$ht-theme-secondary: (
  1: color.mix($ht-theme-1, #fff, 80%),
  2: color.mix($ht-theme-2, #fff, 80%),
  3: color.mix($ht-theme-3, #fff, 80%),
  4: color.mix($ht-theme-4, #fff, 80%),
  5: color.mix($ht-theme-5, #fff, 80%),
  6: color.mix($ht-theme-6, #fff, 80%),
  7: color.mix($ht-theme-7, #fff, 80%),
  8: color.mix($ht-theme-8, #fff, 80%)
);
$ht-theme-lighter: (
  1: color.mix(#fff, $ht-theme-1, 90%),
  2: color.mix(#fff, $ht-theme-2, 85%),
  3: color.mix(#fff, $ht-theme-3, 75%),
  4: color.mix(#fff, $ht-theme-4, 85%),
  5: color.mix(#fff, $ht-theme-5, 75%),
  6: color.mix(#fff, $ht-theme-6, 85%),
  7: color.mix(#fff, $ht-theme-7, 85%),
  8: color.mix(#fff, $ht-theme-8, 90%)
);

// #region: end

/* --------------- end --------------- */
