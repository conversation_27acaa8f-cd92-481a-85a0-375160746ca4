import {
  MYSPACE_CODE,
  ROUTE_DOMAIN_KEY,
  SpaceApi,
  type SpaceLoginModel,
  type LoginResult,
  type Profile,
  type ValidatorCode,
  type SpaceAccountConfig,
  type SpaceStatus
} from '../models';
import { PlatformService } from './platform.service';
import { AppService } from './app.service';
import { PageService } from './page.service';
import { RouteMenuService } from './route-menu.service';
import { TeamService } from './team.service';
import { getSpacePath } from '../utils';
import { PermissionsService } from './permissions.service';
import type { RouteLocationNormalized } from 'vue-router';
import { createSingleClass, useRouterWithout } from '@hetu/core';
import { HttpApiService, responseData, responseReject, responseResult, type ResponseResult } from '@hetu/http';
import { CacheService, CacheStoreType } from '@hetu/util';
import { SettingsService } from '@hetu/theme';
import { TokenService } from '@hetu/auth';
import { ACLService } from '@hetu/acl';

/**
 * 租户空间服务类
 */
export class SpaceServiceCtor extends HttpApiService<SpaceApi> {
  httpApi = new SpaceApi();

  httpModuleKey = 'space';

  spaceStatus: SpaceStatus | null = null;

  loaded = false;

  /** 空间标识 */
  private domainCode?: string;

  /** 租户 ID */
  private tenantId?: string;

  /**
   * 获取当前空间状态
   */
  async getSpaceStatus(): Promise<SpaceStatus | null> {
    if (this.loaded) {
      return this.spaceStatus;
    }

    this.spaceStatus = await this.http.get(this.api.status).then(responseData, responseReject);
    this.loaded = true;
    return this.spaceStatus;
  }

  /**
   * 获取空间账号登录配置信息
   * @see 缓存在`Memory`中
   */
  getAccountConfig(): Promise<SpaceAccountConfig | null> {
    return CacheService.get(this.api.accountConfig, {
      mode: 'promise',
      type: CacheStoreType.Memory
    }).then((result) => (result ? result.data : null), responseReject);
  }

  /**
   * 获取登录用户信息
   */
  getProfile(): Promise<Profile | null> {
    return this.http.get(this.api.info).then(responseData, responseReject);
  }

  /**
   * 获取验证码图片
   */
  validatorCode(): Promise<ValidatorCode | null> {
    return this.http.get(this.api.validatorCode).then(responseData, responseReject);
  }

  /**
   * 登录空间
   * @param model 登录信息
   */
  login(model: SpaceLoginModel): Promise<ResponseResult<LoginResult> | null> {
    let password = btoa(model.password);
    while (password.endsWith('=')) {
      password = password.substring(0, password.length - 1);
    }
    if (password.length > 5) {
      password = password.substring(5, password.length) + password.substring(0, 5);
    }
    const encodedUser = { ...model, password };
    return this.http.post(this.api.login, encodedUser).then(responseResult, responseReject);
  }

  /**
   * 退出空间登录
   * @param dataName 资源名称
   */
  logout(dataName?: string) {
    return this.http
      .get(this.api.logout)
      .then(responseResult, responseReject)
      .then((result) => {
        if (result?.status === 'success') {
          this.clearUserInfo();
          this.toLogin(dataName);
        }
      });
  }

  /**
   * 跳转单点登录
   * @param redirect 重定向地址
   */
  toSSO(redirect: string) {
    const url = this.api.sso.startsWith('/') ? `.${this.api.sso}` : this.api.sso;
    let spaceUrl = PageService.getRelativePageUrl(import.meta.env.VITE_HETU_SPACE_KEY);
    spaceUrl = encodeURIComponent(spaceUrl + '?preloader=hide&redirect=' + encodeURIComponent(redirect));
    location.href = `${url}?redirectUrl=${spaceUrl}&domain=${this.domainCode}`;
  }

  /**
   * 跳转空间登录
   * @param dataName 资源名称
   */
  toLogin(dataName?: string) {
    const router = useRouterWithout();
    const route = router.currentRoute.value;
    const query: Record<string, string | undefined> = {
      redirect: (route.query.redirect as string) ?? encodeURIComponent(route.fullPath),
      dataName: dataName ?? (route.query.dataName as string)
    };
    query[ROUTE_DOMAIN_KEY] = this.getDomainCode();

    // 跳转路由
    router.replace({ path: TokenService.loginUrl, query });
  }

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    SettingsService.setUser();
  }

  /**
   * 获取当前空间的域名标识
   * @param route 路由对象
   * - 非云平台模式从路由中获取域名标识
   */
  getDomainCode(route?: RouteLocationNormalized) {
    if (PlatformService.domain) {
      const domain = PlatformService.domain;
      const current = document.domain;
      return [current, current.replace('www.', '')].includes(domain) ? '' : current.replace(`.${domain}`, '');
    }

    // 从路由中获取域名标识
    if (route) {
      let key = route?.params[ROUTE_DOMAIN_KEY] as string;
      key = key === MYSPACE_CODE ? '' : key;
      return key;
    }

    return this.domainCode === MYSPACE_CODE ? '' : this.domainCode;
  }

  /**
   * 获取当前空间的租户 ID
   */
  getTenantId() {
    return this.tenantId;
  }

  /**
   * 获取当前页面空间中的路由路径
   * @param path 路由路径
   */
  getFullPath(path: string) {
    if (PlatformService.domain) {
      return path;
    }
    return getSpacePath(path, this.getDomainCode());
  }

  /**
   * 切换空间
   * @param domain 空间域名标识
   */
  change(domain?: string) {
    if (AppService.appKey) {
      PageService.toApp(AppService.appKey, domain);
    } else {
      PageService.toMain(domain);
    }
  }

  /**
   * 设置空间标识: 非云平台进入此方法
   * @param domain 空间域名标识
   * @param tenantId 租户 ID
   */
  set(domain: string, tenantId?: string) {
    this.tenantId = tenantId;
    if (this.domainCode === domain) {
      return;
    }

    this.domainCode = domain;
    this.loaded = false;
    AppService.loaded = false;
    RouteMenuService.clear();
    PermissionsService.initialized = false;
    ACLService.set({});
    TeamService.loaded = false;
  }
}

export const SpaceService = createSingleClass(SpaceServiceCtor);
