@import '../../../../../index.scss';

.#{$vis-prefix}-constraints {
  border: 1px solid $input-hover-color;
  background: $input-bg;

  @apply w-full h-56px relative rounded;

  &:after {
    content: '';
    border: 1px solid $input-hover-color;
    width: 30px;
    height: 30px;

    @apply absolute top-50% left-50% rounded -translate-50%;
  }

  .hand {
    @apply w-4 h-4 absolute z-1 cursor-pointer;

    &:after {
      content: '';
      background: #6b7280;
      @apply absolute top-50% left-50% -translate-50%;
    }

    &.l {
      @apply left-6px top-50% -translate-y-50%;

      &:after {
        @apply h-1px w-8px;
      }
    }

    &.r {
      @apply right-6px top-50% -translate-y-50%;

      &:after {
        @apply h-1px w-8px;
      }
    }

    &.t {
      @apply -top-1px left-50% -translate-x-50%;

      &:after {
        @apply h-8px w-1px;
      }
    }

    &.b {
      @apply -bottom-1px left-50% -translate-x-50%;

      &:after {
        @apply h-8px w-1px;
      }
    }

    &.active:after,
    &.active:after {
      background: $primary;
    }

    &.l.active:after,
    &.r.active:after {
      @apply h-2px;
    }

    &.t.active:after,
    &.b.active:after {
      @apply w-2px;
    }

    &.v,
    &.h {
      @apply -translate-50% top-50% left-50%;

      &:after {
        @apply -translate-50% top-50% left-50%;
      }
    }

    &.v {
      @apply w-5px;
      &:after {
        @apply w-1px h-12px;
      }
    }

    &.h {
      @apply h-5px;
      &:after {
        @apply w-12px h-1px;
      }
    }

    &.v.active:after {
      @apply w-2px;
    }
    &.h.active:after {
      @apply h-2px;
    }
  }
}
