import { computed, h } from 'vue';
import { useDesignStore } from '../stores';
import { UUID } from '@hetu/platform-shared';
import {
  useWidgetStore,
  WidgetConfig,
  WidgetBlock,
  WidgetType,
  Color,
  Effects,
  FillPaints,
  FillType,
  Stroke
} from '@vis/document-core';
import type { Records } from '@hetu/util';

export const useWidget = () => {
  const designStore = useDesignStore();

  const { interactConfigs, optionConfigs } = useWidgetStore();
  /**
   * 将组件数据处理成画布需要的格式
   */
  const handleWidgetData = (widget: WidgetConfig) => {
    if (widget.datasetType === 'static') {
      /* "datasetId": null,
      "datasetType": null,
      "dataMapping": null,
      "refreshType": null,
      "refreshSecond": null, */
      return widget.staticData;
    }

    return widget;
  };

  /**
   * 将组件处理成画布需要的格式
   */
  const handleWidget = (widget: WidgetConfig) => {
    const widgetType = widget.type as WidgetType;
    const _widget = new WidgetBlock();
    const data = handleWidgetData(widget);
    const newWidget = {
      ..._widget,
      id: UUID(),
      type: widgetType,
      name: widget.name,
      datasetType: widget.datasetType,
      staticData: data,
      options: optionConfigs?.[widgetType]?.options,
      interact: interactConfigs[widgetType]
    } as WidgetBlock;
    return {
      widgetBlock: newWidget,
      block: optionConfigs[widgetType]?.block
    };
  };

  /**
   * 定义映射类
   */
  const classMap: Records<any> = {
    stroke: Stroke,
    fillPaints: FillPaints,
    effects: Effects
  };

  /**
   * 判断obj上key是否存在或为undefined
   * @param obj
   * @param key
   * @returns
   */
  const isUndefined = (obj: Object, key: string) => {
    return !obj[key as keyof typeof obj];
  };

  /**
   * 为obj添加属性或置为undefined
   * @param obj
   * @param key
   */
  const handleManage = (obj: Object, key: string) => {
    if (isUndefined(obj, key)) {
      // 特殊类型可传入参数
      const args = key === 'fillPaints' ? [FillType.Solid, new Color(0, 0, 0, 0.1)] : [];

      Object.assign(obj, { [key]: new classMap[key](...args) });
    } else {
      Object.assign(obj, { [key]: undefined });
    }
  };

  return { handleWidget, isUndefined, handleManage };
};
