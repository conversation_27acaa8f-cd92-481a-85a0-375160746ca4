import {
  Color,
  Effects,
  FillPaints,
  FillType,
  Font,
  Image,
  Stroke,
  Text,
  TextAlign,
  TextEffects,
  VerticalAlign
} from '../../ui';
import { WidgetName, WidgetType } from '../config/widget-enum';
import { WidgetBlock } from '../widget-block';

/**
 * 选项卡
 * <AUTHOR>
 */
export class Tab extends WidgetBlock {
  type = WidgetType.Tab;

  name = WidgetName.Tab;

  options: TabOptions = new TabOptions();
}

/**
 * 选项卡配置项
 */
export class TabOptions {
  /**
   * 是否多选
   */
  multiple: boolean = false;

  /**
   * 选项卡数据项
   */
  options: TabOption[] = [];

  /**
   * 列宽模式
   */
  resizeX = TabResizeType.Adapt;

  /**
   * 行高模式
   */
  resizeY = TabResizeType.Adapt;
  /**
   * 选项卡列宽
   */
  width: number = 200;

  /**
   * 选项卡行高
   */
  height: number = 50;

  /**
   * 默认选中项
   */
  defaultIndex: string = '0';

  /**
   * 选项卡排版布局
   */
  layout: Layout = new Layout();

  /**
   * 图标
   */
  icon?: IconOption;

  style: TabStyle = new TabStyle();

  /**
   * 选项卡轮播配置
   */
  carousel?: Carousel;
}

/**
 * 选项卡数据选项
 */
export class TabOption {
  id: string = '';
  text: string = '';
  value: string | number | boolean | Record<string, any> = '';
  icon?: string;
  disabled = false;
}

/**
 * 布局
 */
export class Layout {
  /**
   * 列数
   */
  column: number = 4;
  /**
   * 行数
   */
  row: number = 1;
  /**
   * 选项卡间距
   */
  gutter: [number, number] = [4, 4];

  /**
   * 滚动条
   */
  scrollbar = false;
}

/**
 * 图标配置
 */
export class IconOption {
  visible = false;

  width = 20;

  height = 20;
  /**
   * 图片
   */
  image?: Image;

  /**
   * 左边距  右边距
   */
  gutter: [number, number, number, number] = [0, 0, 0, 0];
}

/**
 * 轮播
 */
export class Carousel {
  /**
   * 间隔时长
   */
  interval: number = 1;
  /**
   * 用户点击停留时长
   */
  clickTime: number = 20;
}

/**
 * 选项卡样式配置
 */
export class TabStyle {
  /** 文字方向 */
  textDirection: string = 'horizontal';
  /**
   * 水平对齐方式
   */
  align: string = 'center';
  /**
   * 垂直对齐方式
   */
  verticalAlign: string = 'center';

  /**
   * 文本溢出效果
   *  0溢出  1省略号  2换行 3跑马灯
   */
  overflow: number = 0;

  /**
   * 滚动速度
   * 只有跑马灯效果的时候可配置
   */
  scrollSpeed = 1;

  /**
   * 字体样式
   */
  font: TabText = new TabText(14, 700, undefined, new FillPaints(FillType.Solid, new Color(95, 95, 110)));

  /**
   * 背景样式
   */
  background?: FillPaints = new FillPaints(FillType.Solid, new Color(255, 255, 255));

  /**
   * 描边
   */
  border?: Stroke = new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(230, 225, 230)));

  /**
   * 圆角
   */
  radius = [4, 4, 4, 4];

  /**
   * 透明度
   */
  opacity = 100;

  /**
   * 阴影
   */
  shadow?: Effects;

  /**
   * 悬浮样式
   */
  hover?: TabItemStyle = new TabItemStyle(
    new TabText(14, 700, undefined, new FillPaints(FillType.Solid, new Color(21, 100, 254))),
    new FillPaints(FillType.Solid, new Color(234, 242, 249)),
    new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(25, 130, 228)))
  );

  /**
   * 激活样式
   */
  active?: TabItemStyle = new TabItemStyle(
    new TabText(14, 700, undefined, new FillPaints(FillType.Solid, new Color(21, 100, 254))),
    new FillPaints(FillType.Solid, new Color(255, 255, 255), 0),
    new Stroke([2, 2, 2, 2], undefined, new FillPaints(FillType.Solid, new Color(25, 130, 228)))
  );
}

/**
 * 滚动条样式
 */
export class BarStyle {
  width = 1;
  color = new Color(0, 0, 0, 1);

  constructor(color?: Color) {
    color && (this.color = color);
  }
}

/**
 * 每个选项样式
 */
export class TabItemStyle {
  /**
   * 样式显隐
   */
  visible = true;
  /**
   * 字体样式
   */
  font: TabText = new TabText();

  /**
   * 背景样式
   */
  background: FillPaints = new FillPaints();

  /**
   * 描边
   */
  border: Stroke = new Stroke();

  /**
   * 图标
   */
  icon: IconOption = new IconOption();

  /**
   * 阴影
   */
  shadow: Effects = new Effects();

  constructor(font?: TabText, background?: FillPaints, border?: Stroke, shadow?: Effects) {
    font && (this.font = font);
    background && (this.background = background);
    border && (this.border = border);
    shadow && (this.shadow = shadow);
  }
}

/**
 * 选择模式
 */
export enum SelectMode {
  /** 单选 */
  Single = 'single',

  /** 多选 */
  Multiple = 'multiple'
}

/**
 * 选项卡文本
 */
export class TabText extends Font {
  /** 字间距 */
  letterSpacing: number = 0;

  /** 行高 */
  lineHeight: number = 0;

  /** 对齐方式 */
  alignHorizontal = TextAlign.Center;

  /** 斜体 */
  italic: boolean = false;

  /** 下划线 */
  underlined: boolean = false;

  /** 删除线 */
  through: boolean = false;

  /** 垂直对齐 */
  alignVertical = VerticalAlign.Center;

  /**
   * 文本阴影
   */
  textEffects: TextEffects = new TextEffects(undefined, new Color(0, 0, 0, 0));

  constructor(size?: number, weight?: number, fontFamily?: string, color?: FillPaints) {
    super(size, weight, fontFamily, color);
  }
}

export enum TabResizeType {
  /** 固定尺寸 */
  Fixed = 'fixed',

  /** 适应容器 */
  Adapt = 'adapt'
}
