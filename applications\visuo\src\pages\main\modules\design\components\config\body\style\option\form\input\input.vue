<template>
  <div class="vis-config-input-option">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 基本 </span>
      </div>
      <div class="vis-config-card__body">
        <!-- 输入框类型 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">类型</div>
            <div class="vis-form-field__content">
              <vis-select v-model="computedOptions.type" @update:modelValue="onTypeChange" :options="typeOptions" />
            </div>
          </div>
        </div>
        <!-- 默认值 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">默认值</div>
            <div class="vis-form-field__content">
              <q-input
                v-model="computedOptions.defaultValue"
                placeholder="请输入默认值"
                flat
                borderless
                class="vis-input w-full rounded-borders px-2"
              />
            </div>
          </div>
        </div>
        <!-- 占位符 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">占位符</div>
            <div class="vis-form-field__content">
              <q-input
                v-model="computedOptions.placeholder"
                placeholder="请输入占位符"
                flat
                borderless
                class="vis-input w-full rounded-borders px-2"
              />
            </div>
          </div>
        </div>
        <!-- 清除、禁用 -->
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">状态</div>
            <div class="vis-form-field__content">
              <q-checkbox v-model="computedOptions.disabled" label="禁用" />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label"></div>
            <div class="vis-form-field__content">
              <q-checkbox v-model="computedOptions.clearable" label="可清除" />
            </div>
          </div>
        </div>
        <q-separator />
      </div>
    </div>
    <!-- 文本 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 文本 </span>
        <q-btn flat dense @click="toggleFont">
          <ht-icon class="vis-icon" :name="computedOptions.style?.font ? 'vis-subtract' : 'vis-add'" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <vis-text v-if="computedOptions.style?.font" :option="computedOptions.style.font" />
        <q-separator />
      </div>
    </div>

    <!-- 标签设置 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 标签 </span>
        <q-btn flat dense @click="toggleLabel">
          <ht-icon class="vis-icon" :name="options.label?.show ? 'vis-subtract' : 'vis-add'" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div v-if="options.label?.show">
          <vis-form-label :options="options.label" />
        </div>
        <q-separator />
      </div>
    </div>

    <!-- 查询器 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 查询器 </span>
        <q-btn flat dense @click="toggleQuery">
          <ht-icon class="vis-icon" :name="computedOptions.query ? 'vis-subtract' : 'vis-add'" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div v-if="computedOptions.query">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-fill__content flex" :class="{ disabled: !computedOptions.query.show }">
                  <q-btn flat dense>
                    <q-icon :name="computedOptions.query.locked ? 'lock_outline' : 'lock_open'" class="vis-icon" />
                    <q-menu v-model="showMenuLock" style="width: 120px" class="vis-menu" dense>
                      <q-list dense>
                        <q-item :active="computedOptions.query.locked" @click="handleLock" clickable v-close-popup>
                          <q-item-section avatar>
                            <q-icon name="lock_outline" class="vis-icon" />
                          </q-item-section>
                          <q-item-section>锁定</q-item-section>
                        </q-item>
                        <q-item :active="!computedOptions.query.locked" @click="handleLock" clickable v-close-popup>
                          <q-item-section avatar>
                            <q-icon name="lock_open" class="vis-icon" />
                          </q-item-section>
                          <q-item-section>解锁</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </q-btn>
                  <q-separator vertical class="!m-0" />
                  <vis-select
                    class="flex-1 no-outline"
                    v-model="computedOptions.query.type"
                    :options="InputQueryTypeOptions"
                    :disabled="computedOptions.query.locked"
                  />
                </div>
              </div>
            </div>
            <q-btn class="btn-field" @click="handleQueryVisible">
              <ht-icon class="vis-icon" :name="computedOptions.query?.show ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
            </q-btn>
          </div>
        </div>
        <q-separator />
      </div>
    </div>

    <!-- 前后缀 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 前后缀 </span>
        <q-btn flat dense v-if="!computedOptions.style?.prefix || !computedOptions.style?.suffix">
          <ht-icon class="vis-icon" name="vis-add" />
          <q-menu v-model="showMenuFix" style="width: 66px" class="vis-menu" dense>
            <q-list dense>
              <q-item :disable="!!computedOptions.style.prefix" @click="addFix('prefix')" clickable v-close-popup>
                <q-item-section>前缀</q-item-section>
              </q-item>
              <q-item :disable="!!computedOptions.style.suffix" @click="addFix('suffix')" clickable v-close-popup>
                <q-item-section>后缀</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline" v-if="computedOptions.style.prefix">
          <div class="vis-form-inline__content--minus-32">
            <vis-form-fix :options="computedOptions.style.prefix" />
          </div>
          <q-btn class="btn-field" @click="delFix('prefix')">
            <ht-icon class="vis-icon" name="vis-subtract" />
          </q-btn>
        </div>
        <div class="vis-form-inline" v-if="computedOptions.style.suffix">
          <div class="vis-form-inline__content--minus-32">
            <vis-form-fix :options="computedOptions.style.suffix" />
          </div>
          <q-btn class="btn-field" @click="delFix('suffix')">
            <ht-icon class="vis-icon" name="vis-subtract" />
          </q-btn>
        </div>
        <q-separator />
      </div>
    </div>

    <!-- 校验规则 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 校验规则 </span>
        <q-btn flat dense v-if="computedOptions.rules.length < ruleOptions.length">
          <ht-icon class="vis-icon" name="vis-add" />
          <q-menu v-model="showMenuRule" style="width: 90px" class="vis-menu" dense>
            <q-list dense>
              <q-item
                v-for="(rule, index) in ruleOptions"
                :key="index"
                :disable="hasRules(rule.value)"
                @click="addRule(rule.value)"
                clickable
                v-close-popup
              >
                <q-item-section>{{ rule.label }}</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline" v-for="(rule, index) in computedOptions.rules" :key="index">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-fill__content flex">
                <q-input model-value="" borderless class="rounded-borders flex-1 px-2" input-class="hidden">
                  <span class="text-font-regular">
                    {{ ruleOptions.find((item) => item.value === rule.type)?.label }}
                  </span>
                </q-input>
                <template v-if="rule.value !== undefined">
                  <q-separator vertical class="!m-0" />
                  <q-input v-model="rule.value" borderless class="rounded-borders flex-1 px-2" />
                </template>
              </div>
            </div>
          </div>
          <q-btn class="btn-field" @click="delRule(index)">
            <ht-icon class="vis-icon" name="vis-subtract" />
          </q-btn>
        </div>
        <q-separator />
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./input.ts"></script>
