<template>
  <div class="vis-stroke">
    <div class="vis-form-inline">
      <div :class="`vis-form-inline__content--minus-${minusWidth}`">
        <vis-fill v-model="computedStroke.paints" />
      </div>
    </div>

    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-60">
        <div class="vis-form-field">
          <div class="vis-form-field__label">位置</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedStroke.align" :options="alignOptions" />
          </div>
        </div>
        <div class="vis-form-field--narrow">
          <div class="vis-form-field__label">粗细</div>
          <div class="vis-form-field__content">
            <vis-number
              v-model="computedStroke.position[positionIndex === 4 || positionIndex === 5 ? 0 : positionIndex]"
              :min="0"
              @change="handlePositionChange"
              icon="vis-line-width"
            />
          </div>
        </div>
      </div>

      <q-btn class="btn-field" :class="{ active: positionIndex === 5 }">
        <img class="img-icon" :src="`./static-next/svg/stroke/active-${strokeIcon}.svg`" />
        <q-menu class="vis-menu">
          <q-list>
            <q-item v-for="item in strokeOptions" :key="item.value" :active="item.active" clickable v-close-popup>
              <q-item-section class="!flex-row items-center !justify-start" @click="handleStrokeChange(item)">
                <img
                  class="img-icon mr-2"
                  :src="`./static-next/svg/stroke/${item.active ? 'active-' + item.icon : item.icon}.svg`"
                />

                <q-item-label class="w-8">
                  {{ item.label }}
                </q-item-label>
              </q-item-section>
            </q-item>
            <q-separator class="!my-2" />
            <q-item clickable v-close-popup>
              <q-item-section
                @click="handleStrokeChange({ icon: 'border-a', value: 5, active: false })"
                class="!flex-row items-center !justify-start"
              >
                <ht-icon class="vis-icon mr-2 ml-1" name="hticon-vis-control" />
                <q-item-label> 自定义 </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>

      <q-btn class="!ml-1" :class="{ active: popupShow }" @click.stop="showPopup">
        <ht-icon class="vis-icon" name="hticon-vis-control" />
      </q-btn>
      <vis-popup title="描边样式" ref="popupRef" :target="false" @hide="popupShow = false">
        <div class="vis-form-inline">
          <div class="vis-form-field">
            <div class="vis-form-field__label">样式</div>
            <div class="vis-form-field__content">
              <vis-select class="w-full" v-model="computedStroke.style" :options="lineOptions" :disable="styleDisabled">
                <template #prepend>
                  <span
                    class="mr-2"
                    :style="{ borderStyle: computedStroke.style, borderWidth: '0.5px', width: '20px' }"
                  ></span>
                </template>
                <template #option="{ opt, itemProps }">
                  <q-item v-bind="itemProps">
                    <q-item-section class="!flex-row items-center !justify-start">
                      <span
                        class="mr-2"
                        :style="{ borderStyle: opt.style, borderWidth: '0.5px', width: '20px' }"
                      ></span>
                      {{ opt.label }}
                    </q-item-section>
                  </q-item>
                </template>
              </vis-select>
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label">位置</div>
            <div class="vis-form-field__content">
              <vis-select class="w-full" v-model="computedStroke.align" :options="alignOptions" />
            </div>
          </div>

          <div class="vis-form-field">
            <div class="vis-form-field__label">粗细</div>
            <div class="vis-form-field__content">
              <vis-number
                v-model="computedStroke.position[positionIndex === 4 || positionIndex === 5 ? 0 : positionIndex]"
                :min="0"
                @change="handlePositionChange"
                icon="vis-line-width"
              />
            </div>
          </div>
        </div>
      </vis-popup>
    </div>

    <div class="vis-form-inline" v-if="positionIndex === 5">
      <div class="vis-form-inline__content--minus-28">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[0]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-t.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[1]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-r.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-if="positionIndex === 5">
      <div class="vis-form-inline__content--minus-28">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[2]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-b.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="computedStroke.position[3]" :min="0">
              <template #icon>
                <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-l.svg'" />
              </template>
            </vis-number>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./index.ts"></script>
