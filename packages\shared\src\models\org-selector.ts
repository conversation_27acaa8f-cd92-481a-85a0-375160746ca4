import type { HttpApi } from '@hetu/http';
import { SYSTEM_API_PREFIX } from './index';
/**
 * 树节点
 */
export class OrgSelectorTreeNode {
  id = '';
  fullName = '';
}

export enum SkinType {
  U = 'U',
  D = 'D'
}

export const SkinTypeName = {
  [SkinType.U]: '成员',
  [SkinType.D]: '部门'
};

export class OrgApi implements HttpApi {
  depts = `${SYSTEM_API_PREFIX}/team/dept/list`;
  members = `${SYSTEM_API_PREFIX}/team/dept/member`;
  search = `${SYSTEM_API_PREFIX}/member/search`;
  info = `${SYSTEM_API_PREFIX}/member/current/info`;
  getName = `${SYSTEM_API_PREFIX}/member/name`;
  /** 校验密码策略 */
  checkPasswordStrategy = `${SYSTEM_API_PREFIX}/user/password/check`;
}

export interface OrgConfig {
  user?: Record<string, any>;
}

export const DEPT_SYS_ROOT = 'DEPARTMENT_SYS_ROOT';

/**
 * 组织机构选择器参数
 */
export class OrgSelectorOption {
  /** 标题 */
  title = '';
  id?: string | string[];
  name?: string | string[];
  /** 搜索 */
  search = true;
  /** 是否多选 */
  multiple = true;
  /** 拼接符 */
  concat?: string;
  /** 待选择列表是否分页 */
  paging = true;
  /** 每页显示数量 */
  pageSize = 15;

  /** 是否开启权限过滤 */
  auth = false;

  /** 是否显示部门树, 取值范围 `[true, false, '部门ID']` */
  dept: boolean | string = true;

  /** 是否展开选中部门树, 取值范围 `[true, false, '部门ID']` */
  openDept: boolean | string = false;

  /** 是否允许选择部门 */
  selectDept = true;
  /** 是否允许选择人 */
  selectUser = true;

  /** 包含几级子部门人员,值为0时包含所有下级部门人员,为 1 向下包含 1 级，以此类推 */
  subUserLevel = -1; //

  /*    showIcon: {
            dept: false,       //是否显示部门图标
            position: false,   //是否显示岗位图标
            group: false,      //是否显示群组图标
            role: false,       //是否显示角色图标
            user: false        //是否显示用户图标
        }, */

  /** 获取 `orgTree` 数据的请求路径 */
  orgTreeSource = '';

  /** 获取已选中的 `orgTree` 数据的请求路径 */
  selectTreeSource = '';

  /** 搜索的请求路径 */
  searchSource = '';

  /** 请求参数 */
  otherParam?: Record<string, any>;

  /** 打开窗口事件 */
  open?: any;

  /** 关闭窗口前事件,返回一个 `boolean` 值，确定是否关闭窗口 */
  beforeClose?: any;

  /** 关闭窗口事件 */
  close?: any;
}
