/**
 * 根据字段的数据类型返回图标
 * @param type
 */
export function getDataTypeIcon(type?: string) {
  switch (type) {
    case 'date':
    case 'date_1':
    case 'date_2':
      return 'date';
    case 'datetime':
    case 'datetime_1':
    case 'datetime_2':
      return 'datetime';
    case 'time':
    case 'time_1':
      return 'time';
    case 'integer':
    case 'number':
      return 'int';
    case 'double':
      return 'double';
    case 'string':
      return 'string';
    case 'area_province':
    case 'area_city':
    case 'area_district':
    case 'area':
      return 'area';
    default:
      return type;
  }
}
