/** 当前登录用户角色及已授权资源 */
export interface ACLDataType {
  /** 角色 */
  roles?: string[];

  /** 资源 */
  resources?: Record<string, string | string[]>;
}

/**
 * 校验模式, 默认：`oneOf`
 * - `allOf` 表示必须满足所有角色或资源算有效
 * - `oneOf` 表示只须满足角色组或资源中的一项算有效
 */
export type ACLModeType = 'allOf' | 'oneOf';

export interface ACLType {
  /** 角色 */
  role?: string[];

  /** 资源 */
  resource?: Record<string, string | string[]>;

  /**
   * 校验模式, 默认：`oneOf`
   * - `allOf` 表示必须满足所有角色或资源算有效
   * - `oneOf` 表示只须满足角色组或资源中的一项算有效
   */
  mode?: ACLModeType;

  /** 是否取反, 即结果为 `true` 时表示未授权 */
  except?: boolean;
}

type ACLResourceCodeInstance<K extends keyof any> = {
  [P in K]: ACLCanType;
};

/**
 * 权限资源实例类型
 */
export type ACLResourceInstance<K extends keyof any, T = ACLResource<K>> = ACLResourceCodeInstance<K> & T;

/**
 * 权限资源基类
 * @example
 * ```ts
 * export enum TestACLResourceCode {
 *   Add = 'create',
 *   Delete = 'delete',
 *   Edit = 'edit',
 *   Save = 'save'
 * }
 *
 * export class TestACLResource extends ACLResource<typeof TestACLResourceCode> {
 *   readonly resourceGroup: string = 'test';
 *
 *   get unsave(): ACLCanType {
 *     const code = this.resourceCode;
 *     return { resource: { [this.resourceGroup]: [code.Save] }, except: true };
 *   }
 *
 *   constructor() {
 *     super();
 *
 *     // 注意: 给 `resourceCode` 赋值, 重要!!!
 *     this.resourceCode = TestACLResourceCode;
 *   }
 * }
 *
 * // `acl` 实例中的可用属性: `acl.resourceCode`, `acl.resourceGroup`, `acl.unsave`
 * // 如果想要在 IDE 中通过 `acl.` 获取到依据 `ResourceCode` 值生成的属性提示,
 * // 则需要添加类型断言: `as ACLResourceInstance<TestACLResourceCode, TestACLResource>`
 * // 然后可见: `acl.create`, `acl.delete`, `acl.edit`, `acl.save`
 * const acl = new TestACLResource() as ACLResourceInstance<TestACLResourceCode, TestACLResource>;
 * ```
 */
export abstract class ACLResource<T> {
  /** 资源组 */
  abstract readonly group: string;

  private _resourceCode!: T;

  get resourceCode(): T {
    return this._resourceCode;
  }

  set resourceCode(codes: T) {
    this._resourceCode = codes;
    if (this.group && codes) {
      // 创建默认 acl 验证对象
      const group = this.group;
      const keys = Object.keys(codes);
      keys.forEach((key) => {
        const value = (<any>codes)[key];
        if (!(<any>this)[value] && typeof value === 'string') {
          Object.defineProperty(this, value, {
            get: (): ACLCanType => {
              return { resource: { [group]: value } };
            }
          });
        }
      });
    }
  }

  [key: string]: ACLCanType | T;
}

/**
 * 创建权限资源
 * @param resourceGroup 资源组
 * @param resourceCode 资源点编码对象
 * @returns ACLResourceInstance
 * @example
 * ```ts
 * export enum TestACLResourceCode {
 *   Add = 'create',
 *   Delete = 'delete',
 *   Edit = 'edit',
 *   Save = 'save'
 * }
 *
 * const testCodeObj = {
 *   save: 'save',
 *   unsave: { resource: { 'test': [ 'save' ] }, except: true }
 * };
 *
 * const acl = createACLResource<TestACLResourceCode>('test', TestACLResourceCode);
 * const acl2 = createACLResource<keyof typeof testCodeObj>('test', testCodeObj);
 * ```
 */

export function createACLResource<T>(resourceGroup: string, resourceCode: T) {
  class _ACLResource extends ACLResource<T> {
    readonly group: string = resourceGroup;

    constructor() {
      super();
      this.resourceCode = resourceCode;
    }
  }

  const instance = new _ACLResource() as ACLResourceInstance<keyof any, ACLResource<T>>;

  return instance;
}

/**
 * 权限验证参数类型
 * - `string` | `string[]` 应为角色值
 */
export type ACLCanType = string | string[] | ACLType;
