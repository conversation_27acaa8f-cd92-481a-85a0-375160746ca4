/**
 * 协作者信息
 */
export interface Collaborator {
  id: string;

  /** 协作人ID */
  objectId: string;

  /** 协作人名称 */
  fullName: string;

  /** 协作人头像ID */
  avatar: string;

  /** 协作人电话 */
  cellphone: string;

  /** 协作人邮箱 */
  email: string;

  /**
   * 授权模式
   * - 0: 直接分配
   * - 1: 继承上级
   */
  authMode: number;

  /** 创建时间 */
  createdTime: string;

  /** 协作权限身份ID */
  privilegeId: string;

  /** 权重，值越大权限优先级越高 */
  weighted: number;
}

export interface CollaboratorDTO {
  /** 文件数据ID */
  dataId: string;

  /** 文件名称 */
  dataName: string;

  /** 被邀请对象ID */
  objectId: string;

  /** 协作权限身份ID */
  privilegeId: string;

  /** 目录分类 */
  category: string;

  /**
   * 类别
   * - 0: 文件
   * - 1: 文件夹
   * */
  dataType: number;

  /** 模块编码 */
  moduleCode: string;
}
