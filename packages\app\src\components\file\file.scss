.#{$ht-prefix}-app-file {
  @apply relative cursor-pointer;

  width: 128px;
  margin-right: 6px;
  margin-left: 6px;

  .is-current-show {
    @apply opacity-0 duration-300;
  }

  &:hover .is-current-show,
  &.is-hover .is-current-show,
  &.is-active .is-current-show,
  .is-show {
    @apply opacity-100;
  }

  &__body {
    @apply relative rounded line-height-none duration-300 text-center;
  }

  &:hover &__body,
  &.is-hover &__body {
    @apply bg-grey-light;
  }

  &.is-active &__body {
    @apply bg-primary-lighter;
  }

  &__icon {
    @apply text-6rem;
  }

  &__title {
    @apply leading-6;

    .q-field__control {
      height: 24px;
    }
  }

  &--dense {
    width: 96px;
    margin-right: 4px;
    margin-left: 4px;
  }

  &--dense &__icon {
    @apply text-4rem;
  }
}

.body--dark {
  .#{$ht-prefix}-app-file {
    &:hover,
    &.is-hover,
    &.is-active {
      .#{$ht-prefix}-app-file__body {
        @apply bg-dark;
      }
    }

    &.is-active .#{$ht-prefix}-app-file__body {
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: rgb(255 255 255 / 7%);
        pointer-events: none;
      }
    }
  }

  .q-dark .#{$ht-prefix}-app-file {
    &:hover,
    &.is-hover,
    &.is-active {
      .#{$ht-prefix}-app-file__body {
        background: rgb(255 255 255 / 7%);
      }
    }
  }
}
