import { responseResult, HttpApiService, responseReject, responseData } from '@hetu/http';
import { SettingsService } from '@hetu/theme';
import { type AccountConfig, type LoginResult, PassportApi, type ValidatorCode } from '../models';
import { OrgService } from './org.service';
import { PlatformService } from './platform.service';
import { CacheService, CacheStoreType } from '@hetu/util';
import { TokenService, type TokenModel } from '@hetu/auth';
import { createSingleClass } from '@hetu/core';

class PassportServiceCtor extends HttpApiService<PassportApi> {
  httpApi = new PassportApi();

  httpModuleKey = 'account';

  /**
   * 获取验证码图片
   */
  validatorCode(): Promise<ValidatorCode | null> {
    return this.http.get(this.api.validatorCode).then(responseData, responseReject);
  }

  /**
   * 验证图片验证码
   * @param code
   * @param codeId
   * @returns
   */
  loginCodeCheck(code: string, codeId: string) {
    return this.http.get(this.api.loginCodeCheck, { params: { code, codeId } }).then(responseResult, responseReject);
  }

  /**
   * 获取账号配置信息
   * @see 缓存在`SessionStore`中
   */
  getAccountConfig(): Promise<AccountConfig | null> {
    return CacheService.get(this.api.accountConfig, {
      mode: 'promise',
      type: CacheStoreType.Memory
    }).then(responseData, responseReject);
  }

  /**
   * 退出登录
   */
  logout() {
    const result = this.api.logout
      ? this.http.get(this.api.logout).then(responseResult, responseReject)
      : Promise.resolve();
    return result.then(() => this.clearUserInfo());
  }

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    TokenService.clear();
    SettingsService.setUser();
  }

  /**
   * 生成 `Token` 存储信息
   * @param data 登录返回信息
   */
  makeTokenData(data: LoginResult) {
    let tokenValue = data.token;
    const tokenData: TokenModel = { token: tokenValue };

    // 如果 `data` 中包含 `tokenValue` 值的属性则 `tokenValue` 为:
    // - 返回信息中获取 `token` 的属性名
    // - 发送请求时的 `token` 的属性名
    if (data[tokenValue]) {
      const tokenKey = tokenValue;
      TokenService.config.sendKey = tokenKey;
      tokenValue = data[tokenKey];
      tokenData.key = tokenKey;
      tokenData.token = tokenValue;
    }

    return tokenData;
  }

  /**
   * 处理登录返回结果
   * - 设置 `token` 和 用户信息, 更改 `TokenConfig`
   * @param data 登录返回信息
   */
  async handleLoginResult(data: LoginResult) {
    const tokenData = this.makeTokenData(data);
    let user = data.user;
    if (!user) {
      TokenService.set(tokenData);
      user = (await OrgService.getProfile()) || {};
    }

    if (PlatformService.domain) {
      tokenData.user = user;
      TokenService.set(tokenData);
    } else {
      SettingsService.setUser(user);
    }
  }
}

export const PassportService = createSingleClass(PassportServiceCtor);
