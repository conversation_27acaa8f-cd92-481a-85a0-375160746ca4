import { defineComponent, ref } from 'vue';
import visStoreLayer from './layers/layer.vue';
import visStorePage from './pages/page.vue';

/**
 * 页面和图层容器组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-page-container',
  components: {
    visStorePage,
    visStoreLayer
  },
  setup() {
    // #region 页面高度拖动
    const pagesHeight = ref(300);
    const isDragging = ref(false);
    const startY = ref(0);
    const startHeight = ref(0);

    // 开始拖动
    const onDragStart = (event: MouseEvent) => {
      isDragging.value = true;
      startY.value = event.clientY;
      startHeight.value = pagesHeight.value;

      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', onDragMove);
      document.addEventListener('mouseup', onDragEnd);

      // 阻止默认行为
      event.preventDefault();
    };

    // 拖动过程中
    const onDragMove = (event: MouseEvent) => {
      if (!isDragging.value) return;

      const deltaY = event.clientY - startY.value;
      let newHeight = startHeight.value + deltaY;

      // 限制高度范围 100px - 600px
      newHeight = Math.max(100, Math.min(600, newHeight));

      pagesHeight.value = newHeight;
    };

    // 结束拖动
    const onDragEnd = () => {
      isDragging.value = false;

      // 移除全局鼠标事件监听
      document.removeEventListener('mousemove', onDragMove);
      document.removeEventListener('mouseup', onDragEnd);
    };
    //#endregion

    return {
      pagesHeight,
      isDragging,
      onDragStart
    };
  }
});
