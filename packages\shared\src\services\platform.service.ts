import { ACLResource } from '@hetu/acl';
import { SettingsService } from '@hetu/theme';
import { getQueryString } from '../utils';

export enum PlatformACLResourceCode {
  /** 新建团队 */
  Create = 'create',
  /** 退出团队 */
  Quit = 'quit',
  /** 素材库 */
  Material = 'material',
  /** 资源广场 */
  Market = 'market',
  /** 帮助文档 */
  Doc = 'doc'
}

export class PlatformACLResource extends ACLResource<typeof PlatformACLResourceCode> {
  readonly group: string = 'platform';

  constructor() {
    super();
    this.resourceCode = PlatformACLResourceCode;
  }
}

export class PlatformService {
  static acl = new PlatformACLResource();

  /** 主域名, 来源于配置 */
  static get domain() {
    return SettingsService.app.domain;
  }

  /** 云平台模式: 支持注册, 短信验证, 短信邀请等功能 */
  static get isCloud() {
    return !!SettingsService.app.cloud;
  }

  /** 上下文路径, 来源于配置 */
  static get contextpath() {
    const path: string = SettingsService.app.contextpath;
    return path ? (path.startsWith('/') ? path : `/${path}`) : '';
  }

  /** 用于 dev 模式中对部分判断调试时使用 */
  static get isDevCloud() {
    if (import.meta.env.DEV) {
      return this.isCloud || !!getQueryString('devCloud');
    }
    return this.isCloud;
  }
}
