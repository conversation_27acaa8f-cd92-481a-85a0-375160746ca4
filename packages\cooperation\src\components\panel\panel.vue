<template>
  <q-dialog ref="dialogRef" v-close-popup @before-show="loadMember">
    <q-card class="w-150 !max-w-full q-pa-md">
      <div class="flex items-center justify-between q-mb-md text-lg no-wrap">
        <span class="ellipsis">{{ cooperationFile.title || '' }} - 协作者</span>
        <q-icon name="close" size="16px" class="ht-link" @click="onDialogCancel()" />
      </div>

      <q-input
        v-model="query"
        class="ht-field ht-field--medium ht-field--light q-pb-md"
        placeholder="搜索成员"
        dense
        standout
        clearable
        clear-icon="close"
      >
        <template v-slot:prepend>
          <q-icon name="search" size="20px" />
        </template>
      </q-input>

      <q-scroll-area class="full-width h-120 text-font-regular">
        <div class="q-pb-md text-body1">未加入</div>
        <div class="q-gutter-y-sm">
          <template v-if="loading">
            <div v-for="n in 2" :key="n" class="row items-center q-pa-xs">
              <div class="col-5 flex items-center">
                <q-skeleton size="36px" type="circle" />
                <q-skeleton class="q-ml-sm" height="18px" width="60%" />
              </div>
              <div class="col-4 flex justify-center">
                <q-skeleton height="18px" width="50%" />
              </div>
              <div class="col-3 flex justify-end">
                <q-skeleton height="18px" width="50%" />
              </div>
            </div>
          </template>
          <ht-empty v-else-if="!notJoinProfiles.length" description="暂无数据" size="60px" />
          <template v-else>
            <div
              v-for="profile in notJoinProfiles"
              :key="profile.id"
              class="row items-center q-pa-xs rounded-1 duration-300 hover:bg-grey-light"
            >
              <div class="col-5 flex items-center">
                <q-avatar size="36px" class="bg-font-placeholder" text-color="white">
                  <img v-if="avatarUrl(profile.avatar)" :src="avatarUrl(profile.avatar)" />
                  <span v-else>{{ profile.fullName.toUpperCase().charAt(0) }}</span>
                </q-avatar>
                <span class="q-ml-sm">{{ profile.fullName }}</span>
              </div>
              <div class="col-4 text-center">{{ profile.cellphone || profile.email || '-' }}</div>
              <div class="col-2 offset-1 q-pl-lg">
                <div class="ht-link" v-ca:[cooperationFile.privilegeId]="(isSub ? subAcl : acl).addCooperation">
                  <span class="q-mr-xs">添加</span>
                  <q-icon name="expand_more" size="xs" />
                  <q-menu :offset="[0, 5]" anchor="bottom right" self="top right">
                    <q-list class="q-py-sm" dense>
                      <q-item
                        v-for="permission in permissions"
                        :key="permission.id"
                        @click="handleCommand(permission.id, profile)"
                        clickable
                      >
                        <q-item-section>{{ permission.privilegeName.split('_')[0] }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </div>
              </div>
            </div>
          </template>
        </div>

        <template v-if="isParentPrivilege">
          <div class="q-py-md text-body1">上级文件夹协作者</div>
          <div class="q-gutter-y-sm">
            <template v-if="privilegeLoading">
              <div v-for="n in 2" :key="n" class="row items-center q-pa-xs">
                <div class="col-5 flex items-center">
                  <q-skeleton size="36px" type="circle" />
                  <q-skeleton class="q-ml-sm" height="18px" width="60%" />
                </div>
                <div class="col-4 flex justify-center">
                  <q-skeleton height="18px" width="50%" />
                </div>
                <div class="col-3 flex justify-end">
                  <q-skeleton height="18px" width="50%" />
                </div>
              </div>
            </template>
            <ht-empty v-else-if="!parentPrivileges.length" description="暂无数据" size="60px" />
            <template v-else>
              <div
                v-for="privilege in parentPrivileges"
                :key="privilege.id"
                class="row items-center q-pa-xs rounded-1 duration-300 hover:bg-grey-light"
              >
                <div class="col-5 flex items-center">
                  <q-avatar size="36px" class="bg-font-placeholder" text-color="white">
                    <img v-if="avatarUrl(privilege.avatar)" :src="avatarUrl(privilege.avatar)" />
                    <span v-else>{{ privilege.fullName.toUpperCase().charAt(0) }}</span>
                  </q-avatar>
                  <span class="q-ml-sm">{{ privilege.fullName }}</span>
                </div>
                <div class="col-4 text-center">{{ privilege.cellphone || privilege.email || '-' }}</div>
                <div class="col-2 offset-1 q-pl-lg">{{ getPrivilegeName(privilege) }}</div>
              </div>
            </template>
          </div>
        </template>

        <div class="q-py-md text-body1">当前协作者</div>
        <div class="q-gutter-y-sm">
          <template v-if="privilegeLoading">
            <div v-for="n in 2" :key="n" class="row items-center q-pa-xs">
              <div class="col-5 flex items-center">
                <q-skeleton size="36px" type="circle" />
                <q-skeleton class="q-ml-sm" height="18px" width="60%" />
              </div>
              <div class="col-4 flex justify-center">
                <q-skeleton height="18px" width="50%" />
              </div>
              <div class="col-3 flex justify-end">
                <q-skeleton height="18px" width="50%" />
              </div>
            </div>
          </template>
          <ht-empty v-else-if="!currentPrivileges.length" description="暂无数据" size="60px" />
          <template v-else>
            <div
              v-for="privilege in currentPrivileges"
              :key="privilege.id"
              class="row items-center q-pa-xs rounded-1 duration-300 hover:bg-grey-light"
            >
              <div class="col-5 flex items-center">
                <q-avatar size="36px" class="bg-font-placeholder" text-color="white">
                  <img v-if="avatarUrl(privilege.avatar)" :src="avatarUrl(privilege.avatar)" />
                  <span v-else>{{ privilege.fullName.toUpperCase().charAt(0) }}</span>
                </q-avatar>
                <span class="q-ml-sm">{{ privilege.fullName }}</span>
              </div>
              <div class="col-4 text-center">{{ privilege.cellphone || privilege.email || '-' }}</div>
              <div class="col-2 offset-1 q-pl-lg">
                <div v-if="!getIsChangePermission(privilege)">{{ getPrivilegeName(privilege) }}</div>
                <div v-else class="ht-link">
                  <template v-if="canToolbar()">
                    <span class="q-mr-xs">{{ getPrivilegeName(privilege) }}</span>
                    <q-icon name="expand_more" size="xs" />
                    <q-menu :offset="[0, 5]" anchor="bottom right" self="top right">
                      <q-list class="q-py-sm" dense>
                        <q-item
                          v-for="permission in permissions"
                          :key="permission.id"
                          @click="handleCommand(permission.id, privilege)"
                          clickable
                        >
                          <q-item-section>{{ permission.privilegeName.split('_')[0] }}</q-item-section>
                        </q-item>
                        <q-item @click="handleCommand('remove', privilege)" clickable>
                          <q-item-section>移除</q-item-section>
                        </q-item>
                      </q-list>
                    </q-menu>
                  </template>
                  <span v-else>{{ getPrivilegeName(privilege) }}</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </q-scroll-area>
    </q-card>
  </q-dialog>
</template>

<script src="./panel" lang="ts"></script>
