/**
 * 组件配置类
 * 用于定义和管理可视化组件的基本属性和配置
 */
export class WidgetBase {
  /** 组件名称 */
  name!: string;

  /** 组件标题 */
  title: string = '';

  /** 组件类型 */
  type!: string;

  /** 环境，默认prod */
  env = 'prod';

  /** 分组 */
  group?: string | string[];

  constructor(type?: string, title?: string, name?: string, group?: string | string[]) {
    this.type = type || this.type;
    this.title = title || this.title;
    this.name = name || this.name;
    this.group = group || this.group;
  }
}

/**
 * 组件分组信息
 */
export interface WidgetGroup {
  /** 分组标题 */
  title: string;
  /** 分组图标 */
  icon: string;
  /** 子分组 */
  subGroups?: Record<string, WidgetGroup>;
  /** 组件 */
  widgets?: WidgetBase[];
  /** 是否显示 */
  isShow?: boolean;
}

/**
 * 分组
 */
export const WidgetGroupBase = {
  words: {
    title: '文字',
    icon: 'vi-words',
    subGroups: {
      interactive: {
        title: '交互',
        icon: 'vi-interactive'
      }
    }
  }
};
