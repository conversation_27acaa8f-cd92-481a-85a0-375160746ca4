/**
 * 菜单链接类型
 */
export enum MenuLinkType {
  Route = 'ROUTE',
  Url = 'URL'
}

/**
 * 菜单链接打开方式
 */
export enum MenuOpenTarget {
  Route = '',
  Blank = 'blank',
  Self = 'self',
  IFrame = 'iframe'
}

export interface Menu {
  id?: string;

  /** 名称 */
  menuName: string;

  /** 编码 */
  menuCode: string | number;

  /** 图标 */
  menuIcon?: string;

  /** 链接地址 */
  menuLink?: string;

  /** 链接类型 */
  linkType?: MenuLinkType;

  /** 打开方式 */
  target?: MenuOpenTarget;

  /** 上级id */
  parentId?: string;

  /** 下级菜单 */
  children?: Menu[];

  [key: string]: any;
}
