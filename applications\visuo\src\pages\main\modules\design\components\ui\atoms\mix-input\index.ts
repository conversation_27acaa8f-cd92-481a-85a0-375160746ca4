import { throttle } from 'quasar';
import { computed, defineComponent, onMounted, ref, watch } from 'vue';

/**
 * 混合输入框，可输入数字、字符串
 * 支持min、max设置范围
 * 支持diasble设置禁用状态
 * 在图标上拖动可改变数值
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-mix-input',
  props: {
    modelValue: {
      type: [Number, String],
      required: true
    },
    icon: {
      type: String
    },
    // 数值框的最小值和最大值
    min: {
      type: Number
    },
    max: {
      type: Number
    },
    // 步长
    step: {
      type: Number,
      default: 1
    },
    // 节流控制
    throttle: {
      type: Number,
      default: 0
    },
    placeholder: {
      type: String
    },
    disabled: {
      type: Boolean
    },
    readonly: {
      type: Boolean
    }
  },
  // 指令阻止原生change事件
  directives: {
    'stop-change': {
      mounted(el) {
        const input = el.querySelector('input');
        input.addEventListener(
          'change',
          (e: Event) => {
            e.stopImmediatePropagation();
            e.preventDefault();
          },
          true
        );
      }
    }
  },
  setup(props, { emit }) {
    // 调整灵敏度（值越小，拖动变化越慢）
    const sensitivity = 0.5;

    const numberInput = ref();
    const numberValue = ref(props.modelValue);
    watch(
      () => props.modelValue,
      (val) => {
        numberValue.value = val;
      }
    );

    const handleUpdate = (val: string) => {
      emit('update:modelValue', isNumeric(val) ? Number(val) : val);
      // 触发change事件
      if (`${val}` === `${props.modelValue}`) return;
      emit(
        'change',
        isNumeric(val) ? Number(val) : val,
        isNumeric(props.modelValue + '') ? Number(props.modelValue) : props.modelValue
      );
    };

    const isIconFont = computed(() => {
      return props.icon?.startsWith('hticon-vis');
    });

    // #region 拖动图标改变数值
    onMounted(() => {
      // 查找到当前输入框的图标
      const dragHandle = numberInput.value.$el.querySelector('.drag-icon');

      const isDragging = ref(false);
      const startX = ref(0);
      const startValue = ref(0);

      // 鼠标按下时开始监听拖动
      dragHandle?.addEventListener('mousedown', (e: any) => {
        if (props.readonly) return;

        isDragging.value = true;
        startX.value = e.clientX;
        startValue.value = Number(numberValue.value) || 0;

        document.addEventListener('mousemove', handleDrag);
        document.addEventListener('mouseup', stopDrag);

        // 防止选中文本
        e.preventDefault();
      });

      // 鼠标移动时计算拖动的距离并更新数值
      const handleDrag = throttle((e: MouseEvent) => {
        if (!isDragging.value) return;

        // 保持鼠标样式
        document.body.classList.add('dragging');

        const deltaX = e.clientX - startX.value;

        const newValue = Number.isInteger(props.step)
          ? Math.ceil(startValue.value + deltaX * sensitivity)
          : parseFloat((startValue.value + deltaX * sensitivity * props.step).toFixed(2));

        // 确保数值在 min 和 max 范围内
        const min = props.min !== undefined ? props.min : -Infinity;
        const max = props.max !== undefined ? props.max : Infinity;
        numberValue.value = Math.min(max, Math.max(min, newValue));

        handleUpdate(`${numberValue.value}`);
      }, props.throttle);

      // 鼠标松开时停止监听
      const stopDrag = () => {
        document.body.classList.remove('dragging');

        isDragging.value = false;
        document.removeEventListener('mousemove', handleDrag);
        document.removeEventListener('mouseup', stopDrag);
      };
    });

    // #endregion

    const oldValue = ref(numberValue.value);
    const handleFocus = (e: Event) => {
      if ((e.target as HTMLElement).className.includes('q-btn')) {
        return;
      }
      oldValue.value = numberValue.value;
      numberInput.value && numberInput.value.select();
    };

    const handleBlur = () => {
      if (isNumeric(`${numberValue.value}`)) {
        const value = Number(numberValue.value);
        if (value === undefined) {
          numberValue.value = Number(oldValue.value);
        } else if (props.min !== undefined && value < props.min) {
          numberValue.value = Number(props.min);
        } else if (props.max !== undefined && value > props.max) {
          numberValue.value = Number(props.max);
        }
        oldValue.value = numberValue.value;
      }

      handleUpdate(`${numberValue.value}`);
    };

    /**
     * 判断是不是字符串数字
     * @param str
     * @returns
     */
    const isNumeric = (str: string) => {
      return !isNaN(Number(str)) && !isNaN(parseFloat(str));
    };
    return {
      numberInput,
      numberValue,
      handleUpdate,
      isIconFont,

      handleFocus,
      handleBlur
    };
  }
});
