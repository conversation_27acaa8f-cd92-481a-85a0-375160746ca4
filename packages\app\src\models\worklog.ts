import { BaseModel, OrderType } from '@hetu/http';

/**
 * 排序方式
 */
export interface OrderColumn {
  column: string;
  sorting: OrderType;
}
/**
 * 操作记录
 */
export interface OperateLog extends BaseModel {
  // 操作资源唯一标识
  objectId: string;
  // 资源名称
  objectName: string;
  // 类型(大屏/数据集等)
  objectType: string;
  // 二级分类
  objectCategory: string;
  // 工作操作描述
  operateDesc: string;
  // 操作者姓名
  creatorName: string;
  // 操作者id
  creatorId: string;
  // 扩展数据
  extendData: string;

  [key: string]: any;
}
