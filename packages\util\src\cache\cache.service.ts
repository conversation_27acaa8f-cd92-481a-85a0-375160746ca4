import { createSingleClass } from '@hetu/core';
import { HttpClient, responseResult } from '@hetu/http';
import { type Cache, type CacheStore, CacheStoreType } from './interface';
import { LocalStorageStore } from './local-storage';
import { SessionStorageStore } from './session-storage';

class CacheServiceCtor {
  private readonly prefix = `tui_`;

  private get metaKey(): string {
    return `${this.prefix}cache_meta`;
  }

  private readonly memory: Map<string, any> = new Map<string, any>();

  private readonly local: CacheStore = new LocalStorageStore();

  private readonly session: CacheStore = new SessionStorageStore();

  private get http() {
    return HttpClient;
  }

  constructor() {
    this.loadMeta();
  }

  /**
   * 持久化缓存 `Promise` 对象, 例如：
   * - `set('data/1', HttpClient.get('data/1'))`
   * - `set('data/1', HttpClient.get('data/1'), { version: '1.0' })`
   */
  set<T = any>(key: string, data: Promise<T>, options?: { type?: CacheStoreType.Local; version?: string }): Promise<T>;

  /**
   * 持久化缓存基础对象, 例如：
   * - `set('data/1', 1)`
   * - `set('data/1', 1, { version: '1.0' })`
   */
  set(key: string, data: any, options?: { type?: CacheStoreType.Local; version?: string }): void;

  /**
   * 指定缓存类型进行缓存对象, 例如内存缓存：
   * - `set('data/1', 1, { type: 'm' })`
   * - `set('data/1', 1, { type: 'm', version: '1.0' })`
   */
  set(key: string, data: any, options: { type: CacheStoreType; version?: string }): void;

  /**
   * 缓存对象
   */
  set(
    key: string,
    data: any | Promise<any>,
    options: {
      /** 存储类型 */
      type?: CacheStoreType;
      /** 版本 */
      version?: string;
    } = {}
  ): any {
    options.type = options.type || CacheStoreType.Local;
    if (!(data instanceof Promise)) {
      this.save(options.type, key, { v: data, s: options.version });
      return;
    }

    return data.then((v: any) => {
      this.save(options.type!, key, { v, s: options.version });
    });
  }

  /** 获取缓存数据, 若 `key` 不存在则 `key` 作为HTTP请求缓存后返回 */
  get<T = any>(
    key: string,
    options?: {
      mode: 'promise';
      type?: CacheStoreType;
      version?: string;
    }
  ): Promise<T>;

  /** 获取缓存数据, 若 `key` 不存在或版本不一致则返回 null */
  get(
    key: string,
    options: {
      mode: 'none';
      type?: CacheStoreType;
      version?: string;
    }
  ): any;

  get(
    key: string,
    options: {
      mode?: 'promise' | 'none';
      type?: CacheStoreType;
      version?: string;
    } = {}
  ): Promise<any> | any {
    const isPromise = options.mode !== 'none';
    let value: Cache = JSON.parse('null');

    switch (options.type) {
      case CacheStoreType.Memory:
        this.memory.has(key) && (value = this.memory.get(key) as Cache);
        break;
      case CacheStoreType.Session:
        this.session.meta.has(key) && (value = this.session.get(this.prefix + key));
        break;
      case CacheStoreType.Local:
        this.local.meta.has(key) && (value = this.local.get(this.prefix + key));
        break;
      default:
        value = this.memory.has(key)
          ? (this.memory.get(key) as Cache)
          : this.session.meta.has(key)
          ? this.session.get(this.prefix + key)
          : this.local.get(this.prefix + key);
        break;
    }

    if (!value || (value.s && value.s !== options.version)) {
      if (isPromise) {
        return this.http
          .get(key)
          .then(responseResult)
          .then((data: any) => {
            this.set(key, data, {
              type: options.type as CacheStoreType,
              version: options.version
            });
            return data;
          });
      }
      return null;
    }

    return isPromise ? new Promise((resolve) => resolve(value.v)) : value.v;
  }

  /** 获取缓存数据, 若 `key` 不存在或版本不一致则返回 null */
  getNone<T = any>(key: string): T {
    return this.get(key, { mode: 'none' });
  }

  /** 是否已缓存 `key` */
  has(key: string): boolean {
    return this.memory.has(key) || this.session.meta.has(key) || this.local.meta.has(key);
  }

  /** 移除缓存 */
  remove(key: string) {
    if (this.memory.has(key)) {
      this.memory.delete(key);
      return;
    }

    const storeKey = this.prefix + key;
    if (this.session.meta.has(key)) {
      this.session.remove(storeKey);
      this.removeMeta(key, this.session);
      return;
    }

    this.local.remove(storeKey);
    this.removeMeta(key, this.local);
  }

  /** 清空所有缓存 */
  clear() {
    this.memory.clear();
    this.session.meta.forEach((key) => this.session.remove(this.prefix + key));
    this.local.meta.forEach((key) => this.local.remove(this.prefix + key));
    this.clearMeta();
  }

  private pushMeta(key: string, store: CacheStore) {
    if (store.meta.has(key)) {
      return;
    }

    store.meta.add(key);
    this.saveMeta(store);
  }

  private removeMeta(key: string, store: CacheStore) {
    if (!store.meta.has(key)) {
      return;
    }

    store.meta.delete(key);
    this.saveMeta(store);
  }

  private clearMeta() {
    this.local.meta.clear();
    this.local.remove(this.metaKey);
    this.session.meta.clear();
    this.session.remove(this.metaKey);
  }

  private loadMeta() {
    let ret = this.local.get(this.metaKey);
    if (ret && ret.v) {
      (ret.v as string[]).forEach((key) => this.local.meta.add(key));
    }

    ret = this.session.get(this.metaKey);
    if (ret && ret.v) {
      (ret.v as string[]).forEach((key) => this.session.meta.add(key));
    }
  }

  private saveMeta(store: CacheStore) {
    const metaData: string[] = [];
    store.meta.forEach((key) => metaData.push(key));
    store.set(this.metaKey, { v: metaData });
  }

  private save(type: CacheStoreType, key: string, value: Cache) {
    let store;
    switch (type) {
      case CacheStoreType.Local:
        store = this.local;
        break;
      case CacheStoreType.Session:
        store = this.session;
        break;
      case CacheStoreType.Memory:
        this.memory.set(key, value);
        break;
      default:
        break;
    }

    if (store) {
      store.set(this.prefix + key, value);
      this.pushMeta(key, store);
    }
  }
}

/**
 * 缓存服务类 [单例]
 * <AUTHOR>
 */
export const CacheService = createSingleClass(CacheServiceCtor);
