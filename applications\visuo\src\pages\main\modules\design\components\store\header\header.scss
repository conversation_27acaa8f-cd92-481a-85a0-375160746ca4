.#{$vis-prefix}-store-header {
  @apply w-full h-12 flex flex-col;

  .q-btn {
    outline: none !important;
    box-shadow: none !important;

    &:hover {
      background-color: transparent !important;
    }
  }

  &-title {
    font-size: 14px;
    width: fit-content;
    margin: 11.5px 12px;
    flex: 1;

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      min-width: 0;
      max-width: 160px;
      flex: 1;
    }

    .vis-icon {
      @apply mx-1;
    }

  }


  &-item {
    padding: 2px 4px;
  }

  &-tree {
    @apply w-96 h-full m-2;
    font-size: 12px;

    &-label {
      @apply lh-8 h-8 text-ellipsis whitespace-nowrap overflow-hidden text-gray-800;
    }
  }
}