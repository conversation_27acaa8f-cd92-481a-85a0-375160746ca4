import type { RouteLocationNormalized } from 'vue-router';
import type { ACLType, ACLCanType, ACLDataType, ACLModeType } from './acl';
import { NavService } from '@hetu/theme';
import { useACLConfig } from './config';

/**
 * 权限访问控制服务
 * @static
 * <AUTHOR>
 */
export class ACLService {
  private static full = false;

  /** 用户角色 */
  private static roles: string[] = [];

  /**
   * 已授权资源
   * - key 为资源组 code, value 为该组中已授权资源集合
   */
  private static resources: Record<string, string[]> = {};

  private static get config() {
    return useACLConfig();
  }

  /** 无权限路由页面地址, 默认: `/403` */
  static get guardUrl(): string {
    return this.config.guardUrl!;
  }

  /**
   * 标识当前用户权限为全量, 即不受限
   * @param value 是否全量
   */
  static setFull(value: boolean): void {
    this.full = value;
  }

  /**
   * 设置当前用户角色或权限能力(会先清除所有)
   * @param value 当前登录用户角色及已授权资源
   */
  static set(value: ACLDataType): void {
    this.roles = [];
    this.resources = {};
    this.add(value);
  }

  /**
   * 为当前用户增加角色或权限能力
   * @param value 当前登录用户角色及已授权资源
   */
  static add(value: ACLDataType): void {
    if (value.roles && value.roles.length) {
      this.roles.push(...value.roles);
    }
    if (value.resources && Object.keys(value.resources).length) {
      Object.assign(this.resources, value.resources);
    }
  }

  /**
   * 权限验证: 验证当前用户是否有对应角色或资源
   * @param acl 验证参数
   * - 当 `full: true` 或参数值为 `null` 时返回 `true`
   * - 若使用 `ACLType` 参数, 可以指定 `mode` 校验模式
   */
  static can(acl?: ACLCanType | null): boolean {
    const { preCan } = this.config;
    if (preCan) {
      acl = preCan(acl);
    }

    if (this.full === true || !acl) {
      return true;
    }

    const t = this.parseACLType(acl);
    const result = this.checkByRole(t) || this.checkByResource(t);

    return t.except === true ? !result : result;
  }

  /**
   * 验证当前用户是否有对应路由权限: 基于导航菜单数据进行验证
   * - 当 `full: true` 时返回 `true`
   * - 当 `meta` 中有参数 `acl: false` 时返回 `true`
   * - 当导航菜单数据未初始化时返回 `true`
   * @param route 路由
   * @returns
   */
  static canRoute(route: RouteLocationNormalized) {
    if (this.full === true || route.meta?.acl === false || !NavService.initialized) {
      return true;
    }
    const menu = NavService.getMenuFromRoute(route);
    return !!menu;
  }

  private static checkByRole(t: ACLType) {
    if (t.role && t.role.length) {
      const fn = (v: string) => this.roles.includes(v);
      return this.checkByMode(t.mode as ACLModeType, t.role, fn);
    }

    return false;
  }

  private static checkByResource(t: ACLType) {
    if (t.resource && Object.keys(t.resource).length) {
      const all = t.resource;
      const groups = Object.keys(all);

      const callback = (group: string) => {
        const codes = all[group];
        const fn = (v: string) => this.resources[group]?.includes(v);
        return typeof codes === 'string' ? fn(codes) : this.checkByMode(t.mode as ACLModeType, codes, fn);
      };

      return this.checkByMode(t.mode as ACLModeType, groups, callback);
    }

    return false;
  }

  private static checkByMode(mode: ACLModeType, datas: string[], cb: (v: string) => {}) {
    if (mode === 'allOf') {
      return datas.every(cb);
    }
    return datas.some(cb);
  }

  private static parseACLType(val?: ACLCanType | null): ACLType {
    let t: ACLType;
    if (typeof val === 'string') {
      t = { role: [val] };
    } else if (Array.isArray(val) && val.length && typeof val[0] === 'string') {
      t = { role: val };
    } else if (typeof val === 'object' && !Array.isArray(val)) {
      t = { ...val };
    } else {
      t = { resource: {}, role: [] };
    }

    return { except: false, ...t };
  }
}
