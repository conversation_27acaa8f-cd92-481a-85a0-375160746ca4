@import '../../../../index.scss';

.#{$vis-prefix}-config-data {
  @apply py-2 px-3 flex items-center justify-between;

  &__tree {
    flex: 0 0 calc(100% - 60px);
    flex-shrink: 1;
  }

  &__match {
    @apply flex flex-wrap;

    &-name {
      @apply w-full flex items-center justify-between;
      padding: $primary-margin;
      font-size: $primary-font-size;
      font-weight: $title-font-weight;

    }

    &-field {
      @apply flex flex-wrap w-full;

      padding: 0 $primary-margin $primary-margin $primary-margin;

      // 拖拽容器样式
      .container,
      .draggable-container {
        @apply w-full;
        display: flex;
        flex-direction: column;
        gap: $field-gap;
        margin-bottom: $field-gap;
      }

      .container {
        &:last-child {
          margin-bottom: 0;
        }
      }

      .drag-handle {
        position: absolute;
        left: -$primary-margin;
        top: 50%;
        transform: translateY(-50%);
        width: 10px;
        height: 24px;
        cursor: grab;
        opacity: 0;
        transition: opacity 0.2s ease;

        &::before {
          content: '';
          position: absolute;
          left: 4px;
          top: 55%;
          transform: translateY(-50%);
          width: 6px;
          height: 10px;
          line-height: 24px;
          background-image: radial-gradient(circle at 1px 1px, #666 1px, transparent 1px),
            radial-gradient(circle at 1px 1px, #666 1px, transparent 1px),
            radial-gradient(circle at 1px 1px, #666 1px, transparent 1px),
            radial-gradient(circle at 1px 1px, #666 1px, transparent 1px),
            radial-gradient(circle at 1px 1px, #666 1px, transparent 1px),
            radial-gradient(circle at 1px 1px, #666 1px, transparent 1px);
          background-position: 0 0, 3px 0, 0 3px, 3px 3px, 0 6px, 3px 6px;
          background-repeat: no-repeat;
          background-size: 2px 2px;
        }

        &:active {
          cursor: grabbing;
        }


      }


      // 字段项容器
      .field-item {
        @apply flex;
        gap: $field-gap;
        position: relative;
        border-radius: $border-radius;
        transition: all 0.2s ease;

        &:hover .drag-handle {
          opacity: 1;
        }

        .drag-handle {
          opacity: 0;
        }
      }


      // 拖拽状态样式
      .ghost {
        .field-btn {
          opacity: 0.4;
          background-color: transparent;
          border: 2px dashed $select-active-color;
          color: $select-active-color;
        }
      }

      .chosen {
        z-index: 9999;
      }

      .drag {
        opacity: 0.8;

        .field-btn {
          transform: scale(1.1);
          box-shadow: 0 6px 18px rgba(0, 0, 0, 0.2);
        }

        // 拖拽时隐藏移除按钮
        .remove-btn {
          display: none;
        }
      }

      .field-btn {
        @apply w-full justify-center;
        flex: 0 0 calc(100% - 64px);
        border-radius: 4px;
        border: 1px solid $input-hover-color;

        &:hover {
          background-color: rgba(0, 0, 0, 0);
          border: 1px solid $select-active-color;
        }

        &.active {
          border: 1px solid $select-active-color;
        }

        // 可拖拽按钮样式
        &.draggable-btn {
          cursor: grab;

          &:active {
            cursor: grabbing;
          }
        }
      }

      .add-field-btn {
        @apply w-full justify-center;
        flex: 0 0 calc(100% - 32px);
        border: 1px dashed $select-active-color;
        color: $select-active-color;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: $input-bg;
        }
      }
    }
  }
}