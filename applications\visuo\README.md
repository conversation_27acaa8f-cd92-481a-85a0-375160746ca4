# hetu-app-visuo

可视化设计应用

## Project setup

```
pnpm install
```

### Compiles and hot-reloads for development

```
pnpm serve
```

### Compiles and minifies for production

```
pnpm build
```

### Lints and fixes files

```
pnpm lint
```

### Git commit

> 需符合 [commitlint](https://github.com/conventional-changelog/commitlint/#what-is-commitlint) 提交规范, 否则不能提交

```
<type>(<scope>): <jiraId> <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

[参考说明](https://www.jianshu.com/p/201bd81e7dc9)

辅助命令

```
pnpm commit | pnpm exec git-cz
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
