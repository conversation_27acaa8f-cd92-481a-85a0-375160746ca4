import { useDesignStore } from '../../../../stores';
import { defineComponent, computed } from 'vue';
import VisDesignMoveable from '../moveable/moveable.vue';
import { type Page } from '@vis/document-core';

export default defineComponent({
  name: 'vis-design-canvas',
  components: {
    VisDesignMoveable
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();
    const horizontalGuidesRef = designStore.horizontalGuidesRef;
    const verticalGuidesRef = designStore.verticalGuidesRef;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;

    const page = computed(() => {
      return designStore.active.value.page as Page;
    });
    return {
      page
    };
  }
});
