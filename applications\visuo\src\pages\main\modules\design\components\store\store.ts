import { defineComponent } from 'vue';
import { VisStoreHeader, VisStoreBody, VisStoreSidebar } from './index';
import { useDesignStore } from '../../stores/design.store';

/**
 * 组件库面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-store',
  components: {
    VisStoreHeader,
    VisStoreBody,
    VisStoreSidebar
  },
  setup() {
    const { selectedMenu } = useDesignStore();
    
    return {
      selectedMenu
    };
  }
});
