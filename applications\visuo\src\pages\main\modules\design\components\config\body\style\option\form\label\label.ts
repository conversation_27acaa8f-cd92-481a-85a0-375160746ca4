import { Block, FontConfig, TextAdapt, TextAlign, useDocumentStore, WidgetBlock, type Label } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';
import { useDesignStore } from '../../../../../../../stores';
/**
 * 表单通用标签属性面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-label',
  props: {
    options: {
      type: Object as PropType<Label>,
      required: true
    }
  },
  setup(props) {
    const designStore = useDesignStore();
    const activeGraph = computed(() => designStore.active.value.graphs?.[0]);

    const docStore = useDocumentStore();

    const computedOptions = computed(() => props.options);

    const positionOptions = [
      {
        label: '左',
        value: 'left'
      },
      {
        label: '上',
        value: 'top'
      }
    ];

    // 标签位置改变时，调整组件高度
    const handlePosition = (val: string) => {
      if (val === 'top') {
        activeGraph.value.height *= 2;
      } else if (val === 'left') {
        activeGraph.value.height = Math.round(activeGraph.value.height / 2);
      }
    };

    // 对齐方式
    const alignOptions = [
      { value: TextAlign.Left, label: '', icon: 'vis-text-left', tip: '左对齐' },
      { value: TextAlign.Center, label: '', icon: 'vis-text-center', tip: '居中对齐' },
      { value: TextAlign.Right, label: '', icon: 'vis-text-right', tip: '右对齐' }
    ];

    // #region 弹窗区域
    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const fontFamilyOptions = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });
    const { fontWeights: fontWeightOptions, fontSizes: fontSizeOptions } = new FontConfig();

    // 展示模式
    const adaptOptions = [
      { value: TextAdapt.Single, label: '', icon: 'vis-single', tip: '单行模式' },
      { value: TextAdapt.Ellipsis, label: '', icon: 'vis-ellipsis', tip: '省略文本' },
      { value: TextAdapt.Fixed, label: '', icon: 'vis-fixed', tip: '固定宽高' }
    ];

    const adaptSingle = computed(() => {
      return computedOptions.value.font.adapt === TextAdapt.Single;
    });
    // 内边距
    const showMenuPadding = ref(false);

    const calcSingle = () => {
      // 判断数组中的值是否完全一致
      return computedOptions.value.padding.every((item) => item === computedOptions.value.padding[0]);
    };
    const isSingle = ref(calcSingle());

    const padding = ref(computedOptions.value.padding[0]);
    const updatePadding = (val: number) => {
      computedOptions.value.padding = [val, val, val, val];
    };

    const handlePadding = (single: boolean) => {
      isSingle.value = single;

      if (!single) {
        padding.value = computedOptions.value.padding[0];
        computedOptions.value.padding = [padding.value, padding.value, padding.value, padding.value];
      }
      showMenuPadding.value = false;
    };

    // #endregion

    return {
      computedOptions,
      positionOptions,
      handlePosition,
      alignOptions,

      popupRef,
      popupShow,
      showPopup,

      fontFamilyOptions,
      fontWeightOptions,
      fontSizeOptions,
      adaptOptions,
      adaptSingle,

      showMenuPadding,
      isSingle,
      padding,
      updatePadding,
      handlePadding
    };
  }
});
