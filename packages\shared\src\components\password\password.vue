<template>
  <q-dialog ref="dialogRef" :persistent="true">
    <q-card class="q-dialog-plugin">
      <q-card-section class="q-dialog__title">{{ title || '验证' }}</q-card-section>

      <q-card-section class="q-py-none">
        <q-form @submit.prevent ref="formRef">
          <div>密码</div>
          <q-input
            v-model="params.accessPassword"
            type="password"
            placeholder="请输入密码"
            :rules="rules.accessPassword"
            lazy-rules
            outlined
            dense
          />
          <template v-if="validateCaptcha">
            <div>验证码</div>
            <q-input
              v-model="params.captchaCode"
              placeholder="请输入验证码"
              :rules="rules.captchaCode"
              lazy-rules="ondemand"
              ref="codeInputRef"
              outlined
              dense
              @blur="codeInputRef?.validate()"
            >
              <template v-slot:append>
                <q-img class="!w-27 cursor-pointer" :src="code.code" @click.stop="fetchCodeImg()">
                  <template v-slot:error>加载失败</template>
                </q-img>
              </template>
            </q-input>
          </template>
        </q-form>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn label="确定" color="primary" unelevated @click="submit" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" src="./password"></script>
