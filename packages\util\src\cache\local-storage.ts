import type { Cache, CacheStore } from './interface';

/**
 * 持久化存储服务类
 * <AUTHOR>
 */
export class LocalStorageStore implements CacheStore {
  meta: Set<string> = new Set<string>();

  get(key: string): Cache {
    return JSON.parse(localStorage.getItem(key) || 'null') || null;
  }

  set(key: string, value: Cache): boolean {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  }

  remove(key: string) {
    localStorage.removeItem(key);
  }
}
