import { computed, defineComponent, type PropType } from 'vue';
import type { ACLResource } from '@hetu/acl';
import { CooperationACLService } from '../../services';
import type { CooperationFile } from '../../models';
import { useFilePageStore } from '@hetu/platform-app';

/**
 * 文件信息中的协作权限信息
 */
export default defineComponent({
  name: 'ht-co-file-info-item',
  props: {
    /** 文件 */
    file: {
      type: Object as PropType<CooperationFile>,
      required: true
    },
    acl: {
      type: Object as PropType<ACLResource<any>>,
      required: true
    }
  },
  setup(props) {
    const store = useFilePageStore();

    const privilegeName = computed(() => {
      const privilegeId = props.file?.privilegeId;
      if (privilegeId) {
        const permission = CooperationACLService.get(privilegeId);
        return permission ? permission.privilegeName.split('_')[0] : '';
      }

      return '';
    });

    const onOpen = () => store.setAction('openCoPanel', [props.file]);

    return {
      privilegeName,

      onOpen
    };
  }
});
