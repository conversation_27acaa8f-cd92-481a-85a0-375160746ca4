import { MessageManageService } from './message-manage.service';

type UnreadMessageObserver = (messageCount: number) => void;

export class MessageSubscription {
  /** 通知回调 */
  next: UnreadMessageObserver;

  constructor(fn: UnreadMessageObserver) {
    this.next = fn;
  }
}

/** 消息订阅服务 */
export class MessageObserveService {
  /** 是否启动监听 */
  private static enable = false;
  private static timeoutId?: number;
  private static routerWatcher?: VoidFunction;
  private static visibilityWatcher?: VoidFunction;

  private static data?: number;
  private static loading = false;

  /** 订阅列表 */
  private static subscriptions: MessageSubscription[] = [];

  /** 订阅 */
  static subscribe(observer: UnreadMessageObserver) {
    // 根据 `observer` 创建一个订阅，
    // 在该订阅被退订时，从当前订阅列表中移除
    const subscription = new MessageSubscription(observer);
    this.subscriptions.push(subscription);
    this.data && subscription.next(this.data);

    this.start();

    return subscription;
  }

  /** 手动执行 */
  static next() {
    this.loadMessage();
  }

  /** 退订订阅 */
  static unsubscribe(subscription: MessageSubscription) {
    const indexOf = this.subscriptions.indexOf(subscription);

    indexOf > -1 && this.subscriptions.splice(indexOf, 1);

    if (!this.subscriptions.length) {
      this.stop();
    }
  }

  /** 开启监听 */
  static start() {
    // 需避免重复监听
    if (this.enable) return;

    // this.enable = true;

    // 立即调用
    this.data === undefined && this.next();
  }

  /** 停止监听 */
  static stop() {
    if (!this.enable) return;

    this.routerWatcher?.();
    this.routerWatcher = undefined;

    clearTimeout(this.timeoutId);
    this.data = undefined;

    this.enable = false;
  }

  static subscribeVisibilityChange() {
    let timer: number;

    this.visibilityWatcher = () => {
      clearTimeout(timer);
      if (document.visibilityState === 'hidden') {
        // 浏览器页签隐藏三分钟后停止查询
        timer = window.setTimeout(() => this.stop(), 180000);
      } else {
        // 浏览器页签显示时如已停止会立即查询
        this.start();
      }
    };

    document.addEventListener('visibilitychange', this.visibilityWatcher);
  }

  static unsubscribeVisibilityChange() {
    this.visibilityWatcher && document.removeEventListener('visibilitychange', this.visibilityWatcher);
    this.visibilityWatcher = undefined;
  }

  /** 订阅时间周期 */
  private static subscribeTime() {
    this.timeoutId = window.setTimeout(() => this.loadMessage(), 60_000);
  }

  private static async loadMessage() {
    if (this.loading) return;

    this.loading = true;
    clearTimeout(this.timeoutId);

    try {
      await MessageManageService.unreadCount().then((data) => {
        this.data = data;
        this.subscriptions.forEach((sub) => sub.next(data));
      });
      // eslint-disable-next-line no-empty
    } catch (error) {}

    this.enable && this.subscribeTime();
    this.loading = false;
  }
}
