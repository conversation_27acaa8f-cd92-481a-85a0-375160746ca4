import { computed, defineComponent, ref, watch } from 'vue';
import VisConfigStyle from './style/style.vue';
import VisConfigData from './style/data/data.vue';
import VisConfigInteractive from './interactive/interactive.vue';
import VisConfigNode from './node/node.vue';
import { useDesignStore } from '../../../stores';

/**
 * 配置面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-body',
  components: {
    VisConfigStyle,
    VisConfigData,
    VisConfigInteractive,
    VisConfigNode
  },
  props: {},
  setup(props) {
    const tab = ref('style');

    const designStore = useDesignStore();

    const isNode = computed(() => ['style', 'interactive'].includes(tab.value));

    const rulerState = designStore.rulerState;

    const infiniteCanvasRef = designStore.infiniteCanvasRef;

    //#region: 画布缩放
    const zoomFormat = computed(() => {
      return parseInt(`${rulerState.value.zoom * 100}`) + '%';
    });

    const scale = ref(Math.round(rulerState.value.zoom * 100));

    const options = [50, 100, 120, 150];

    const step = ref(25);

    const setZoom = (zoom: number) => {
      const min = rulerState.value.zoomRange[0];
      const max = rulerState.value.zoomRange[1];

      if (zoom > max) {
        zoom = max;
      } else if (zoom < min) {
        zoom = min;
      }
      rulerState.value.zoom = zoom;
      infiniteCanvasRef.value?.setZoom(zoom);
    };
    const onScale = (plus: number) => {
      const isIn = (step.value * plus) / 100;
      const zoom = rulerState.value.zoom + isIn;
      setZoom(zoom);
    };

    const onChangeScale = (value: number) => {
      setZoom(value / 100);
    };

    watch(
      () => rulerState.value.zoom,
      (zoom: number) => {
        scale.value = Math.round(zoom * 100);
      }
    );
    //#endregion

    return {
      isNode,
      tab,

      scale,
      zoomFormat,
      options,
      setZoom,
      onScale,
      onChangeScale
    };
  }
});
