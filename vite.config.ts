import { type ConfigEnv, loadEnv, type UserConfig, normalizePath } from 'vite';
import vue from '@vitejs/plugin-vue';
import unocss from 'unocss/vite';
import { quasar, transformAssetUrls } from '@quasar/vite-plugin';
import { createMpaPlugin } from 'vite-plugin-virtual-mpa';
import { visualizer } from 'rollup-plugin-visualizer';
import { chunkSplitPlugin } from 'vite-plugin-chunk-split';
// import { viteMockServe } from 'vite-plugin-mock';
import { assetsDir, setupApps, copyAssets, Applications } from '@hetu/cli';
import viteCompression from 'vite-plugin-compression';
// import basicSsl from '@vitejs/plugin-basic-ssl';
import pkg from './package.json';

// 版本 & 构建时间
const version = pkg.version;
const now = new Date();
const buildDate = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}`;

process.env.VITE_APP_BUILD_INFO = `版本(next): ${version}, 构建日期: ${buildDate}`;
process.env.VITE_APP_TITLE = process.env.VITE_APP_TITLE || pkg.description;

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {
  // 环境变量
  const { VITE_BASE_PATH, VITE_PROXY_TARGET, VITE_APP_ANALYZE } = loadEnv(mode, process.cwd());

  const { targetApps, appPages, appAlias, appSassAdditionalDatas, appManualChunks } = setupApps();

  const otherApps = Object.values(Applications as Record<string, string>).filter(
    (key) => !targetApps.includes(key)
  ) as string[];

  // const isBuild = command === 'build';
  const enableAnalyze = VITE_APP_ANALYZE === 'on';

  return {
    base: VITE_BASE_PATH,
    experimental: {
      renderBuiltUrl(
        filename: string,
        { hostId, hostType, type }: { hostId: string; hostType: 'js' | 'css' | 'html'; type: 'public' | 'asset' }
      ) {
        if (hostType === 'html') {
          return './' + filename;
        }

        return { relative: true };
      }
    },
    worker: {
      format: 'es'
    },
    // 服务
    server: {
      // 服务器主机名
      host: true,
      // 端口号
      port: 4300,
      // 设为 true 时若端口已被占用则会直接退出，
      // 而不是尝试下移一格端口、
      strictPort: false,
      // http.createServer() 配置项
      // https: true,
      proxy: {
        [`^/(${[
          'static',
          'static-next/js',
          'static-next/css',
          'static-next/fonts',
          ...otherApps.map((a) => `static-next/${a}`)
        ]
          .concat(otherApps)
          .join('/|')}/|index.html|invite.html)`]: {
          target: `http://${VITE_PROXY_TARGET}/`,
          changeOrigin: true
        },
        // api
        '^/[^@].+(?<!\\..+)$': {
          target: `http://${VITE_PROXY_TARGET}/`,
          changeOrigin: true
        },
        // 模型/自定义组件等文件地址
        '^/(.+)/TJ(([0-9]|[a-z]){30})/(.+)\\.(.+)$': {
          target: `http://${VITE_PROXY_TARGET}/`,
          changeOrigin: true
        },
        '/ws': {
          target: `ws://${VITE_PROXY_TARGET}/`,
          ws: true,
          changeOrigin: true
        }
      },

      // 开发服务器配置 CORS
      // boolean | CorsOptions
      cors: {},
      // 设置为 true 强制使依赖构建
      // force: true,
      // 禁用或配置HMR连接
      hmr: {},
      // 传递给 chokidar 的文件系统监视器选项
      watch: {}
    },
    plugins: [
      vue({
        template: { transformAssetUrls }
      }),
      // basicSsl(),
      unocss(),
      quasar({
        sassVariables: '@hetu/platform-shared/styles/quasar.variables.scss'
      }),
      // splitVendorChunkPlugin(),
      chunkSplitPlugin({
        customChunk: (context) => {
          // files into pages directory is export in single files
          const { id } = context;

          let name;
          for (const fn of appManualChunks) {
            name = fn(id);
            if (typeof name === 'string') {
              break;
            }
          }

          return name || null;
        },
        customSplitting: {
          // Any file that includes `utils` in src dir will be bundled in the `utils` chunk
          // 'utils': [/src\/utils/]
        }
      }),
      copyAssets(),
      createMpaPlugin({
        template: 'index.html',
        /**
         * You can write directly or use `createPages` function independently outside and then pass it to this field.
         * Both of the above can enjoy type hints.
         */
        pages: appPages,
        /**
         * Use html minimization feature at build time.
         */
        // htmlMinify: true,
        /**
         * Customize the history fallback rewrite rules for `dev server`.
         * If you config your pages as above, this rewrite rules will be automatically generated.
         * Otherwise you should manually write it, which will overwrite the default.
         */
        rewrites: [
          {
            from: new RegExp('/([a-z0-9/]*(.html)?)'),
            to: (ctx) => {
              return normalizePath(`/${ctx.match[1]}${ctx.match.includes('.html') ? '' : '/index.html'}`);
            }
          }
        ]
        /**
         * Sometimes you might want to reload `pages` config or restart ViteDevServer when
         * there are some files added, removed, changed and so on. You can set `watchOptions` to
         * customize your own logic.
         *
         * The `include` and `exclude` based on `Rollup.createFilter`
         * @see https://vitejs.dev/guide/api-plugin.html#filtering-include-exclude-pattern
         */
        // watchOptions: {
        //   events: ['add', 'unlink', 'change'],
        //   include: ['**/pages/**'],
        //   handler: (ctx) => {
        //     console.log(ctx.type, ctx.file);
        //     // ctx.reloadPages();
        //   }
        // }
      }),
      viteCompression({
        // 如果体积大于阈值，将被压缩，单位为b，体积过小时请不要压缩，以免适得其反
        threshold: 10240
      }),
      enableAnalyze &&
        visualizer({
          gzipSize: true,
          brotliSize: true,
          emitFile: false,
          filename: 'analyze.html', //分析图生成的文件名
          open: true //如果存在本地服务端口，将在打包后自动展示
        })
      // viteMockServe({
      //   ignore: /^_/,
      //   mockPath: 'mock',
      //   localEnabled: !isBuild,
      //   logger: true,
      //   injectCode: `
      //     import { setupProdMockServer } from '../mock/_createProductionServer';

      //     setupProdMockServer();
      //     `
      // })
    ],
    resolve: {
      alias: {
        // '@': fileURLToPath(new URL('./src', import.meta.url))
        ...appAlias
      }
    },
    css: {
      // 指定传递给 CSS 预处理器的选项
      preprocessorOptions: {
        scss: {
          charset: false,
          additionalData: `
            @use "sass:color";
            @use "sass:map";
            @import "@hetu/platform-shared/styles/variables.scss";
            ${appSassAdditionalDatas.join('')}
          `
        }
      }
    },
    build: {
      // 浏览器兼容性 'esnext' | 'modules'
      target: 'esnext',
      //输出路径
      // outDir: 'dist',
      // 生成静态资源的存放路径
      assetsDir,
      // 小于此阈值的导入或引用资源将内联为 base64 编码, 以避免额外的http请求, 设置为 0, 可以完全禁用此项，
      assetsInlineLimit: 4096,
      // 启动 / 禁用 CSS 代码拆分
      cssCodeSplit: true,
      // 构建后是否生成 source map 文件
      sourcemap: false,
      // 自定义底层的 Rollup 打包配置
      rollupOptions: {
        output: {
          chunkFileNames: `${assetsDir.replace('./', '')}/js/[name]-[hash].js`,
          entryFileNames: `${assetsDir.replace('./', '')}/js/[name]-[hash].js`,
          // assetFileNames: `${assetsDir.replace('./', '')}/[ext]/[name]-[hash].[ext]`
          assetFileNames(chunkInfo) {
            let folder = '[ext]';
            if (['ttf', 'woff', 'woff2'].find((ext) => chunkInfo.name?.endsWith(ext))) {
              folder = 'fonts';
            }
            return `${assetsDir.replace('./', '')}/${folder}/[name]-[hash].[ext]`;
          }
        }
      },

      // @rollup/plugin-commonjs 插件的选项
      commonjsOptions: {},

      // 构建的库
      // lib: { entry: string, name?: string, formats?: ('es' | 'cjs' | 'umd' | 'iife')[], fileName?: string },

      // 当设置为 true, 构建后将会生成 manifest.json 文件
      manifest: enableAnalyze,

      // 设置为 false 可以禁用最小化混淆
      // 或是用来指定是应用哪种混淆器
      // boolean | 'terser' | 'esbuild'
      minify: 'terser',

      // 传递给 Terser 的更多 minify 选项
      terserOptions: {},

      // 设置为false 来禁用将构建好的文件写入磁盘
      write: true,

      // 默认情况下 若 outDir 在 root 目录下， 则 Vite 会在构建时清空该目录。
      emptyOutDir: true,

      // chunk 大小警告的限制
      chunkSizeWarningLimit: 500
    }
  };
};
