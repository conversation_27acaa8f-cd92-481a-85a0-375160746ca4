<template>
  <i :class="[className, colorName]" :style="{ fontSize: size }">
    <svg viewBox="0 0 1024 1024" v-if="svg" aria-hidden="true">
      <use :xlink:href="svgPath"></use>
    </svg>
    <slot></slot>
  </i>
</template>

<script setup lang="ts">
import { defineOptions, withDefaults, defineProps, computed } from 'vue';

// 图标与默认颜色的映射
const colorMap: { [key: string]: string } = {
  string: 'primary',
  number: 'info',
  boolean: 'positive',
  location: 'accent'
};

defineOptions({ name: 'ht-icon' });

const props = withDefaults(
  defineProps<{
    name: string;
    svg?: boolean;
    svgFolder?: string;
    size?: string;
    color?: string;
  }>(),
  {
    svgFolder: './static/icons'
  }
);

const className = computed(() => {
  const name = props.svg ? props.name.replace(/\//g, '-') : props.name;
  return name.startsWith('hticon-') ? name : `hticon-${name}`;
});
const svgPath = computed(() => `${props.svgFolder}/${props.name}.svg`);

const colorName = computed(() => {
  return props.color ? `text-${props.color}` : colorMap[props.name] ? `text-${colorMap[props.name]}` : '';
});
</script>

<style scoped>
svg {
  overflow: hidden;
  fill: currentcolor;
}
</style>
