const { prompt } = require('enquirer');
const chalk = require('chalk');
const { getExistsApps, getUnExistsApps, getAppConfig, getAppCurrentBranch, BaseAppConfig } = require('./apps');

const promptChoices = async (choices, emptyMessage, single) => {
  if (choices && choices.length) {
    !single &&
      console.log(
        chalk.gray(`(按 ${chalk.blue('<space>')} 选择, ${chalk.blue('<a>')} 切换全选, ${chalk.blue('<i>')} 反选)`)
      );
    const { apps } = await prompt({
      type: single ? 'select' : 'multiselect',
      name: 'apps',
      message: `请选择应用:`,
      choices
    });

    return apps;
  }

  console.log(emptyMessage);
  return [];
};

const baseAppInfo = () => {
  const info = getAppCurrentBranch();
  console.log(chalk.blue(`${chalk.bgBlue(` ${BaseAppConfig.name} `)} ● ${chalk.bgGreen(` ${info.branch} `)}`));

  return info;
};

/**
 * 选择当前工程下已拉取的子应用
 * @param {boolean} single 单选
 * @returns
 */
exports.choiceExistsApps = async (single) => {
  // const { branch } = baseAppInfo();

  const apps = getExistsApps();
  const choices = apps.map((app) => {
    const config = getAppConfig(app);
    // const { branch: appBranch, isMatch } = getAppCurrentBranch(app);

    // return {
    //   name: app,
    //   message: `${config.name} ● ${app} ● ${isMatch ? appBranch : chalk.yellow(appBranch)}`,
    //   hint: isMatch
    //     ? ''
    //     : '\n    ' +
    //       chalk.gray(
    //         `当前分支(${chalk.yellow(appBranch)})与平台指定分支(${chalk.green(config.branch ?? branch)})不匹配`
    //       )
    // };
    return {
      name: app,
      message: `${config.name} ● ${app}`
    };
  });

  return await promptChoices(choices, chalk.yellow(`应用列表为空, 请添加应用: ${chalk.cyan('pnpm app add')}`), single);
};

/** 选择当前工程下未拉取的子应用 */
exports.choiceUnExistsApps = async () => {
  const { branch } = baseAppInfo();

  const apps = getUnExistsApps();
  const choices = apps.map((app) => {
    const config = getAppConfig(app);

    return {
      name: app,
      message: `${config.name} ● ${app} ● ${config.branch ?? branch}`
    };
  });

  return await promptChoices(choices, chalk.green('应用已全部添加'));
};
