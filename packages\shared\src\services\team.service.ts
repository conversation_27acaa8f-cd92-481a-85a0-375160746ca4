import { HttpApiService, responseData, responseReject, responseResult } from '@hetu/http';
import { TeamApi, Team, type Teams, TEAM_MODULE_KEY, type Profile, UserGroup } from '../models';
import { createSingleClass } from '@hetu/core';

class TeamServiceCtor extends HttpApiService<TeamApi> {
  httpApi = new TeamApi();

  httpModuleKey = TEAM_MODULE_KEY;

  /** 当前团队信息 */
  current: Team | null = null;

  loaded = false;

  /**
   * 查询团队列表
   */
  getTeams() {
    return this.http.get(this.api.list).then(responseData<Teams>, responseReject);
  }

  /**
   * 查询团队信息
   * @param reload 重新获取
   */
  async getTeam(reload = false) {
    if (!reload && this.loaded) {
      return this.current as Team;
    }

    const team = await this.http.get(this.api.details).then(responseData<Team>, responseReject);
    this.current = team;
    this.loaded = true;

    return team;
  }

  /**
   * 新建
   * @param team 团队信息
   */
  create(team: Team) {
    return this.http.post(this.api.create, team).then(responseResult, responseReject);
  }

  /**
   * 保存
   * @param team 团队信息
   */
  save(team: Team) {
    return this.http.post(this.api.save, team).then(responseResult<Team>, responseReject);
  }

  /**
   * 验证域名
   * @param id 团队ID
   * @param domain 域名
   */
  domainCheck(id: string, domain: string) {
    return this.http.get(this.api.codeCheck, { params: { id, domain } }).then(responseResult, responseReject);
  }

  /**
   * 退出
   * @param teamId 团队ID
   */
  leave(teamId: string) {
    return this.http.post(this.api.leave, { teamId }).then(responseResult, responseReject);
  }

  /**
   * 查询团队下的成员列表(手机号、邮箱脱敏)
   * @param loginType 账号类型: 默认查全部
   * - 0: 平台用户
   * - 1: 团队用户
   */
  getMembers(loginType?: 0 | 1) {
    return this.http.get(this.api.members, { params: { loginType } }).then(responseData<Profile[]>, responseReject);
  }

  /**
   * 查询团队下的用户组列表
   */
  getGroups() {
    return this.http.get(this.api.groups).then(responseData<UserGroup[]>, responseReject);
  }
}

/**
 * 团队服务类
 * <AUTHOR>
 */
export const TeamService = createSingleClass(TeamServiceCtor);
