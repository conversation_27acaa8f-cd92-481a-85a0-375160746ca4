import {
  BehaviorType,
  Constraints,
  CrossAlignment,
  Frame,
  Graph,
  HoriConsType,
  Transform,
  useLayout,
  VertConsType
} from '@vis/document-core';
import { useDesignStore } from '../../../../../../stores';
import { computed, defineComponent, nextTick, watch } from 'vue';
import { useAction, useGraph } from '../../../../../../hooks';

/**
 * 位置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-position',
  props: {},
  setup(props) {
    const designStore = useDesignStore();
    const { align } = useAction();
    const { formatRotate, resetGraphXYByDom } = useGraph();
    const { isFreeform, isFlex, isGrid } = useLayout();

    const activeGraphs = computed(() => designStore.active.value.graphs as Graph[]);

    const activeGraph = computed(() => designStore.active.value.graph as Graph);

    const activeFrame = computed(() => designStore.active.value.frame as Frame);

    const transform = computed(() => designStore.active.value.graph?.transform as Transform);

    const onChangeRotate = (value: number) => {
      transform.value.rotate = formatRotate(value);
    };
    const onRotateStep = (rotate: number) => {
      transform.value.rotate = formatRotate(transform.value.rotate + rotate);
    };

    const isSingleAlign = computed(() => {
      let flag =
        activeGraphs.value.length === 1 && !activeGraphs.value[0].parent && !activeGraphs.value[0].children?.length;
      if (activeFrame.value && isFlex(activeFrame.value)) {
        flag = true;
      }
      return flag;
    });

    const isMultipleAlign = computed(() => {
      return activeGraphs.value.length < 3;
    });

    //#region 约束

    const constraints = computed(() => activeGraph.value.constraints as Constraints);

    const horiOptions = [
      { value: HoriConsType.Left, label: '靠左固定' },
      { value: HoriConsType.Right, label: '靠右固定' },
      { value: HoriConsType.Stretch, label: '左右固定' },
      { value: HoriConsType.Center, label: '居中' },
      { value: HoriConsType.Scale, label: '跟随缩放' }
    ];

    const vertOptions = [
      { value: VertConsType.Top, label: '靠上固定' },
      { value: VertConsType.Bottom, label: '靠下固定' },
      { value: VertConsType.Stretch, label: '上下固定' },
      { value: VertConsType.Center, label: '居中' },
      { value: VertConsType.Scale, label: '跟随缩放' }
    ];

    const behaviorOptions = [
      { value: BehaviorType.Follow, label: '随父级滚动' },
      { value: BehaviorType.Fixed, label: '固定' },
      { value: BehaviorType.Sticky, label: '吸顶固定' }
    ];

    const onChangeHori = (type: HoriConsType) => {
      constraints.value.horizontal = type;
      // 更新约束辅助线
      designStore.moveableRef.value?.updateRect();
    };

    const onChangeVert = (type: VertConsType) => {
      constraints.value.vertical = type;
      // 更新约束辅助线
      designStore.moveableRef.value?.updateRect();
    };

    //#endregion

    /**
     * 网格布局中图形在格子里的对齐方式
     * @param type
     * @param align
     */
    const onGridItemAlign = (type: 'justifySelf' | 'alignSelf', align: CrossAlignment) => {
      if (activeGraph.value.gridItem) {
        activeGraph.value.gridItem[type] = align;
        nextTick(() => resetGraphXYByDom([activeGraph.value]));
      }
    };

    return {
      activeGraph,
      transform,

      onChangeRotate,
      onRotateStep,

      align,
      isSingleAlign,
      isMultipleAlign,

      constraints,
      horiOptions,
      vertOptions,
      behaviorOptions,

      HoriConsType,
      VertConsType,

      onChangeHori,
      onChangeVert,

      activeFrame,
      isFreeform,
      isFlex,
      isGrid,
      CrossAlignment,

      onGridItemAlign
    };
  }
});
