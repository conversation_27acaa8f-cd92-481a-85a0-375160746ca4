import { computed, defineComponent } from 'vue';
import VisColorEyedropper from './eyedropper/index.vue';
import { useColorTransfer } from '@vis/page-main/modules/design/hooks';

export default defineComponent({
  name: 'vis-color-picker',
  components: { VisColorEyedropper },
  props: {
    modelValue: {
      type: String,
      required: true
    }
  },
  setup(props, { emit }) {
    const computedModel = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        emit('update:modelValue', value);
      }
    });

    const { rgbaStrToRgb } = useColorTransfer();
    const style = computed(() => {
      const { r, g, b } = rgbaStrToRgb(computedModel.value);

      return {
        '--picker-color': computedModel.value,
        '--picker-alpha-background': `linear-gradient(90deg, rgba(${r},${g},${b},0), rgba(${r},${g},${b},1))`
      };
    });

    return {
      computedModel,
      style
    };
  }
});
