import { defineComponent, computed, type PropType } from 'vue';
import type { Block } from '../../models';
import { useDocumentStore } from '../../stores';
/**
 * 块
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-block',
  components: {},
  props: {
    graph: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    const block = computed(() => {
      return props.graph;
    });

    const docStore = useDocumentStore();

    const widget = docStore.document.value.blocks.find((item) => item.id === block.value.decoration);

    return { block, widget };
  }
});
