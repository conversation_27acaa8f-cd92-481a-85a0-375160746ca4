import { createHttpApiProxy, useApiConfig, type HttpApi } from './api';
import { HttpClient } from './client';

/**
 * Api Service 基类
 *
 * @example
 * ```ts
 * class TestApi implements HttpApi {
 *   test: string = '/get';
 * }
 *
 * class TestServiceCtor extends HttpApiService<TestApi> {
 *   httpApi = new TestApi();
 *   httpModuleKey = 'test';
 *
 *   test(id: string) {
 *     return this.http.get(this.api.test, { params: { id } });
 *   }
 * }
 *
 * export const TestService = createSingleClass(TestServiceCtor);
 * ```
 *
 * <AUTHOR>
 */
export abstract class HttpApiService<T extends HttpApi> {
  private httpApiProxy?: T;

  /** Api 信息: 只用来赋值, 通过 `this.api` 获取 */
  abstract httpApi: T;

  /** 所属模块标识 */
  abstract httpModuleKey?: string;

  /** `HttpClient` */
  protected get http() {
    return HttpClient;
  }

  /**
   * Proxy for `this.httpApi`
   */
  get api(): T {
    const apiConfig = useApiConfig();
    if (!this.httpApiProxy && apiConfig) {
      this.httpApiProxy = createHttpApiProxy<T>(this.httpApi, this.httpModuleKey);
    }
    return this.httpApiProxy || this.httpApi;
  }
}
