/// <reference types="vite/client" />

interface ImportMetaEnv {
  /** 页面 title */
  readonly VITE_APP_TITLE: string;

  /** 应用平台 */
  readonly VITE_HETU_PLATFORM_KEY: string;

  /** 帐号登录&注册 */
  readonly VITE_HETU_ACCOUNT_KEY: string;

  /** 团队管理后台 */
  readonly VITE_HETU_TEAM_KEY: string;

  /** 个人设置 */
  readonly VITE_HETU_PROFILE_KEY: string;

  /** 邀请确认 */
  readonly VITE_HETU_INVITE_KEY: string;

  /** 资源广场 */
  readonly VITE_HETU_MARKET_KEY: string;

  /** 空间(SSO) */
  readonly VITE_HETU_SPACE_KEY: string;

  /** 可视化大屏应用 */
  readonly VITE_HETU_SCREEN_KEY: string;

  /** 数据工厂应用 */
  readonly VITE_HETU_ANALYSIS_KEY: string;

  /** 指标管理应用 */
  readonly VITE_HETU_INDICATORS_KEY: string;

  /** 智能报警应用 */
  readonly VITE_HETU_ALARM_KEY: string;

  /** 多维分析应用 */
  readonly VITE_HETU_BI_KEY: string;

  /** 数据门户应用 */
  readonly VITE_HETU_PORTAL_KEY: string;

  /** API 应用 */
  readonly VITE_HETU_API_KEY: string;

  /** 数据管理应用 */
  readonly VITE_HETU_DATAFRAME_KEY: string;

  /** 数字孪生应用 */
  readonly VITE_HETU_TWIN_KEY: string;

  /** 可视化设计应用 */
  readonly VITE_HETU_VISUO_KEY: string;

  /** 应用页面 */
  readonly VITE_HETU_PAGES: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare interface Window {
  appBootstrap: Function;
}
