import { ref, defineComponent, type PropType, watch, type Ref, computed, onMounted } from 'vue';
import type { Menu } from '@hetu/theme';
import { type MaterialFile, MaterialType, Material } from '../../models';
import { MaterialService } from '../../services';
import { CatalogService, CatalogFileService, useFilePage, type FileTreeNode } from '@hetu/platform-app';
import { AttachmentService, UUID, scrollDomIntoView } from '@hetu/platform-shared';
import { QFile, QScrollArea, useQuasar } from 'quasar';
import { useMaterialFilePageStore } from '../../stores';

/**
 * 图片/视频
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-material-media',
  props: {
    modelValue: String,
    multi: {
      type: Boolean,
      default: true
    },
    /** 当前选中的菜单 */
    activeMenu: {
      type: Object as PropType<Menu>,
      required: true
    },
    type: {
      type: String as PropType<MaterialType>,
      default: MaterialType.Image,
      required: true
    }
  },
  setup(props, { emit }) {
    const $q = useQuasar();
    const isImage = props.type === MaterialType.Image;

    const store = useMaterialFilePageStore<MaterialFile>();
    store.preActiveId.value = props.modelValue;

    const filePage = useFilePage<MaterialFile>({
      acl: isImage ? MaterialService.imageAcl : MaterialService.videoAcl,
      category: props.type,
      cacheKey: 'material_' + props.type,
      multiSelect: props.multi,
      order: {
        column: 'createdTime'
      },
      sortColumns: {
        createdTime: '上传时间',
        fileSize: '文件大小'
      },
      store,
      extraTreeKey: 'attachmentId',
      loadData: () => MaterialService.getList(props.type)
    });

    const inited = filePage.init();

    const scrollAreaRef = ref<QScrollArea>();
    onMounted(() => {
      inited.then(() => {
        // 滚动当前 active 文件到可见区域
        const scrollbar = scrollAreaRef.value?.getScrollTarget();
        const activeDoms = scrollbar?.getElementsByClassName('is-active');
        if (activeDoms?.length) {
          scrollDomIntoView(activeDoms.item(0)!, scrollbar!);
        }
      });
    });

    const active = store.active;
    const hasActive = computed(() => !!(Array.isArray(active.value) ? active.value.length : active.value));

    //#region 全选
    const checkAll = ref(false);
    if (props.multi) {
      watch(
        () => (active as Ref<MaterialFile[] | undefined>).value?.length,
        () => {
          const avtiveLen = (active as Ref<MaterialFile[] | undefined>).value?.length;
          checkAll.value = !!avtiveLen && avtiveLen === filePage.folders.value.length + filePage.files.value.length;
        }
      );
    }

    const selectAll = () => {
      const active = store.active as Ref<MaterialFile[]>;
      if (checkAll.value) {
        active.value = filePage.folders.value.concat(filePage.files.value);
      } else {
        active.value = [];
      }
    };
    //#endregion 全选

    //#region 预览&查看素材
    const preview = ref(false);
    const previewId = ref('');

    const togglePreview = (id?: string) => {
      previewId.value = id ?? '';
      preview.value = !preview.value;
    };

    const downloadUrl = (id: string, type?: string) => AttachmentService.downloadFileUrl(id, type);
    //#endregion 预览&查看素材

    //#region 其它操作
    const breadcrumbsBackup = (file?: MaterialFile) => {
      filePage.breadcrumbsBackup(file);
      folderOpened();
    };

    const folderOpened = (folder?: MaterialFile) => {
      if (!props.multi && props.modelValue) {
        const file = store.tree.getFile(props.modelValue);
        const parentId = file?.parentId ?? '';
        const folderId = (folder ?? store.open.value)?.id ?? '';
        if (file && parentId === folderId) {
          active.value = file;
        }
      }

      folder && filePage.sort();
    };

    const clickFile = (file: MaterialFile, isActive: boolean) => {
      if (props.multi) return;

      const id = isActive ? file.attachmentId : '';
      emit('update:modelValue', id);
    };

    /** 删除 */
    const del = (file?: MaterialFile) => {
      file = file ?? (active.value as MaterialFile);
      if (!file && !(Array.isArray(file) && (file as Array<MaterialFile>).length)) {
        return;
      }

      $q.dialog({
        title: '提示',
        message: `确认删除 ${Array.isArray(file) ? `${file.length} 条数据` : `[ ${file.title} ]`}?`,
        ok: '确定',
        cancel: '取消'
      }).onOk(async () => {
        const folders: string[] = [];
        const files: string[] = [];

        let clearModelValue: undefined | (() => void);
        const datas = Array.isArray(file) ? file : [file];
        datas.forEach((item) => {
          (item.dataType ? folders : files).push(item.id);

          if (!item.dataType && item.attachmentId === props.modelValue) {
            clearModelValue = () => emit('update:modelValue', '');
          }
        });

        const all = [];
        const folderLen = folders.length;
        const fileLen = files.length;
        folderLen && all.push(CatalogService.delete(filePage.category, folders));
        fileLen && all.push(CatalogFileService.delete(filePage.category, files));

        const result = await Promise.all(all);

        let isFolderSuccess = true;
        let isFileSuccess = true;
        if (folderLen) {
          isFolderSuccess = result[0]?.status === 'success';
          isFolderSuccess && folders.forEach(filePage.removeNode);
        }
        if (fileLen) {
          isFileSuccess = result[folderLen ? 1 : 0]?.status === 'success';
          isFileSuccess && files.forEach(filePage.removeNode);
          clearModelValue && clearModelValue();
        }

        if (isFolderSuccess && isFileSuccess) {
          $q.notify({ position: 'top', type: 'positive', message: '删除成功' });
        } else if (isFolderSuccess && folderLen) {
          $q.notify({ position: 'top', type: 'warning', message: '文件夹删除成功, 文件删除失败!!!' });
        } else if (isFileSuccess && fileLen) {
          $q.notify({ position: 'top', type: 'warning', message: '文件删除成功, 文件夹删除失败!!!' });
        }
      });
    };
    //#endregion 其它操作

    //#region 文件上传
    // 上传中文件集合
    const uploadDatas = ref<File[]>();
    // 上传文件格式限制
    const accept = computed(() => {
      return (
        props.type === MaterialType.Video
          ? ['.mp4', '.ogg', '.webm']
          : ['.jpg', '.jpeg', '.bmp', '.png', '.gif', '.wbmp', '.webp']
      ).join(', ');
    });
    // 上传控件
    const uploaderRef = ref<QFile>();
    // 显示页面上传器
    const showPageUploader = ref<boolean>(false);

    /** 拖拽进入 */
    const dragenter = () => {
      showPageUploader.value = true;
    };
    /** 拖拽离开 */
    const dragleave = () => {
      showPageUploader.value = false;
    };
    const stopAndPreventDrag = (e: DragEvent) => {
      e && e.dataTransfer && (e.dataTransfer.dropEffect = 'copy');
      e.cancelable !== false && e.preventDefault();
      e.stopPropagation();
    };
    /** 拖拽放置 */
    const drop = (e: DragEvent) => {
      stopAndPreventDrag(e);
      const files = (e.dataTransfer as DataTransfer).files;
      uploaderRef.value?.addFiles(files);
      showPageUploader.value = false;
    };
    /** 选择文件 */
    const pickFiles = () => {
      uploaderRef.value?.pickFiles();
    };
    /** 上传 */
    const upload = () => {
      if (!uploadDatas.value) {
        return;
      }
      uploadDatas.value.forEach(async (file) => {
        const formData = new FormData();
        formData.append(isImage ? 'files' : 'file', file, file.name);
        formData.append('fileName', file.name);
        formData.append('moduleCode', 'material');
        if (isImage) {
          formData.append('isScale', '1');
          formData.append('width', '130');
          formData.append('height', '110');
          formData.append('keepAspectRatio', '1');
        }

        const id = UUID();
        const parentId = store.open.value?.id || '';
        const node = store.tree.addNode({
          id,
          // 用于 for key
          uid: id,
          title: file.name,
          parentId,
          category: filePage.category,
          extension: file.type.split('/')[1],
          fileSize: file.size,
          dataType: 0,
          percent: 0
        } as MaterialFile);

        const config = {
          onUploadProgress: (progressEvent: any) => {
            node.percent = parseInt(((progressEvent.loaded / progressEvent.total) * 100).toString(), 10);
          }
        };
        const result = await (isImage
          ? AttachmentService.uploadImage(formData, config)
          : AttachmentService.uploadVideo(formData, config));

        if (result?.status === 'success') {
          // 上传成功保存素材
          const data = isImage ? result.data[0] : result.data;
          const isSuccess = await saveMaterial(node, data);

          if (isSuccess) {
            return $q.notify({ type: 'positive', message: '上传成功', position: 'top' });
          }
        }

        // 上传/保存失败删除进度
        store.tree.removeNode(node.id);
      });

      uploadDatas.value = undefined;
    };

    /** 保存素材信息 */
    const saveMaterial = async (node: FileTreeNode<MaterialFile>, attachment: { id: string; fileName: string }) => {
      const material = new Material(
        attachment.fileName,
        filePage.category as MaterialType,
        attachment.id,
        node.parentId,
        node.extension,
        node.fileSize
      );

      const result = await MaterialService.save(material);
      const isSuccess = result?.status === 'success';
      if (isSuccess) {
        node.id = result.data.id;
        node.title = material.name;
        node.attachmentId = attachment.id;
        node.createdTime = result.data.createdTime;
        store.tree.replaceNode(node.uid as string, node);
      }

      return isSuccess;
    };
    //#endregion

    return {
      ...filePage,

      isImage,
      scrollAreaRef,

      active,
      hasActive,

      checkAll,
      selectAll,

      preview,
      previewId,
      togglePreview,
      downloadUrl,

      breadcrumbsBackup,
      folderOpened,
      clickFile,
      del,

      uploadDatas,
      uploaderRef,
      accept,
      showPageUploader,
      dragenter,
      dragleave,
      stopAndPreventDrag,
      drop,
      pickFiles,
      upload
    };
  }
});
