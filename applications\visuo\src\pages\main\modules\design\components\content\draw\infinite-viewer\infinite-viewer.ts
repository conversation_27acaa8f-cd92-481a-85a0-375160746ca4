import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';
import VueInfiniteViewer from 'vue3-infinite-viewer';
import { useActionStore, useDesignStore } from '../../../../stores';
import InfiniteViewer, { type OnDragStart, type OnPinch, type OnScroll } from 'infinite-viewer';
import { useShortcutKeys } from '../../../../hooks';

/**
 * 无限画布
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-infinite-viewer',
  components: {
    VueInfiniteViewer
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();

    const selectoRef = designStore.selectoRef;
    const moveableRef = designStore.moveableRef;

    const rulerState = designStore.rulerState;
    const horizontalGuidesRef = designStore.horizontalGuidesRef;
    const verticalGuidesRef = designStore.verticalGuidesRef;

    const infiniteCanvasRef = ref<InfiniteViewer>();

    /**
     * 无限画布滚动事件
     * @param e
     */
    const onScroll = (e: OnScroll) => {
      //  当滚动无限画布时，手动调用此方法重新检查元素位置，用于在容器滚动后刷新选择区域的位置计算
      selectoRef.value?.checkScroll();

      // 画布滚动时标尺也要滚动
      if (horizontalGuidesRef.value && verticalGuidesRef.value) {
        horizontalGuidesRef.value.scroll(e.scrollLeft);
        horizontalGuidesRef.value.scrollGuides(e.scrollTop);

        verticalGuidesRef.value.scroll(e.scrollTop);
        verticalGuidesRef.value.scrollGuides(e.scrollLeft);
      }
      rulerState.value.zoom = e.zoomY;

      rulerState.value.defaultScrollPos = e.scrollLeft;
      rulerState.value.defaultGuidesPos = e.scrollTop;
    };

    const onDragStart = (e: OnDragStart) => {
      // const target = e.inputEvent.target;
      // if (target.nodeName === 'A' || moveableRef.value.isMoveableElement(target) || moveableRef.value!.isDragging()) {
      //   e.stop();
      // }
    };

    const onPinch = (e: OnPinch) => {
      rulerState.value.zoom = e.zoom;
    };

    const onClick = () => {
      // designStore.activeContainer.value = undefined;
    };

    //#region 按空格键移动画布
    const actionStore = useActionStore();
    const handAction = computed(() => actionStore.actions.value.hand);
    const frameAction = computed(() => actionStore.actions.value.frame);
    const textboxAction = computed(() => actionStore.actions.value.textbox);

    const { addShortcuts, removeShortcuts } = useShortcutKeys();

    onMounted(() => {
      addShortcuts();
    });

    onUnmounted(() => {
      removeShortcuts();
    });

    // 画布鼠标样式
    const className = computed(() => {
      if (handAction.value.active) {
        return 'grab';
      } else if (frameAction.value.active || textboxAction.value.active) {
        return 'crosshair';
      } else if (designStore.canvasState.value.isRotate) {
        return 'rotateing';
      } else {
        return '';
      }
    });

    //#endregion

    return {
      handAction,
      className,

      infiniteCanvasRef,
      onScroll,
      onDragStart,
      onPinch,
      onClick
    };
  },
  mounted() {
    const designStore = useDesignStore();
    designStore.infiniteCanvasRef.value = this.infiniteCanvasRef;
    this.infiniteCanvasRef?.scrollTo(0, 0);
  },
  methods: {}
});
