import {
  HttpApiService,
  responseData,
  type PagingData,
  responseReject,
  responseResult,
  type PagingQuery,
  OrderType
} from '@hetu/http';

import { type Message, MessageApi, MessageACLResource } from '../models';
import { createSingleClass } from '@hetu/core';

class MessageManageServiceCtor extends HttpApiService<MessageApi> {
  httpApi = new MessageApi();

  httpModuleKey = 'messageNotice';

  acl = new MessageACLResource();

  /** 未读消息数 */
  unreadCount(): Promise<number> {
    return this.http.get(this.api.unread).then(responseData, responseReject);
  }

  /** 消息列表 */
  messages(pageNum: number, all: boolean) {
    const pq: PagingQuery = {
      pageNum,
      pageSize: 10,
      orders: [{ column: 'sendTime', sorting: OrderType.Desc }],
      filters: all ? {} : { status: 0 }
    };

    return this.http.post(this.api.page, pq).then(responseData<PagingData<Message>>, responseReject);
  }

  delete(ids: string[]) {
    return this.http.delete(this.api.delete, { data: { ids } }).then(responseResult, responseReject);
  }

  deleteAll() {
    return this.http.delete(this.api.deleteAll).then(responseResult, responseReject);
  }

  read(id: string) {
    return this.http.get(this.api.read, { params: { id } }).then(responseResult, responseReject);
  }

  readAll() {
    return this.http.get(this.api.readAll).then(responseResult, responseReject);
  }
}

/** 通知消息 */
export const MessageManageService = createSingleClass(MessageManageServiceCtor);
