<template>
  <div class="vis-config-status-other">
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__label">填充</div>
        <div class="vis-form-field__content">
          <vis-fill v-model="computedStyle.background" :showEyes="false" :minusWidth="0" />
        </div>
      </div>
      <!-- 描边 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">描边颜色</div>
        <div class="vis-form-field__content">
          <vis-fill v-model="computedStyle.border.paints" :showEyes="false" :minusWidth="0" />
        </div>
      </div>

      <!-- 粗细 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">描边粗细</div>
        <div class="vis-form-field__content">
          <vis-number
            v-model="computedStyle.border.position[0]"
            :min="0"
            @change="handlePositionChange"
            icon="vis-line-width"
          />
          <q-btn flat :class="{ active: showLine }" @click="showLine = !showLine">
            <ht-icon class="vis-icon" name="hticon-vis-control" />
            <q-tooltip> 自定义 </q-tooltip>
          </q-btn>
        </div>
      </div>

      <div class="vis-form-field" v-if="showLine">
        <div class="vis-form-field__label"></div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.border.position[0]" :min="0">
            <template #icon>
              <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-t.svg'" />
            </template>
          </vis-number>
          <vis-number v-model="computedStyle.border.position[1]" :min="0">
            <template #icon>
              <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-r.svg'" />
            </template>
          </vis-number>
        </div>
      </div>
      <div class="vis-form-field" v-if="showLine">
        <div class="vis-form-field__label"></div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.border.position[2]" :min="0">
            <template #icon>
              <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-b.svg'" />
            </template>
          </vis-number>
          <vis-number v-model="computedStyle.border.position[3]" :min="0">
            <template #icon>
              <img class="img-icon drag-icon" :src="'./static-next/svg/stroke/border-l.svg'" />
            </template>
          </vis-number>
        </div>
      </div>

      <!-- 样式 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">描边样式</div>
        <div class="vis-form-field__content">
          <vis-select class="w-full" v-model="computedStyle.border.style" :options="lineOptions">
            <template #prepend>
              <span
                class="mr-2"
                :style="{ borderStyle: computedStyle.border.style, borderWidth: '0.5px', width: '20px' }"
              ></span>
            </template>
            <template #option="{ opt, itemProps }">
              <q-item v-bind="itemProps">
                <q-item-section class="!flex-row items-center !justify-start">
                  <span class="mr-2" :style="{ borderStyle: opt.style, borderWidth: '0.5px', width: '20px' }"></span>
                  {{ opt.label }}
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
      </div>

      <!-- 阴影类型 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">阴影类型</div>
        <div class="vis-form-field__content">
          <vis-select class="flex-1" v-model="computedStyle.shadow.type" :options="effectOptions" />
        </div>
      </div>
      <!-- 阴影 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">特效位置</div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.shadow.offset.x" icon="hticon-vis-letter-x"></vis-number>
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label"></div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.shadow.offset.y" icon="hticon-vis-letter-y"></vis-number>
        </div>
      </div>

      <!-- 阴影效果 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">效果</div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.shadow.blur" icon="hticon-vis-blur" :min="0"></vis-number>
        </div>
      </div>

      <!-- 阴影扩展 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">扩展</div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedStyle.shadow.spread" icon="hticon-vis-spread" :min="0"></vis-number>
        </div>
      </div>

      <!-- 阴影颜色 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">阴影颜色</div>
        <div class="vis-form-field__content">
          <vis-fill :minusWidth="0" v-model="computedStyle.shadow.color" :onlyColor="true" :hideTitle="false" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./other.ts"></script>
