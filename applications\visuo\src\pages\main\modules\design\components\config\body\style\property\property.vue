<template>
  <div class="vis-config-style-property">
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        位置
        <q-btn
          v-if="activeGraph.parent && parentFrame && parentFrame.autoLayout.direction !== DirectionType.Freeform"
          :class="activeGraph.ignoreAutoLayout ? 'active' : ''"
          @click="onIgnoreAutoLayout"
          flat
          dense
        >
          <ht-icon class="vis-icon" name="vis-position" />
          <q-tooltip> 忽略自动布局 </q-tooltip>
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <vis-position />
        <q-separator />
      </div>
    </div>

    <div class="vis-config-card">
      <div class="vis-config-card__header">
        布局
        <div class="grid grid-flow-col gap-1" v-if="frame">
          <q-btn :class="{ active: frame.autoLayout.direction !== DirectionType.Freeform }" @click="onSetupLayout" flat>
            <ht-icon
              :name="`vis-auto-layout${frame.autoLayout.direction === DirectionType.Freeform ? '-add' : ''}`"
              class="vis-icon"
            />
            <q-tooltip>
              {{ frame.autoLayout.direction === DirectionType.Freeform ? '添加' : '删除' }}自动布局
            </q-tooltip>
          </q-btn>

          <q-btn v-if="!frame.parent" @click="onChangeLayoutType" flat>
            <q-icon class="vis-icon" :name="frame.autoLayout.type === 'fixed' ? 'screenshot_monitor' : 'devices'" />
            <q-tooltip> {{ frame.autoLayout.type === 'fixed' ? '固定布局' : '响应式布局' }} </q-tooltip>
          </q-btn>

          <q-btn flat>
            <q-icon class="vis-icon" name="more_vert" />
            <q-menu dense style="width: 180px" class="vis-menu">
              <q-list dense>
                <q-item @click="onAdaptContent" clickable v-close-popup>
                  <q-item-section avatar>
                    <q-icon class="vis-icon" name="zoom_in_map" />
                  </q-item-section>
                  <q-item-section>适应内容大小</q-item-section>
                </q-item>
                <q-item :active="frame.clip" @click="frame.clip = !frame.clip" clickable v-close-popup>
                  <q-item-section avatar>
                    <ht-icon class="vis-icon" name="vis-clip" />
                  </q-item-section>
                  <q-item-section>裁剪超出内容</q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-btn>
        </div>
      </div>
      <div class="vis-config-card__body">
        <vis-layout ref="layoutRef" />
        <q-separator />
      </div>
    </div>

    <div class="vis-config-card">
      <div class="vis-config-card__header">外观</div>
      <div class="vis-config-card__body">
        <vis-aspect :option="activeGraph" />
        <q-separator />
      </div>
    </div>

    <div class="vis-config-card" v-if="activeGraph.text">
      <div class="vis-config-card__header">文本</div>
      <div class="vis-config-card__body">
        <vis-text
          :option="activeGraph.text"
          :extra="['alignHorizontal', 'alignVertical', 'adapt', 'letterSpacing', 'fontStyle', 'lineHeight']"
        />
        <q-separator />
      </div>
    </div>

    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>填充</span>
        <q-btn flat dense @click="addFillPaints">
          <ht-icon class="vis-icon" name="vis-add" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <template v-if="!undef('fillPaints') && activeGraph.fillPaints?.length">
          <div class="position-relative" v-for="(item, index) in activeGraph.fillPaints" :key="indexArr[index]">
            <vis-fill :minusWidth="60" v-model="activeGraph.fillPaints[index]" />
            <q-btn flat dense class="delete-btn" @click="deleteFillPaints(index)">
              <ht-icon class="vis-icon" name="vis-remove" />
            </q-btn>
          </div>
        </template>
        <q-separator />
      </div>
    </div>

    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>描边</span>
        <q-btn flat dense @click="manage('stroke')">
          <ht-icon class="vis-icon" :name="undef('stroke') ? 'vis-add' : 'vis-remove'" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="position-relative" v-if="!undef('stroke')">
          <vis-stroke v-model="activeGraph.stroke" />
        </div>
        <q-separator />
      </div>
    </div>
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>特效</span>
        <q-btn flat dense @click="manage('effects')">
          <ht-icon class="vis-icon" :name="undef('effects') ? 'vis-add' : 'vis-remove'" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="position-relative" v-if="!undef('effects')">
          <vis-effects v-model="activeGraph.effects" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./property.ts"></script>
