@import '../../../index.scss';

.#{$vis-prefix}-color-picker {
  .q-color-picker {
    &__spectrum {
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid rgba(255, 255, 255, 0.8);
    }

    &__sliders {
      margin-left: 48px;
      padding: 4px 8px;
      display: flex;
      flex-direction: column;
      height: calc(24px + $color-silder-width * 2);
      justify-content: space-around;

      .q-slider {
        &__thumb {
          width: $color-silder-width * 2 !important;
          height: $color-silder-width * 2 !important;

          path {
            display: none;
          }

          &-shape {
            transform: scale(0.5) !important;
            border-radius: 50%;
            box-shadow:
              inset 0 0 0 1px #ccc,
              inset 0 0 0 3px white,
              inset 0 0 0 4px #ccc;
          }
        }

        &__focus-ring {
          display: none !important;
        }

        &__track {
          height: $color-silder-width !important;
          border-radius: $color-silder-width !important;
          background-size: 0 !important;

          &::before,
          &::after {
            content: '';
            display: block;
            position: absolute;
            left: $color-silder-width * -0.5;
            right: $color-silder-width * -0.5;
            height: $color-silder-width;
            border-radius: $color-silder-width * 0.5;
          }

          &-container--h {
            padding: 0
          }
        }
      }

    }

    &__hue {
      .q-slider__track {
        &::before {
          background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);
        }
      }
    }

    &__alpha {
      .q-slider__track {
        &::before {
          background: var(--picker-alpha-background) !important;
        }

        &::after {
          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==") !important
        }
      }
    }

    &__spectrum-circle {
      width: 12px;
      height: 12px;
      box-shadow: inset 0 0 0 0.5px #ccc, inset 0 0 0 1.5px white, inset 0 0 0 2px #ccc;
      transform: translate(-50%, -50%);
    }
  }

  // 取色器样式
  .#{$vis-prefix}-color-eyedropper {
    position: absolute;
    left: 12px;
    bottom: 12px;
  }
}