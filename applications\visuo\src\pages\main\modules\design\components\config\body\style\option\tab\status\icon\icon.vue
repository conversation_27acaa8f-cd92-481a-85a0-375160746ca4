<template>
  <div class="vis-config-status-icon">
    <vis-fill-image v-model="iconOption" :isIcon="true" @update:modelValue="handleUpdateIcon" />
    <div class="vis-form-inline q-pt-sm">
      <div class="vis-form-field">
        <div class="vis-form-field__label">宽度</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.width" icon="hticon-vis-letter-w" :min="0" />
        </div>
      </div>

      <div class="vis-form-field">
        <div class="vis-form-field__label">高度</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.height" icon="hticon-vis-letter-h" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">左边距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.gutter[3]" icon="hticon-vis-padding-left" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">右边距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.gutter[1]" icon="hticon-vis-padding-right" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">上边距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.gutter[0]" icon="hticon-vis-vertical_t" :min="0" />
        </div>
      </div>
      <div class="vis-form-field">
        <div class="vis-form-field__label">底边距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="iconStyle.gutter[2]" icon="hticon-vis-vertical_b" :min="0" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./icon.ts"></script>
