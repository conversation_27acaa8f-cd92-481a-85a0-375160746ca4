<template>
  <q-layout :class="`ht-space-login ${domain}`">
    <q-page-container>
      <q-page class="row flex-center">
        <q-card class="full-width max-w-100" :flat="$q.screen.xs">
          <q-card-section class="text-h5">{{ title || '请登录' }}</q-card-section>
          <q-card-section :class="`q-p${accountConfig?.ssoConfig ? 'y' : 't'}-none`">
            <q-form ref="formRef">
              <q-input v-model="loginModel.username" label="用户名" :rules="rules.username" lazy-rules dense>
                <template v-slot:prepend>
                  <q-icon size="20px" name="person" />
                </template>
              </q-input>
              <q-input
                v-model="loginModel.password"
                type="password"
                label="密码"
                :rules="rules.password"
                lazy-rules
                dense
              >
                <template v-slot:prepend>
                  <q-icon size="20px" name="lock" />
                </template>
              </q-input>
              <template v-if="code.valid">
                <q-input
                  v-model="loginModel.code"
                  label="验证码"
                  :rules="rules.code"
                  lazy-rules="ondemand"
                  ref="codeInputRef"
                  dense
                  @blur="codeInputRef?.validate()"
                >
                  <template v-slot:prepend>
                    <q-icon size="20px" name="confirmation_number" />
                  </template>
                  <template v-slot:append>
                    <q-img class="!w-27 cursor-pointer" :src="code.code" @click.stop="fetchCodeImg()">
                      <template v-slot:error>加载失败</template>
                    </q-img>
                  </template>
                </q-input>
              </template>
            </q-form>
            <q-btn class="full-width" label="登录" color="primary" unelevated @click="login" />
          </q-card-section>
          <q-card-section v-if="accountConfig?.ssoConfig" class="text-center">
            <label class="ht-link" @click="sso">
              {{ accountConfig.ssoConfig.clientName }}
            </label>
          </q-card-section>
        </q-card>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script lang="ts" src="./login"></script>
