import { responseData, type HttpApi, type ResponseResult, HttpClient } from '@hetu/http';
import { Notify } from 'quasar';
import { type System, SystemState, ProductMode } from './model';

const cow = ['s', 't', 'e', 'h', 'm', 's', 'y'];
const dog = [2, 1, 7, 0, 3];
const cat = (s: string) => atob(s);

const url = () => {
  const { server, prefix, suffix } = systemApi || {};

  return [
    server,
    prefix,
    '/' + cow.shift() + cow.pop() + cow.pop() + cow.shift() + cow.shift() + cow.pop(),
    cat(['L' + dog.shift(), 'V' + dog.length, 'dH', 'Jh', 'cw'].join(dog.pop() ? '' : cow[0])),
    suffix
  ].join('');
};

let systemApi = null as unknown as HttpApi;

let reload = false;

const store = { system: {} as System };

/** 系统信息 */
export const useSystem = () => store.system;

/** 是否是数据开发平台产品 */
export const isTrustedProduct = () => store.system.product === ProductMode.Trusted;

export const setupSystem = async (api?: HttpApi) => {
  if (reload) {
    location.href = location.href.split('#')[0];
    return useSystem();
  }

  if (!systemApi && api) {
    systemApi = Object.assign({}, api);
  }

  let system = {} as any;

  try {
    const data = await HttpClient.get<ResponseResult<System>>(url(), { data: { 'ht-allow-anonymous': true } }).then(
      responseData
    );

    if (data) {
      system = Object.freeze(data);

      switch (system.state) {
        case SystemState.Normalized:
          reload = true;
          break;
        case SystemState.Overdue:
          Notify.create({
            type: 'negative',
            message: '授权已超期将限制部分功能, 请联系管理员',
            position: 'top',
            timeout: 0,
            actions: [{ icon: 'close', size: 'sm', color: 'white', round: true }]
          });
          break;
        default:
          break;
      }

      store.system = system;
    }
  } catch (error) {
    system = Promise.reject(error);
  }

  return system;
};

export * from './model';
