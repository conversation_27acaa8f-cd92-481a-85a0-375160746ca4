<template>
  <div class="vis-store-sidebar">
    <div class="vis-store-sidebar__logo h-12 flex flex-col justify-between mx-1">
      <img class="w-6 h-6 m-1 my-11px" :src="'./static-next/img/logo.png'" alt="logo" />
      <q-separator />
    </div>

    <q-tabs v-model="selectedMenu" vertical indicator-color="transparent" class="vis-store-sidebar__menu vis-tabs">
      <q-tab v-for="tab in tabs" :key="tab.name" :name="tab.name" :ripple="false">
        <ht-icon class="vis-icon" :name="tab.icon" />
      </q-tab>
    </q-tabs>
    <q-separator class="vis-store-sidebar__separator" vertical />
  </div>
</template>

<script lang="ts" src="./sidebar.ts"></script>

<style lang="scss" scoped src="./sidebar.scss"></style>
