/**
 * 网格布局
 * <AUTHOR>
 */
export const useGridLayout = () => {
  /**
   * 列宽分配算法
   * @param {number} totalWidth - 容器总宽度（如500）
   * @param {number} columns - 列数（如3）
   * @param {number} gap - 列间距（如20）
   * @returns {number[]} 每列宽度数组
   */
  const calculateColumns = (totalWidth: number, columns: number, gap: number): number[] => {
    // 计算总间隔占用空间
    const totalGapSpace = gap * (columns - 1);
    // 计算可用分配宽度
    const availableWidth = totalWidth - totalGapSpace;
    // 基础列宽（向下取整）
    const baseWidth = Math.floor(availableWidth / columns);
    // 需要分配的余数像素
    const remainder = availableWidth % columns;

    // 创建初始列宽数组
    const columnWidths: Array<number> = Array(columns).fill(baseWidth);

    // 从第一列开始分配余数（每列+1）
    for (let i = 0; i < remainder; i++) {
      columnWidths[i]++;
    }

    return columnWidths;
  };

  /**
   * 分配的格子map
   * @param {number} totalWidth - 容器总宽度（如500）
   * @param {number} columns - 列数（如3）
   * @param {number} gap - 列间距（如20）
   * @returns {
   *  1: [0, 100],
   *  2: [120, 220]
   * }
   */
  const helperGridMap = (totalWidth: number, columns: number, gap: number) => {
    const columnWidths = calculateColumns(totalWidth, columns, gap);
    const map: Record<string, number[]> = {};
    let total = 0;

    columnWidths.forEach((w, i) => {
      const start = total;
      const end = start + w;
      map[`${i + 1}`] = [start, end];
      total = end + gap;
    });
    return map;
  };
  return { calculateColumns, helperGridMap };
};
