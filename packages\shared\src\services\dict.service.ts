import { responseData, HttpApiService, responseReject, type HttpApi } from '@hetu/http';
import { SYSTEM_API_PREFIX, type Dict } from '../models';
import { createSingleClass } from '@hetu/core';

class DictApi implements HttpApi {
  dicts = `${SYSTEM_API_PREFIX}/api/v1/dictionaries/dictionary/sublist`;
}

class DictServiceCtor extends HttpApiService<DictApi> {
  httpApi = new DictApi();

  httpModuleKey = 'dict';

  /**
   * 按字典编码查询直接子字典项
   * @param dictCode 上级编码
   */
  getDicts(dictCode: string): Promise<Dict[]> {
    return this.http.get(this.api.dicts, { params: { dictCode } }).then(responseData, responseReject);
  }
}

/**
 * 字典服务类
 */
export const DictService = createSingleClass(DictServiceCtor);
