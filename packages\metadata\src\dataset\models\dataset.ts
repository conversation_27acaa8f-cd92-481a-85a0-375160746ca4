import { BaseModel } from '@hetu/http';
import { DatasetDataFilter, DatasetFilter } from './dataset-filter';
import type { DatasetFields } from './dataset-field';
import type { QueryParams } from './dataset-param';
import type { FileInfo } from '@hetu/platform-shared';

/**
 * 数据资源
 * <AUTHOR>
 */
export class Dataset extends BaseModel implements DatasetFilter {
  /** 名称 */
  datasetName = '';

  /** 类型 */
  datasetType = '';

  /** 描述 */
  remark = '';

  /** 目录Id */
  catalogId = '';

  /** 自动排序 0-否，1-是 */
  autoSorted = 1;

  /** 字段 */
  datasetFields: DatasetFields = [];

  /** 查询参数字段集合 */
  queryParamList?: QueryParams = [];

  dataFilter = new DatasetDataFilter();
}

/**
 * 数据资源文件
 */
export interface DatasetFile extends FileInfo {
  /** 数据集类别 */
  datasetType: string;
}

/**
 * 数据资源对象数组
 * @type
 */
export type Datasets = Dataset[];

/** 数据资源类型名称 */
export enum DatasetType {
  Sql = 'sql',
  Api = 'api',
  Join = 'join',
  Tda = 'tda'
}

/** 数据资源类型名称 */
export enum DatasetTypeName {
  sql = 'SQL数据资源',
  api = 'API数据资源',
  join = '关联数据资源',
  tda = '多维分析'
}

export interface DatasetTreeNode {
  title: string;
  id: string;
  children?: DatasetTreeNode[];
  isLeaf?: boolean;
  dataType?: number;
  datasetType?: string;
}
