<template>
  <div class="vis-position">
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__label">对齐</div>
        <div
          v-if="activeFrame && !isFreeform(activeFrame) && activeGraph.gridItem"
          class="vis-form-inline__content--minus-32"
        >
          <div class="vis-form-field">
            <div class="vis-button-group flex-1 flex rounded-borders">
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.justifySelf === CrossAlignment.Start }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('justifySelf', CrossAlignment.Start)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-l.svg'" />
                <q-tooltip> 左对齐 </q-tooltip>
              </q-btn>
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.justifySelf === CrossAlignment.Center }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('justifySelf', CrossAlignment.Center)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-c.svg'" />
                <q-tooltip> 水平居中对齐 </q-tooltip>
              </q-btn>
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.justifySelf === CrossAlignment.End }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('justifySelf', CrossAlignment.End)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-r.svg'" />
                <q-tooltip> 右对齐 </q-tooltip>
              </q-btn>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-button-group flex-1 flex rounded-borders">
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.alignSelf === CrossAlignment.Start }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('alignSelf', CrossAlignment.Start)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-t.svg'" />
                <q-tooltip> 顶对齐 </q-tooltip>
              </q-btn>
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.alignSelf === CrossAlignment.Center }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('alignSelf', CrossAlignment.Center)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-vc.svg'" />
                <q-tooltip> 垂直居中对齐 </q-tooltip>
              </q-btn>
              <q-btn
                size="xs"
                class="flex-1"
                :class="[{ 'vis-btn-active': activeGraph.gridItem.alignSelf === CrossAlignment.End }]"
                :disabled="isFlex(activeFrame)"
                @click="onGridItemAlign('alignSelf', CrossAlignment.End)"
                dense
                flat
              >
                <img class="img-icon" :src="'./static-next/svg/position/align-b.svg'" />
                <q-tooltip> 底对齐 </q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
        <div class="vis-form-field__content auto-layout" v-else>
          <q-btn @click="align('left')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-l.svg'" />
            <q-tooltip> 左对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('center')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-c.svg'" />
            <q-tooltip> 水平居中对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('right')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-r.svg'" />
            <q-tooltip> 右对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('top')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-t.svg'" />
            <q-tooltip> 顶对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('middle')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-vc.svg'" />
            <q-tooltip> 垂直居中对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('bottom')" :disabled="isSingleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-b.svg'" />
            <q-tooltip> 底对齐 </q-tooltip>
          </q-btn>
          <q-btn @click="align('verticalSpace')" :disabled="isMultipleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-v.svg'" />
            <q-tooltip> 垂直平均分布 </q-tooltip>
          </q-btn>
          <q-btn @click="align('horizontalSpace')" :disabled="isMultipleAlign" flat dense>
            <img class="img-icon" :src="'./static-next/svg/position/align-h.svg'" />
            <q-tooltip> 水平平均分布 </q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>

    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">坐标</div>
          <div class="vis-form-field__content">
            <vis-number v-model="transform.translate[0]" icon="hticon-vis-letter-x" />
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-number v-model="transform.translate[1]" icon="hticon-vis-letter-y" />
          </div>
        </div>
      </div>
    </div>

    <template v-if="constraints && activeGraph.parent">
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">约束</div>
            <div class="vis-form-field__content">
              <div class="vis-constraints">
                <div
                  :class="{
                    hand: true,
                    l: true,
                    active: [HoriConsType.Left, HoriConsType.Stretch].includes(constraints.horizontal)
                  }"
                  @click="onChangeHori(HoriConsType.Left)"
                ></div>
                <div
                  :class="{
                    hand: true,
                    r: true,
                    active: [HoriConsType.Right, HoriConsType.Stretch].includes(constraints.horizontal)
                  }"
                  @click="onChangeHori(HoriConsType.Right)"
                ></div>
                <div
                  :class="{
                    hand: true,
                    h: true,
                    active: constraints.horizontal === HoriConsType.Center
                  }"
                  @click="onChangeHori(HoriConsType.Center)"
                ></div>

                <div
                  :class="{
                    hand: true,
                    t: true,
                    active: [VertConsType.Top, VertConsType.Stretch].includes(constraints.vertical)
                  }"
                  @click="onChangeVert(VertConsType.Top)"
                ></div>
                <div
                  :class="{
                    hand: true,
                    b: true,
                    active: [VertConsType.Bottom, VertConsType.Stretch].includes(constraints.vertical)
                  }"
                  @click="onChangeVert(VertConsType.Bottom)"
                ></div>
                <div
                  :class="{
                    hand: true,
                    v: true,
                    active: constraints.vertical === VertConsType.Center
                  }"
                  @click="onChangeVert(VertConsType.Center)"
                ></div>
              </div>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content column">
              <vis-select v-model="constraints.horizontal" :options="horiOptions" @change="onChangeHori"> </vis-select>
              <vis-select v-model="constraints.vertical" :options="vertOptions" @change="onChangeVert"> </vis-select>
            </div>
          </div>
        </div>
      </div>
      <div class="vis-form-inline">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label text-right">固定行为</div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__content">
              <vis-select v-model="constraints.scrollBehavior" :options="behaviorOptions"> </vis-select>
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field--width-50">
          <div class="vis-form-field__label">旋转</div>
          <div class="vis-form-field__content">
            <vis-number
              v-model="transform.rotate"
              @change="onChangeRotate"
              icon="hticon-vis-rotate"
              :min="-180"
              :max="180"
              suffix="°"
            />
          </div>
        </div>
        <div class="vis-form-field btn-group-field">
          <div class="vis-form-field__content">
            <q-btn-group flat>
              <q-btn @click="onRotateStep(-90)">
                <ht-icon class="vis-icon" name="hticon-vis-flip-h" />
              </q-btn>
              <q-separator vertical inset class="!m-0" />
              <q-btn @click="onRotateStep(90)">
                <ht-icon class="vis-icon" name="hticon-vis-flip-v" />
              </q-btn>
            </q-btn-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
