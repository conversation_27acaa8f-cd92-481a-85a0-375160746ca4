import { HttpApiService, responseReject, responseResult, type ResponseResult, responseData } from '@hetu/http';
import { GeoACLResource, MaterialGeo, MaterialGeoApi } from '../models';
import { createSingleClass } from '@hetu/core';

/**
 * 素材库-地理坐标系服务类
 */

export class MaterialGeoServiceCtor extends HttpApiService<MaterialGeoApi> {
  httpApi = new MaterialGeoApi();

  httpModuleKey = 'geo';

  acl = new GeoACLResource();

  /**
   * 列表数据
   */
  list(): Promise<MaterialGeo[]> {
    return this.http.get(this.api.list).then(responseData, responseReject);
  }

  /**
   * 详情
   * @param id id
   */
  info(id: string): Promise<MaterialGeo> {
    return this.http.get(this.api.info, { params: { id } }).then(responseData, responseReject);
  }

  /**
   * 保存
   * @param geoInfo geo对象
   */
  save(geoInfo: MaterialGeo): Promise<ResponseResult | null> {
    return this.http.post(this.api.save, geoInfo).then(responseResult, responseReject);
  }

  /**
   * 删除
   * @param id
   */
  delete(ids: string[]): Promise<ResponseResult | null> {
    return this.http.delete(this.api.delete, { data: { ids } }).then(responseResult, responseReject);
  }

  /**
   * 校验区域编码唯一
   * @param id id
   * @param cityCode 区域编码
   */
  codeCheck(id: string, cityCode: string) {
    return this.http.get(this.api.codeCheck, { params: { id, cityCode } }).then(responseResult, responseReject);
  }

  /**
   * 校验区域名称唯一
   * @param id id
   * @param name 区域名称
   */
  nameCheck(id: string, name: string) {
    return this.http.get(this.api.nameCheck, { params: { id, name } }).then(responseResult, responseReject);
  }

  /**
   * 启用禁用地理位置
   * @param id id
   * @param active 状态
   */
  updateActive(id: string, active: 0 | 1) {
    return this.http.get(this.api.updateActive, { params: { id, active } }).then(responseResult, responseReject);
  }
}

export const MaterialGeoService = createSingleClass(MaterialGeoServiceCtor);
