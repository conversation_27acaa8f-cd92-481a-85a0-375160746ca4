import type { AxiosRequestConfig } from 'axios';
import { merge } from 'lodash-es';
import { setupApiConfig, type RootHttpApi } from './api';
import type { HttpRequestInterceptorType, HttpResponseInterceptorType } from './interceptor';

export interface HttpRequestConfig extends AxiosRequestConfig {}

/**
 * HTTP 模块配置
 */
export interface HttpConfig {
  /** 请求配置 */
  requestConfig: HttpRequestConfig;

  /** API 配置 */
  apiConfig: RootHttpApi | (() => RootHttpApi);

  /** 拦截器 */
  interceptors: Partial<{
    /** 请求拦截器 */
    request: HttpRequestInterceptorType[];

    /** 响应拦截器 */
    response: HttpResponseInterceptorType[];
  }>;
}

/** 默认配置 */
export const HTTP_DEFAULT_CONFIG = {
  requestConfig: {
    timeout: 60000
  }
} as HttpConfig;

export function setupHttpConfig(config?: Partial<HttpConfig>) {
  config && config.apiConfig && setupApiConfig(config.apiConfig);

  return merge({}, HTTP_DEFAULT_CONFIG, config);
}
