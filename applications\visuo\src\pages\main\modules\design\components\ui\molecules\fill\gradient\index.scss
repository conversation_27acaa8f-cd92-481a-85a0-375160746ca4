// Fill Gradient组件样式
@import '../../../../index.scss';

.#{$vis-prefix}-fill-gradient {
  .gradient-box {
    position: relative;
    height: $color-silder-width;
    border-radius: $color-silder-width;
    background: url('data:image/svg+xml;utf8,%3Csvg%20width%3D%226%22%20height%3D%226%22%20viewBox%3D%220%200%206%206%22%20fill%3D%22none%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cpath%20d%3D%22M0%200H3V3H0V0Z%22%20fill%3D%22%23E1E1E1%22/%3E%3Cpath%20d%3D%22M3%200H6V3H3V0Z%22%20fill%3D%22white%22/%3E%3Cpath%20d%3D%22M3%203H6V6H3V3Z%22%20fill%3D%22%23E1E1E1%22/%3E%3Cpath%20d%3D%22M0%203H3V6H0V3Z%22%20fill%3D%22white%22/%3E%3C/svg%3E%0A');
  }

  .gradient-slider {
    position: absolute;
    left: $color-silder-width * -0.5;
    right: $color-silder-width * -0.5;
    height: 100%;
    border-radius: $color-silder-width;
  }

  .gradient-points {
    display: flex;
    position: absolute;
    top: 0;
    align-items: center;
    width: 100%;
    height: $color-silder-width;

    .gradient-point {
      display: block;
      position: absolute;
      width: $color-silder-width * 2;
      height: $color-silder-width * 2;
      transform: translateX(-50%) scale(0.5);
      border-radius: $color-silder-width;
      background: transparent;
      border: 1px solid #ccc;
      box-shadow:
        inset 0 0 0 1px #ccc,
        inset 0 0 0 3px white,
        inset 0 0 0 4px #ccc;

      &.active::after {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        width: $color-silder-width * 2;
        height: $color-silder-width * 2;
        border-radius: $color-silder-width;
        border: 1px solid #3670f7;
        box-shadow:
          inset 0 0 0 1px #3670f7,
          inset 0 0 0 3px white,
          inset 0 0 0 4px #ccc;
      }
    }
  }
}