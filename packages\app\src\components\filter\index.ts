import { computed, defineComponent, onMounted, ref, type PropType, watch } from 'vue';
import {
  FilterType,
  FilterTypeName,
  NormalFilter,
  QueryFilter,
  RangeFilter,
  ScopeFilter,
  getFilterTypes,
  AxisField,
  type Limit
} from '../../models';
import Normal from './normal/normal.vue';
import Query from './query/query.vue';
import Range from './range/range.vue';
import Scope from './scope/scope.vue';
import { cloneDeep } from 'lodash-es';

/**
 * 筛选器
 * <AUTHOR>
 */
export default defineComponent({
  name: 'ht-filter',
  components: {
    'ht-filter-normal': Normal,
    'ht-filter-query': Query,
    'ht-filter-range': Range,
    'ht-filter-scope': Scope
  },
  props: {
    field: {
      type: Object as PropType<AxisField>,
      required: true
    },
    fieldValues: {
      type: Object as PropType<Promise<{ columnValue: any }[]>>,
      required: true
    },
    fieldKey: {
      type: String,
      default: 'fieldName'
    },
    open: {
      type: Boolean
    },
    fieldAlias: {
      type: String
    }
  },
  emits: ['onClose', 'onConfirm', 'setLimit'],
  setup(props, { emit }) {
    const visible = ref(false);

    const tab = ref(FilterType.Normal);

    const field = computed(() => cloneDeep(props.field));

    const fieldName = computed(() => props.field[props.fieldKey as keyof AxisField]);

    const filterTypes = computed(() => getFilterTypes(field.value));

    const filterExpressions = ref<(NormalFilter | QueryFilter | RangeFilter | ScopeFilter)[]>([]);

    const filterRule = ref('and');

    const includeNull = ref(0);
    onMounted(() => {
      filterTypes.value?.forEach((type) => {
        const expression = field.value.filterExpression?.find((fe) => fe.filterType === type);
        switch (type) {
          case FilterType.Normal:
            filterExpressions.value.push(expression || new NormalFilter());
            break;
          case FilterType.Query:
            filterExpressions.value.push(expression || new QueryFilter());
            break;

          case FilterType.Range:
            filterExpressions.value.push(expression || new RangeFilter());
            break;
          case FilterType.Scope:
            filterExpressions.value.push(expression || new ScopeFilter());
            break;
        }
      });

      field.value.filterRule && (filterRule.value = field.value.filterRule);
      field.value.includeNull && (includeNull.value = field.value.includeNull);
    });

    watch(
      () => props.open,
      () => {
        visible.value = props.open;
      },
      { immediate: true }
    );

    const onClose = () => {
      visible.value = false;
      emit('onClose');
    };

    const onConfirm = () => {
      visible.value = false;
      emit(
        'onConfirm',
        Object.assign({}, field.value, { filterExpression: filterExpressions, filterRule, includeNull })
      );
    };

    /**
     * 抛出分页对象
     * @param limit
     */
    const changeLimit = (limit: Limit) => {
      emit('setLimit', limit);
    };

    return {
      fieldName,
      visible,
      tab,
      filterTypes,
      FilterTypeName,
      filterExpressions,
      filterRule,
      includeNull,
      onClose,
      onConfirm,
      changeLimit
    };
  }
});
