$ht-space-prefix-cls: '#{$ht-prefix}-space';

.#{$ht-space-prefix-cls} {
  @apply relative flex items-center px-4 py-2 rounded-lg border-2 border-solid border-transparent cursor-pointer 
    duration-300 text-font-secondary bg-grey-light hover:text-font-primary hover:bg-primary-lighter;

  &:not(.is-active) .q-avatar {
    opacity: 0.65;
  }

  &.is-active {
    @apply text-primary border-primary bg-primary-lighter;
  }

  &s {
    &.is-mini {
      .#{$ht-space-prefix-cls} {
        .q-avatar {
          & + * {
            display: none;
          }

          ~ i {
            position: absolute;
            right: 0;
          }
        }
      }
    }

    &-popup {
      .q-scrollarea {
        @apply w-87.5 h-74;
      }
    }
  }

  &-team {
    @apply flex items-center pa-2 border-2 border-transparent rounded-lg cursor-pointer 
      duration-300 hover:bg-grey-light;

    &.is-active {
      @apply bg-primary-lighter;
    }

    & + & {
      @apply mt-2;
    }
  }
}
