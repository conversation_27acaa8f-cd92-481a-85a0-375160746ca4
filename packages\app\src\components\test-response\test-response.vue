<template>
  <q-dialog class="ht-test-response" v-model="responseShow" seamless maximized position="bottom">
    <q-splitter
      v-model="splitter"
      horizontal
      reverse
      :limits="limits"
      class="!pointer-events-none"
      separator-class="pointer-events-auto"
      style="width: 100%; height: 100vh"
    >
      <template v-slot:before> </template>
      <template v-slot:separator>
        <q-icon name="more_horiz" class="-mt-1" size="16px" />
        <q-icon name="more_horiz" class="mt-1" size="16px" />
      </template>
      <template v-slot:after>
        <q-card square flat bordered class="column pointer-events-auto full-width full-height">
          <q-card-section class="row justify-between items-center q-px-sm q-py-none">
            <div class="text-14px">执行结果</div>
            <div>
              <q-btn flat round icon="delete" size="sm" @click="emit('clearInfo')" />
              <q-btn flat round icon="close" size="sm" @click="emit('close')" />
            </div>
          </q-card-section>
          <q-separator />
          <q-scroll-area class="q-pa-sm col">
            <slot></slot>
          </q-scroll-area>
          <q-card-section class="row items-center no-wrap q-py-none"> </q-card-section>
        </q-card>
      </template>
    </q-splitter>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineOptions, defineProps, defineEmits } from 'vue';

defineOptions({ name: 'ht-test-response' });
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  limits: {
    type: Array,
    default: () => [25, 75]
  }
});

const emit = defineEmits(['close', 'clearInfo']);

const splitter = ref(30);

const responseShow = computed(() => props.show);
</script>
