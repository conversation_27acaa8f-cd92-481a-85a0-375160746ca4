import type { Directive } from '@hetu/core';
import { ACLService } from './acl.service';
import type { DirectiveBinding } from 'vue';

/**
 * 权限访问控制指令
 * <AUTHOR>
 */
export const ACLDirective: Directive = {
  name: 'acl',

  mounted(el: HTMLElement, binding: DirectiveBinding) {
    // 验证权限
    const pass = ACLService.can(binding.value);

    if (pass) return;

    if (el.remove) {
      el.remove();
    } else {
      const parentElement = el.parentElement as HTMLElement;
      parentElement && parentElement.removeChild(el);
    }
  }
};
