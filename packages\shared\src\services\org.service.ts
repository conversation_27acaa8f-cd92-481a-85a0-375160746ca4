import { HttpApiService, responseData, responseReject, responseResult, type ResponseResult } from '@hetu/http';
import { Dept, OrgApi, OrgSelectorTreeNode, type Profile } from '../models';
import { createSingleClass } from '@hetu/core';

class OrgServiceCtor<T extends OrgApi = OrgApi> extends HttpApiService<OrgApi | T> {
  httpApi: OrgApi = new OrgApi();

  httpModuleKey = 'org';

  cacheIdMap = new Map<string, string>();

  /**
   * 获取deptTree数据
   * @param url
   */
  getDepts(url?: string) {
    return this.http.get(url ? url : this.api.depts).then(responseData<Dept[]>, responseReject);
  }

  /**
   * 获取选择数据
   * @param deptId
   * @param url
   */
  getMembers(deptId?: string, url?: string) {
    return this.http
      .get(url ? url : this.api.members, { params: { deptId } })
      .then(responseData<OrgSelectorTreeNode[]>, responseReject);
  }
  /**
   * 根据fullName查询当前团队下的成员
   * @param fullName
   */
  getSearch(fullName?: string) {
    return this.http
      .get(this.api.search, { params: { fullName } })
      .then(responseData<OrgSelectorTreeNode[]>, responseReject);
  }

  /**
   * 当前登录人个人信息查询
   */
  getProfile() {
    return this.http.get(this.api.info).then(responseData<Profile>, responseReject);
  }

  /**
   * 根据 id 获取名称
   * @param id
   */
  async getName(id: string | string[]) {
    id = Array.isArray(id) ? id : [id];
    const unreadyIds = Array.from(new Set(id)).filter((key) => key && !this.cacheIdMap.get(key));
    if (unreadyIds.length) {
      const data = await this.http
        .get(this.api.getName, { params: { id: unreadyIds.join(';') } })
        .then(responseData<Profile[]>, responseReject);

      data &&
        data.forEach((user) => {
          this.cacheIdMap.set(user.id, user.fullName);
        });
    }
    return id.map((key) => this.cacheIdMap.get(key) || '');
  }

  /**
   * 密码策略验证
   * @param password
   * @param headers 传token
   */
  checkPasswordStrategy(password: string, headers?: any) {
    return this.http
      .get(this.api.checkPasswordStrategy, {
        params: {
          password
        },
        headers
      })
      .then(responseResult, responseReject);
  }
}

/**
 * 组织机构服务类
 * <AUTHOR>
 */
export const OrgService = createSingleClass(OrgServiceCtor);
