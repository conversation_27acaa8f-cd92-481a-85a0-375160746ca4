import { defineComponent, ref, type Ref, onMounted, watch, onBeforeMount, computed } from 'vue';
import { DatasetService, type DatasetTreeNode } from '@hetu/metadata-shared';
import { createTree } from '@hetu/util';
import type { QSelect, QMenu } from 'quasar';
import { useDesignStore } from '../../../../../../stores';
import { useDocumentStore } from '@vis/document-core';

/**
 * 数据资源下拉菜单
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-dataset-tree',
  components: {},
  props: {
    modelValue: {
      type: String,
      required: true
    },
    name: {
      type: String
    }
  },
  emits: ['onSelect', 'update:modelValue'],
  setup(props, { emit }) {
    const { rightWidth } = useDesignStore();

    const { staticDataSheets } = useDocumentStore();

    const staticDataList = computed(() =>
      staticDataSheets.value.map((item) => ({ title: item, id: item, datasetType: 'static' }))
    );

    /** 所有节点树 */
    const nodeTree: Ref<DatasetTreeNode[]> = ref([]);

    const loading = ref(true);

    const search = ref('');

    const selected = ref({ label: props.name, value: props.modelValue });

    const selectRef = ref<QSelect>();
    const menuRef = ref<QMenu>();

    // 获取数据资源列表
    const loadDataset = async () => {
      loading.value = true;
      await DatasetService.getDatasetList()
        .then((data) => {
          if (data) {
            nodeTree.value = createTree([...data.directorys, ...data.files]);
          }
        })
        .finally(() => {
          loading.value = false;
        });
    };

    onBeforeMount(() => {
      loadDataset();
    });

    const onSelect = (data: DatasetTreeNode & { expanded?: boolean }) => {
      if (!data.dataType) {
        // 如果不是文件夹，选择该节点并关闭弹窗
        selected.value.value = data.id;
        selected.value.label = data.title;
        emit('update:modelValue', data.id);
        emit('onSelect', data);
        // 手动关闭弹窗
        menuRef.value?.hide();
      } else {
        // 如果是文件夹，展开/收起节点，弹窗不关闭
        data.expanded = !data.expanded;
      }
    };

    watch(
      () => props.name,
      () => {
        selected.value.label = props.name;
      }
    );

    return {
      loading,
      nodeTree,
      staticDataList,
      search,
      selected,
      selectRef,
      menuRef,
      loadDataset,
      onSelect,
      rightWidth
    };
  }
});
