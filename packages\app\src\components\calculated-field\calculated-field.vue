<template>
  <q-dialog
    class="ht-app-calculated-field"
    v-model="visible"
    no-backdrop-dismiss
    no-esc-dismiss
    @drop.stop="() => false"
  >
    <q-card class="w-700px h-500px !max-w-70%">
      <div class="fit column">
        <div class="row justify-between items-center q-py-sm q-px-md">
          <div class="text-dark font-medium text-lg">计算字段</div>
          <div class="col-auto q-ml-md">
            <q-input
              v-model="calculatedFieldName"
              class="ht-app-calculated-field__input full-width text-subtitle1"
              ref="fieldNameRef"
              placeholder="请输入计算字段名称"
              maxlength="30"
              no-error-icon
              :rules="nameRules"
              dense
            >
            </q-input>
            <!-- 用于按输入内容长度撑起文本框宽度 -->
            <div class="q-field__input invisible min-h0!">{{ calculatedFieldName }}</div>
          </div>
          <q-btn class="q-ml-auto" icon="close" size="sm" @click="close" rounded dense flat />
        </div>
        <q-separator />
        <div class="col column q-pa-md max-w-100%">
          <q-card class="fit" bordered flat>
            <div class="fit row no-wrap">
              <div class="q-pa-sm column">
                <q-input
                  v-model="filter"
                  ref="filterRef"
                  class="ht-field--small"
                  placeholder="请输入关键字"
                  maxlength="50"
                  no-error-icon
                  dense
                >
                  <template #prepend>
                    <q-icon name="search" />
                  </template>
                </q-input>
                <q-scroll-area class="q-mt-sm col" content-active-style="width:100%" content-style="width:100%">
                  <q-tree
                    class="full-width ht-tree text-center"
                    :nodes="fieldTree"
                    :filter="filter"
                    node-key="fieldOriginal"
                    label-key="fieldAlias"
                    dense
                    no-selection-unset
                    control-color="primary"
                    no-connectors
                    default-expand-all
                  >
                    <template v-slot:default-header="scope">
                      <div
                        v-if="!scope.node.children"
                        class="row justify-between items-center hover:bg-grey-light full-width q-px-xs q-py-xs cursor-move"
                        draggable="true"
                        @dragstart="onDrag($event, scope.node)"
                      >
                        <span class="col row items-center q-py-xs line-height-none">
                          <span class="q-pl-sm col text-left select-none ellipsis">
                            <q-tooltip :delay="1000">
                              {{ `${scope.node.fieldAlias} ( ${scope.node.fieldOriginal} )` }}
                            </q-tooltip>
                            <span class="line-height-16px">
                              {{ scope.node.fieldAlias }}
                            </span>
                          </span>
                        </span>
                      </div>
                      <span class="line-height-32px q-pl-xs" v-else>{{
                        fieldTypeMap[scope.node.fieldDatatype]?.fieldName || scope.node.fieldDatatype
                      }}</span>
                    </template>
                  </q-tree>
                </q-scroll-area>
              </div>
              <q-separator vertical />
              <form
                :class="['col', 'relative-position', editorError ? 'border border-solid border-negative' : '']"
                autocorrect="off"
                autocapitalize="off"
                autocomplete="off"
                spellcheck="false"
              >
                <q-editor
                  ref="editorRef"
                  class="fit"
                  v-model="editor"
                  placeholder="请输入计算字段内容"
                  content-class="fit"
                  :content-style="{ 'word-break': 'break-all' }"
                  :toolbar="[]"
                  :definitions="{
                    redo: {
                      tip: 'Save your work',
                      icon: 'save',
                      label: 'Save',
                      handler: () => {}
                    },
                    undo: {
                      tip: 'Upload to cloud',
                      icon: 'cloud_upload',
                      label: 'Upload',
                      handler: () => {}
                    }
                  }"
                  flat
                  @paste="onPaste"
                  @dragover="(e:Event)=>e.preventDefault()"
                  @click="editorClick"
                  @drop.stop="drop"
                  @keydown.enter="(e:Event)=>e.preventDefault()"
                  @keydown="onKeydown"
                  @update:model-value="editorChange"
                />
                <div v-if="editorError" class="absolute left-0 text-negative q-pt-xs">{{ editorError }}</div>
              </form>
            </div>
          </q-card>
        </div>
        <div class="row justify-end q-pb-md q-px-md q-gutter-sm">
          <q-btn unelevated @click="close">
            <span class="">取消</span>
          </q-btn>
          <q-btn color="primary" unelevated @click="onConfirm">
            <span class="">确定</span>
          </q-btn>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" src="./calculated-field.ts"></script>

<style src="./calculated-field.scss" lang="scss"></style>
