import type { HttpApi } from '@hetu/http';

/**
 * 数据表 API
 * <AUTHOR>
 */
export class DatatableApi implements HttpApi {
  /** `/metadata` */
  public static readonly API_PREFIX = `/metadata` as const;

  // 数据源中的表[get] id
  list = `${DatatableApi.API_PREFIX}/table` as const;

  // 数据源中的表[get] id 导入后的
  importList = `${DatatableApi.API_PREFIX}/table/list` as const;

  // 查询表中的字段信息[get] id tableName
  fields = `${DatatableApi.API_PREFIX}/table/field` as const;

  // 通过数据id查询查询tableName
  getListFromDataframe = '/dataframe/dataset/table/list';

  [key: string]: any;
}
