import { computed, defineComponent, ref } from 'vue';
import { useDesignStore } from '../../../stores';

/**
 * 侧边栏
 */
export default defineComponent({
  name: 'VisStoreSidebar',
  setup() {
    const { selectedMenu } = useDesignStore();

    const isNode = ref(false);

    const tabs = computed(() => {
      if (isNode.value) {
        return [
          {
            name: '1',
            icon: 'o_hub'
          },
          {
            name: '2',
            icon: 'o_token'
          },
          {
            name: '3',
            icon: 'o_polyline'
          }
        ];
      }
      return [
        {
          name: 'pages',
          icon: 'hticon-vis-frame'
        },
        {
          name: 'charts',
          icon: 'hticon-vis-chart'
        },
        {
          name: 'widgets',
          icon: 'hticon-vis-navigation-c'
        },
        {
          name: 'templates',
          icon: 'hticon-vis-navigation'
        },
        {
          name: 'images',
          icon: 'hticon-vis-navigation-pic'
        },
        {
          name: 'favorites',
          icon: 'hticon-vis-navigation-t'
        },
        {
          name: 'materials',
          icon: 'hticon-vis-navigation-d'
        },
        {
          name: 'svgs',
          icon: 'hticon-vis-canvas-s '
        }
      ];
    });

    return {
      selectedMenu,
      tabs
    };
  }
});
