import { FillPaints } from './fill';

/**
 * 基础字体类
 */
export class Font {
  fontSize: number = 12;
  fontFamily: string = '默认字体';
  fontWeight: number = 400;
  color: FillPaints = new FillPaints();

  constructor(size?: number, weight?: number, fontFamily?: string, color?: FillPaints) {
    size && (this.fontSize = size);
    fontFamily && (this.fontFamily = fontFamily);
    weight && (this.fontWeight = weight);
    color && (this.color = color);
  }
}

/**
 * 文本类
 */
export class Text extends Font {
  /** 字间距 */
  letterSpacing: number = 0;

  /** 行高 */
  lineHeight: number = 0;

  /** 对齐方式 */
  alignHorizontal = TextAlign.Left;

  /** 适应方式 */
  adapt = TextAdapt.Fixed;

  /** 斜体 */
  italic: boolean = false;

  /** 下划线 */
  underlined: boolean = false;

  /** 删除线 */
  through: boolean = false;

  /** 垂直对齐 */
  alignVertical = VerticalAlign.Top;

  /** 首行缩进 */
  textIndent: number = 0;

  /** 列表样式 none-无, ordered-有序列表, unordered-无序列表 */
  // listStyle: 'none' | 'ordered' | 'unordered' = 'none';

  constructor(size?: number, weight?: number, fontFamily?: string) {
    super(size, weight, fontFamily);
  }
}

export enum TextAlign {
  /** 左对齐 */
  Left = 'left',
  /** 居中对齐 */
  Center = 'center',
  /** 右对齐 */
  Right = 'right',
  /** 两端对齐 */
  Justify = 'justify'
}

export enum TextAdapt {
  /** 单行模式 */
  Single = 'single',
  /** 自动高度 */
  Auto = 'auto',
  /** 固定高度 */
  Fixed = 'fixed',
  /** 省略文本 */
  Ellipsis = 'ellipsis'
}

export enum VerticalAlign {
  /** 顶部对齐 */
  Top = 'top',
  /** 居中对齐 */
  Center = 'center',
  /** 底部对齐 */
  Bottom = 'bottom'
}

/**
 * 字体配置
 */
export class FontConfig {
  /**
   * 字号
   */
  fontSizes: number[] = [12, 14, 16, 18, 20, 24, 28, 36, 48, 72];

  /**
   * 字重
   */
  fontWeights: { label: string; value: number }[] = [
    { label: 'Light', value: 300 },
    { label: 'Normal', value: 400 },
    { label: 'Medium', value: 500 },
    { label: 'Semi Bold', value: 600 },
    { label: 'Bold', value: 700 },
    { label: 'Extra Bold', value: 800 },
    { label: 'Heavy', value: 900 }
  ];

  // 设置斜体、下划线和删除线
  fontStyles = [
    { value: 'italic', label: '', icon: 'vis-format_italic', tip: '斜体' },
    { value: 'underlined', label: '', icon: 'vis-format_underline', tip: '下划线' },
    { value: 'through', label: '', icon: 'vis-format_through', tip: '删除线' }
  ];
}
