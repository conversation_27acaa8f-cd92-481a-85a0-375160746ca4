import { useAction } from '../../hooks';
import { useActionStore } from '../../stores';
import { defineComponent, ref } from 'vue';

/**
 * 工具栏
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-dock',
  components: {},
  props: {},
  setup(props) {
    const actionStore = useActionStore();

    const actions = actionStore.actions;

    const toolbars = ref([
      [['move', 'hand', 'scale'], 'frame', 'textbox'],
      ['design', 'dhyana', 'node', 'interact']
    ]);

    const groupActive = (keys: string[]) => {
      const activeKey = keys.find((key) => actions.value[key].active);
      return activeKey ? actions.value[activeKey] : actions.value[keys[0]];
    };

    const onHandler = (actionKey: string) => {
      // 调用操作方法
      (useAction() as any)[actionKey] && (useAction() as any)[actionKey]();
    };

    return { actions, toolbars, groupActive, onHandler };
  }
});
