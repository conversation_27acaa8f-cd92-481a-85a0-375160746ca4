import { responseData, responseReject, HttpApiService } from '@hetu/http';
import { DatasourceACLResource, DatasourceApi, type DatasourceFile, DatasourceType } from './models';
import type { FileData } from '@hetu/platform-shared';
import { createSingleClass } from '@hetu/core';

/**
 * 数据源服务类
 * <AUTHOR>
 */
export class DatasourceServiceCtor<T extends DatasourceApi = DatasourceApi> extends HttpApiService<DatasourceApi | T> {
  httpApi: DatasourceApi = new DatasourceApi();

  httpModuleKey = 'datasource';

  acl = new DatasourceACLResource();

  /**
   * 根据类型查询数据源列表
   * @param datasourceType 要查询的数据源类型
   */
  getList(...types: DatasourceType[]): Promise<FileData<DatasourceFile>> {
    return this.http
      .get(this.api.list, { params: { datasourceType: types.join(';') } })
      .then(responseData, responseReject);
  }
  /**
   * 根据类型查询数据源下拉列表
   * @param datasourceType 要查询的数据源类型
   */
  getSelectionList(...types: DatasourceType[]): Promise<FileData<DatasourceFile>> {
    return this.http
      .get(this.api.selectionList, { params: { datasourceType: types.join(';') } })
      .then(responseData, responseReject);
  }
}

export const DatasourceService = createSingleClass(DatasourceServiceCtor);
