import type { RootHttpApi } from '@hetu/http';
import { Layout } from './layout';

export interface Config {
  /**
   * 应用信息
   */
  app: App;
  /**
   * 布局
   */
  layout: Layout;
  /**
   * API
   */
  api: RootHttpApi;

  [key: string]: any;
}

export interface App {
  /**
   * 应用名
   */
  name?: string;
  /**
   * 应用名(EN)
   */
  ename?: string;
  /**
   * 公司
   */
  company?: string;
  /**
   * 年份
   */
  year?: number;
  /**
   * 版权
   */
  copyright?: string;
  /**
   * 版本
   */
  version?: string;

  /**
   * 登录是否需要验证码
   */
  loginValidatorCode?: boolean;

  /**
   * 登录页面地址
   */
  loginUrl?: string;

  /**
   * 京ICP备
   */
  icp?: string;

  /**
   * 京公网安备
   */
  pns?: string;
  /**
   * 京ICP备链接
   */
  icpLink?: string;

  /**
   * 京公网安备链接
   */
  pnsLink?: string;

  [key: string]: any;
}

export interface User {
  name?: string;
  fullName?: string;
  avatar?: string;
  email?: string;
  [key: string]: any;
}
