// 插件不支持 es module
const custom = require('@digitalroute/cz-conventional-changelog-for-jira/configurable');

const types = {
  feat: {
    description: '新功能(feature)',
    title: 'Features'
  },
  fix: {
    description: '修补bug',
    title: 'Bug Fixes'
  },
  docs: {
    description: '文档(documentation)',
    title: 'Documentation'
  },
  style: {
    description: '格式(不影响代码运行的变动)',
    title: 'Style'
  },
  refactor: {
    description: '重构(即不是新增功能, 也不是修改bug的代码变动)',
    title: 'Code Refactoring'
  },
  // test: {
  //   description: '测试',
  //   title: 'Tests'
  // },
  // build: {
  //   description: 'Changes that affect the build system or external dependencies (npm, webpack, typescript)',
  //   title: 'Builds'
  // },
  // ci: {
  //   description: 'Changes to our CI configuration files and scripts (NOTE: Does not bump the version)',
  //   title: 'Continuous Integrations'
  // },
  chore: {
    description: '构建过程或辅助工具的变动',
    title: 'Chores'
  },
  revert: {
    description: '撤销 commit',
    title: 'Reverts'
  },
  release: {
    description: '发布版本',
    title: 'Release'
  }
  // 性能优化
  // perf: {
  //   description: 'Improvements that will make your code perform better',
  //   title: 'Performance'
  // }
};

module.exports = custom({
  types,
  jiraOptional: true,
  jiraPrefix: 'DATAV',
  skipScope: false,
  customScope: true,
  scopes: ['*', 'build', 'deps'],
  maxHeaderWidth: 100
});
