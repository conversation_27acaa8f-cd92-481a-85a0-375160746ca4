import { defineComponent, ref } from 'vue';

/**
 * 组件库面板头部
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-store-header',
  setup() {
    const title = ref('这是一个大屏');

    const options = ref([
      { label: '这是一个大屏', value: 'value1' },
      { label: '这是一个大屏', value: 'value2' }
    ]);

    const expanded = ref(['这是一个大屏']);

    const simple = [
      {
        label: '这是一个大屏1',
        children: [
          {
            label: '这是一个大屏11',
            icon: 'o_folder',
            children: [{ label: '这是一个大屏111' }, { label: '这是一个大屏112' }]
          }
        ]
      }
    ];
    return {
      title,
      options,
      expanded,
      simple
    };
  }
});
