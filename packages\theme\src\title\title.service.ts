import { useThemeConfig } from '../config';
import { NavService } from '../nav/nav.service';
import { useRouterWithout } from '@hetu/core';

/**
 * 浏览器标题
 * <AUTHOR>
 */
export class DocTitleConfig {
  /** 前缀 */
  prefix = '';
  /** 后缀 */
  suffix = '';
  /** 分隔符 */
  separator = ' - ';
  /** 是否反转 */
  reverse = false;
  /** 默认标题名 */
  default = `No Page Name`;
}

export class TitleService {
  private static title = '';

  static get config() {
    const themeConfig = useThemeConfig();

    if (typeof themeConfig.docTitleConfig === 'function') {
      const config = themeConfig.docTitleConfig();

      return new Proxy(new DocTitleConfig(), {
        get(target, p: keyof DocTitleConfig) {
          return config[p] ?? target[p];
        }
      });
    }

    return themeConfig.docTitleConfig;
  }

  /**
   * 设置浏览器标题
   * @param title - 标题
   */
  static setTitle(title?: string) {
    if (!title) {
      if (NavService.activeMenus && NavService.activeMenus.length) {
        const activeMenu = NavService.getMenu(NavService.activeMenus[0]);
        title = activeMenu?.menuName;
      }
      if (!title) {
        title = useRouterWithout().currentRoute.value.meta?.title;
      }
    }

    title = title || this.config.default;
    if (this.title !== title) {
      this.title = title as string;
      let newTitles = [];
      this.config.prefix && newTitles.push(this.config.prefix);
      newTitles.push(title);
      this.config.suffix && newTitles.push(this.config.suffix);
      this.config.reverse && (newTitles = newTitles.reverse());
      document.title = newTitles.join(this.config.separator);
    }
  }
}
