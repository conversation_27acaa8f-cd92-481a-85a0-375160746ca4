<template>
  <Moveable
    className="vis-design-canvas--moveable"
    ref="moveableRef"
    @render="onRender"
    :target="moveableTargets"
    @changeTargets="onChangeTargets"
    :draggable="isDraggable"
    :edge="edge"
    @dragStart="onDragStart"
    @drag="onDrag"
    @dragEnd="onDragEnd"
    :resizable="true"
    :keepRatio="isKeepRatio"
    :useResizeObserver="true"
    :useMutationObserver="true"
    @resize="onResize"
    @resizeStart="onResizeStart"
    @resizeEnd="onResizeEnd"
    :roundable="isRoundable"
    :isDisplayShadowRoundControls="'horizontal'"
    :roundClickable="'control'"
    :roundPadding="-15"
    @round="onRound"
    :scrollable="true"
    :scrollOptions="scrollOptions"
    @scroll="onScroll"
    :renderDirections="renderDirections"
    :defaultGroupOrigin="'center center'"
    @dragGroup="onDragGroup"
    @dragGroupStart="onDragGroupStart"
    @dragGroupEnd="onDragGroupEnd"
    @resizeGroupEnd="onResizeGroupEnd"
    @rotateGroupStart="canvasState.isRotate = true"
    @rotateGroupEnd="onRotateGroupEnd"
    @roundGroupEnd="onRoundGroupEnd"
    :rotatable="true"
    :rotationPosition="['top-left', 'top-right', 'bottom-left', 'bottom-right']"
    @rotate="onRotate"
    @rotateStart="canvasState.isRotate = true"
    @rotateEnd="canvasState.isRotate = false"
    :snappable="true"
    :elementGuidelines="elementGuidelines"
    :verticalGuidelines="verticalGuidelines"
    :horizontalGuidelines="horizontalGuidelines"
    :snapContainer="snapContainer"
    :snapDirections="snapDirections"
    :elementSnapDirections="snapDirections"
    :ables="[dimensionViewable, customRotation]"
    :props="{ dimensionViewable: true, customRotation: true }"
  />
</template>

<script lang="ts" src="./moveable.ts"></script>
<style lang="scss" src="./moveable.scss"></style>
<!--
    :target: 当前选中的目标
    @changeTargets：目标改变事件

    :edge: 是否可以通过边缘缩放和调整大小

    :resizable: 允许改变尺寸
    :keepRatio: 保持纵横比
    :useResizeObserver：自动检测目标尺寸变化
    :useMutationObserver：检测内联样式变化
    @resize：改变尺寸时触发
    @rotateStart: 旋转开始时触发
    @rotateEnd: 旋转结束时触发

    :rotatable: 允许旋转
    :rotationTarget: 自定义旋转控制点的类名  :rotationTarget="'.rotation-handle'"
    :rotationPosition: 指定旋转控制点的位置
    @rotate: 旋转时事件


    :defaultGroupOrigin: 默认的组原点

    :snappable: 是否可以吸附到指南线上
    :elementGuidelines: 对齐的元素[DOM]
    :verticalGuidelines: 垂直方向辅助线[number]
    :horizontalGuidelines: 水平方向辅助线[number]
    :snapContainer: 对齐的容器
    :snapDirections: 在容器里的对齐方向
    :elementSnapDirections: 元素的对齐方向
  -->
<!-- 
  :rotationPosition="['top-left', 'top-right', 'bottom-left', 'bottom-right']"
     :defaultGroupOrigin="'20% 20%'"
    @dragOrigin="onDragOrigin"
    :transformOrigin="'20% 20%'"
    :originDraggable="true"
    :originRelative="true"

  :clippable="true"
    :dragWithClip="0"
    :defaultClipPath="'inset'"
    @clip="onMoveableClip" -->
