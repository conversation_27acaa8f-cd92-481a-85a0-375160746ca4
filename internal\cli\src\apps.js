const fs = require('fs');
const chalk = require('chalk');
const { normalizePath } = require('vite');
const { resolve, getBranch, isProduction } = require('./utils');

/** 应用目录 */
const appsFolder = 'applications';

/** 基础平台配置 */
const BaseAppConfig = {
  key: 'base',
  name: '河图平台'
};

/** 河图应用标识 */
const Applications = {
  /** 应用平台 */
  Platform: 'platform',

  /** 帐号登录&注册 */
  Account: 'account',

  /** 团队管理后台 */
  Team: 'team',

  /** 个人设置 */
  Profile: 'profile',

  /** 邀请确认 */
  Invite: 'invite',

  /** 资源广场 */
  Market: 'market',

  /** 空间(SSO) */
  Space: 'space',

  // -------- 子应用 --------

  /** 多维分析应用 */
  BI: 'bi',

  /** 数据门户应用 */
  Portal: 'portal',

  /** API 应用 */
  Api: 'api',

  /** 数据管理应用 */
  Dataframe: 'dataframe',

  /** 可视化大屏应用 */
  Screen: 'screen',

  /** 数据工厂应用 */
  Analysis: 'analysis',

  /** 指标模型应用 */
  Indicators: 'indicators',

  /** 智能报警应用 */
  Alarm: 'alarm',

  /** 数字孪生应用 */
  Twin: 'twin',

  /** 可视化设计 */
  Visuo: 'visuo'
};

/**
 * @typedef Config
 * @type {object}
 * @property {string} name - 应用名称.
 * @property {string} url - 仓库地址.
 * @property {string|undefined} branch - 指定分支, 默认同主仓库当前分支.
 * @property {string|undefined} path - 放置路径, 默认值: `<appsFolder>/<appName>`.
 */
/**
 * 子应用仓库配置
 * @type Record<string, Config>
 */
const SubAppConfigs = {
  // [Applications.BI]: {
  //   name: '多维分析'
  // },

  // [Applications.Portal]: {
  //   name: '数据门户'
  // },

  // [Applications.Api]: {
  //   name: 'API'
  // },

  // [Applications.Dataframe]: {
  //   name: '数据管理'
  // },

  // [Applications.Twin]: {
  //   name: '数字孪生'
  // },

  [Applications.Visuo]: {
    name: '设计文件'
  }
};

const getAppConfig = (name) => {
  return SubAppConfigs[name] || {};
};

const getAppPath = (name) => {
  const config = getAppConfig(name);
  return config.path || `${appsFolder}/${name}`;
};

const getAppVersion = (name) => {
  return require(`${process.cwd()}/${getAppPath(name)}/package.json`).version;
};

const getAppHooks = (name) => {
  try {
    const script = require(`${process.cwd()}/${getAppPath(name)}/scripts/hooks`);
    return script && script.hooks ? script.hooks : false;
  } catch (error) {
    // console.log(error);
  }
  return false;
};

const getAppCurrentBranch = (name = BaseAppConfig.key) => {
  const current = JSON.parse(process.env.APP_CURRENT_INFO || '{}');

  current[name] = current[name] || {};
  let { branch } = current[name];

  if (!branch) {
    if (name === BaseAppConfig.key) {
      branch = getBranch();
    } else {
      branch = getBranch(getAppPath(name));
      const config = getAppConfig(name);
      current[name].isMatch = branch === (config.branch ?? getAppCurrentBranch().branch);
    }
    current[name].branch = branch;
  }

  process.env.APP_CURRENT_INFO = JSON.stringify(current);
  return { branch, isMatch: current[name].isMatch };
};

const appHooks = {};
const appPages = [];
const appAlias = {};
const appSassAdditionalDatas = [];
const appManualChunks = [
  (id) => {
    if (
      ['node_modules/quasar', 'vite/modulepreload-polyfill', 'commonjsHelpers.js'].find((item) => id.includes(item))
    ) {
      return 'vendor';
    } else if (
      ['core', 'http', 'acl', 'auth', 'theme', 'util', 'boot', 'shared', 'metadata'].find((item) =>
        id.startsWith(normalizePath(resolve(`./packages/${item}`)))
      )
    ) {
      // _export_sfc 打包时会按生成后的文件名排序优先放入, 要避免被放入应用内的文件中需保持此文件名排序靠前
      return '_hetu';
    } else if (
      ['app', 'cooperation'].find((item) => id.startsWith(normalizePath(resolve(`./packages/${item}`)))) ||
      id.includes('node_modules/@quasar/quasar-ui-qiconpicker/dist')
    ) {
      return 'platform-app';
    } else if (id.startsWith(normalizePath(resolve('./packages/material')))) {
      return 'platform-material';
    }
  }
];
const appVersions = {};

const initHooks = (appNames) => {
  const pagePaths = {};
  appNames.forEach((app) => {
    // 当前工程无平台基础页面
    if (app === 'base') {
      appVersions[Applications.Platform] = require(resolve('./package.json')).version;
      return;
    }

    const hooks = getAppHooks(app);
    if (hooks) {
      appHooks[app] = hooks;

      const version = hooks.version || getAppVersion(app);
      appVersions[app] = version;

      if (hooks.getPages) {
        const subPages = hooks.getPages();
        Object.keys(subPages).forEach((page) => {
          const pageConfig = subPages[page];
          // pageConfig.entry = [`./${appsFolder}/polyfills.ts`].concat(pageConfig.entry);
          // pageConfig.chunks = [AppCacheGroups.vendors.name, AppCacheGroups.core.name].concat(pageConfig.chunks || []);

          let name = '';
          if (app === 'base') {
            // 平台
            name = `${page === 'main' ? hooks.appKey || Applications.Platform : page}`;
          } else {
            // 子应用
            name = `${hooks.appKey || app}${page === 'main' ? '' : `-${page}`}`;
          }
          pageConfig.name = name;
          pageConfig.data = pageConfig.data || {};
          pageConfig.data.dark = pageConfig.data.dark || false;
          pageConfig.data.baseURI = new Array(pageConfig.filename.split('/').length - 1).fill('../').join('');
          appPages.push(pageConfig);
          pagePaths[name] = pageConfig.filename;
        });
      }

      // 应用环境变量
      // if (hooks.getEnvs) {
      //   const envs = hooks.getEnvs();
      //   for (key in envs) {
      //     process.env[key] = envs[key];
      //   }
      // }

      // alias
      if (hooks.getAlias) {
        const result = hooks.getAlias();
        result && Object.assign(appAlias, result);
      }

      // sass additionalData
      if (hooks.sassAdditionalData) {
        appSassAdditionalDatas.push(hooks.sassAdditionalData);
      }

      // manualChunks
      if (hooks.getManualChunks) {
        appManualChunks.push(hooks.getManualChunks);
      }
    }
  });

  // 应用标识环境变量
  Object.values(Applications).forEach((key) => {
    if (key === Applications.Example && !appNames.includes(Applications.Example)) {
      return;
    }

    process.env[`VITE_HETU_${key.toUpperCase()}_KEY`] = key;

    if (!pagePaths[key]) {
      pagePaths[key] = `${
        Applications.Platform === key ? 'index' : Applications.Invite === key ? key : `${key}/index`
      }.html`;
    }
  });

  // 应用页面环境变量: 子应用只有主页配置
  process.env.VITE_HETU_PAGES = JSON.stringify(pagePaths);
};

const banner = async (appNames) => {
  console.log(chalk.blueBright('----------------------------------------------'));
  console.log(
    chalk.blueBright(
      `
                 ╦ ╦╔═╗╔╦╗╦ ╦
                 ╠═╣║╣  ║ ║ ║
                 ╩ ╩╚═╝ ╩ ╚═╝
  `
    )
  );
  // console.log(chalk.bgBlue(' Apps '), appNames, '\n');
  const info = {};
  appNames.forEach((app) => {
    // const { branch, isMatch } = getAppCurrentBranch(app);
    // info[app === BaseAppConfig.key || isMatch ? app : chalk.yellow(app)] = {
    //   名称: app === BaseAppConfig.key ? BaseAppConfig.name : SubAppConfigs[app].name,
    //   分支: branch
    // };
    info[app] = {
      名称: app === BaseAppConfig.key ? BaseAppConfig.name : SubAppConfigs[app].name
    };
  });

  console.table(info);
  console.log(chalk.blueBright('----------------------------------------------'));
};

const getExistsApps = () => {
  return Object.keys(SubAppConfigs).filter((appName) => {
    return fs.existsSync(`${getAppPath(appName)}`);
  });
};

const getUnExistsApps = () => {
  return Object.keys(SubAppConfigs).filter((appName) => {
    return !fs.existsSync(`${getAppPath(appName)}`);
  });
};

const setupApps = () => {
  const targetApps = [BaseAppConfig.key];
  let targets = process.env.VITE_TARGET_APPS;
  if (targets === 'true') {
    // 当前工程下已拉取的子应用
    targets = getExistsApps();
  } else if (typeof targets === 'string') {
    // 指定应用, 多个应用使用逗号分隔
    targets = targets.split(',');
  } else {
    console.log(chalk.red('未选择应用'));
    process.exit(0);
  }

  targets && targetApps.push(...targets);

  banner(targetApps);
  initHooks(targetApps);

  if (isProduction) {
    fs.writeFileSync(`${resolve('./public')}/versions.json`, JSON.stringify(appVersions, null, 2));
  }

  return {
    targetApps,
    appPages,
    appAlias,
    appSassAdditionalDatas,
    appManualChunks
  };
};

module.exports = {
  BaseAppConfig,
  Applications,
  SubAppConfigs,
  appHooks,
  appPages,
  appAlias,
  appSassAdditionalDatas,
  appManualChunks,

  getAppPath,
  getAppConfig,
  getAppHooks,
  getAppCurrentBranch,
  getExistsApps,
  getUnExistsApps,
  setupApps
};
