@font-face {
  font-family: 'hticon'; /* Project id 4274896 */
  src: url('iconfont.woff2?t=1731486632772') format('woff2'), url('iconfont.woff?t=1731486632772') format('woff'),
    url('iconfont.ttf?t=1731486632772') format('truetype');
}

[class*='hticon'] {
  font-family: 'hticon' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hticon-ai-assistant:before {
  content: '\e60a';
}

.hticon-reload:before {
  content: '\e788';
}

.hticon-copy:before {
  content: '\e8b0';
}

.hticon-sql:before {
  content: '\e6d1';
}

.hticon-chart:before {
  content: '\e659';
}

.hticon-area:before {
  content: '\e78a';
}

.hticon-time:before {
  content: '\e67d';
}

.hticon-datetime:before {
  content: '\e681';
}

.hticon-double:before {
  content: '\e6a8';
}

.hticon-date:before {
  content: '\e684';
}

.hticon-int:before {
  content: '\e743';
}

.hticon-location:before {
  content: '\e603';
}

.hticon-string:before {
  content: '\e689';
}

.hticon-boolean:before {
  content: '\e6be';
}

.hticon-number:before {
  content: '\e671';
}

.hticon-delete-outline:before {
  content: '\e62a';
}

.hticon-access-time:before {
  content: '\e628';
}

.hticon-map-china:before {
  content: '\e62e';
}

.hticon-block:before {
  content: '\e7a6';
}

.hticon-domain:before {
  content: '\e7b7';
}

.hticon-favorite-fill:before {
  content: '\e86a';
}

.hticon-widget:before {
  content: '\e652';
}

.hticon-analysis:before {
  content: '\e630';
}

.hticon-clear:before {
  content: '\e666';
}

.hticon-save:before {
  content: '\e7a8';
}

.hticon-publish:before {
  content: '\e629';
}

.hticon-import:before {
  content: '\e73d';
}

.hticon-export:before {
  content: '\e73f';
}

.hticon-app-platform:before {
  content: '\e613';
}

.hticon-manage-system:before {
  content: '\e65c';
}

.hticon-undo:before {
  content: '\e6ee';
}

.hticon-team:before {
  content: '\ebf5';
}

.hticon-exit:before {
  content: '\e60d';
}

.hticon-border-outer:before {
  content: '\e7a9';
}

.hticon-fire:before {
  content: '\e842';
}

.hticon-addusergroup:before {
  content: '\e663';
}

.hticon-adduser:before {
  content: '\e664';
}

.hticon-appstore-o:before {
  content: '\e665';
}

.hticon-list:before {
  content: '\ea3b';
}

.hticon-card-list:before {
  content: '\e688';
}

.hticon-hide:before {
  content: '\e83c';
}

.hticon-show:before {
  content: '\e66d';
}

.hticon-bulb:before {
  content: '\e7fa';
}

.hticon-image-error:before {
  content: '\e606';
}

.hticon-folder-open-fill:before {
  content: '\e860';
}

.hticon-folder-add-fill:before {
  content: '\e85e';
}

.hticon-folder-fill:before {
  content: '\e85f';
}

.hticon-help:before {
  content: '\e609';
}

.hticon-source-material:before {
  content: '\e717';
}

.hticon-favorite:before {
  content: '\e65e';
}

.hticon-data-source:before {
  content: '\e632';
}

.hticon-phone:before {
  content: '\e687';
}

.hticon-key:before {
  content: '\e8c9';
}

.hticon-dingtalk:before {
  content: '\e882';
}

.hticon-wechat:before {
  content: '\e883';
}
