import { computed, defineComponent, ref, type PropType } from 'vue';
import { FontConfig, useDocumentStore, type FormFix } from '@vis/document-core';
/**
 * 前后缀面板
 */
export default defineComponent({
  name: 'vis-config-form-fix',
  props: {
    options: {
      type: Object as PropType<FormFix>,
      required: true
    }
  },
  setup(props) {
    const computedOptions = computed({
      get() {
        return props.options;
      },
      set(value) {
        Object.assign(props.options, value);
      }
    });

    const isIcon = ref(!!computedOptions.value.icon);

    const showMenuType = ref(false);

    const handleType = (value: string) => {
      isIcon.value = value === 'icon';
    };

    const handleVisible = () => {
      computedOptions.value.show = !computedOptions.value.show;
    };

    const handleIconChange = (value: string) => {
      console.log('handleIconChange', value);
    };

    // #region 弹窗
    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const docStore = useDocumentStore();
    const fontFamilyOptions = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });
    const { fontWeights: fontWeightOptions, fontSizes: fontSizeOptions } = new FontConfig();

    // #endregion

    return {
      computedOptions,
      isIcon,
      showMenuType,
      handleType,

      handleVisible,
      handleIconChange,

      popupRef,
      popupShow,
      showPopup,
      fontFamilyOptions,
      fontWeightOptions,
      fontSizeOptions
    };
  }
});
