import { DirectionType, Frame, HoriConsType, VertConsType, type Graph } from '@vis/document-core';
import { useDesignStore } from '../stores';
import { computed } from 'vue';
import type { Records } from '@hetu/util';

/**
 * 约束
 * <AUTHOR>
 */
export const useConstraints = () => {
  const designStore = useDesignStore();
  const active = computed(() => designStore.active.value);
  const activeFrame = computed(() => active.value.frame);

  /**
   * 计算辅助线的位置,多值
   * @param graph
   */
  const guideLineStyles = (graph: Graph) => {
    // 约束必须在frame内
    if (!graph.constraints || !activeFrame.value) {
      return;
    }

    const left = graph.transform.translate[0];
    const top = graph.transform.translate[1];
    const { width, height } = graph;
    const primaryColor = '#4af';

    const leftLine = {
      position: 'absolute',
      width: `${left}px`,
      height: `1px`,
      transform: `translateX(-${left}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const rightLine = {
      position: 'absolute',
      width: `${activeFrame.value.width - left - width}px`,
      height: `1px`,
      transform: `translateX(${width}px)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const hLine = {
      position: 'absolute',
      width: `${width / 2}px`,
      height: `1px`,
      transform: `translateX(50%)`,
      top: `calc(${height}px / 2)`,
      borderTop: `1px dashed ${primaryColor}`
    };

    const topLine = {
      position: 'absolute',
      width: `1px`,
      height: `${top}px`,
      transform: `translateY(-${top}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const bottomLine = {
      position: 'absolute',
      width: `1px`,
      height: `${activeFrame.value.height - top - height}px`,
      transform: `translateY(${height}px)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const vLine = {
      position: 'absolute',
      width: `1px`,
      height: `${height / 2}px`,
      transform: `translateY(50%)`,
      left: `calc(${width}px / 2)`,
      borderLeft: `1px dashed ${primaryColor}`
    };

    const lines = [];
    switch (graph.constraints.horizontal) {
      case HoriConsType.Left:
        lines.push(leftLine);
        break;
      case HoriConsType.Right:
        lines.push(rightLine);
        break;
      case HoriConsType.Stretch:
        lines.push(...[leftLine, rightLine]);
        break;
      case HoriConsType.Center:
        lines.push(hLine);
        break;
    }

    switch (graph.constraints.vertical) {
      case VertConsType.Top:
        lines.push(topLine);
        break;
      case VertConsType.Bottom:
        lines.push(bottomLine);
        break;
      case VertConsType.Stretch:
        lines.push(...[topLine, bottomLine]);
        break;
      case VertConsType.Center:
        lines.push(vLine);
        break;
    }
    return lines;
  };

  /**
   * 是否是默认left、top约束
   * @param graph
   * @returns
   */
  const isDefaultConstraints = (graph: Graph) => {
    return (
      graph.constraints &&
      graph.constraints.horizontal === HoriConsType.Left &&
      graph.constraints.vertical === VertConsType.Top
    );
  };

  /**
   * 计算容器内子元素固定不变的值
   * @param graph
   * @return {"子元素id": RectPosition}
   */
  const getRectPosition = (graph: Graph) => {
    const { width, height } = graph;

    const positions: Records<RectPosition> = {};

    graph.children?.forEach((g) => {
      if (g.constraints) {
        // 子元素不变的数据，离父元素的距离
        const position = {} as RectPosition;

        const ch = g.constraints.horizontal;
        const cv = g.constraints.vertical;

        // ---- 水平----

        // 水平靠右固定：右侧不变的值
        if (ch === HoriConsType.Right) {
          position.right = width - g.width - g.transform.translate[0];
        }
        // 水平左右固定：左侧、右侧不变的值
        if (ch === HoriConsType.Stretch) {
          position.left = g.transform.translate[0];
          position.right = width - g.width - g.transform.translate[0];
        }
        // 水平居中：宽度不变，左右按比例变化
        if (ch === HoriConsType.Center) {
          position.pWidth = width;
          position.left = g.transform.translate[0];
        }
        // 水平跟随缩放：宽度、左右都按比例变化
        if (ch === HoriConsType.Scale) {
          position.pWidth = width;
          position.left = g.transform.translate[0];
          position.width = g.width;
        }

        // ---- 垂直----

        // 垂直靠下固定：下侧不变的值
        if (cv === VertConsType.Bottom) {
          position.bottom = height - g.height - g.transform.translate[1];
        }
        // 垂直上下固定：上侧、下侧不变的值
        if (cv === VertConsType.Stretch) {
          position.top = g.transform.translate[1];
          position.bottom = height - g.height - g.transform.translate[1];
        }
        // 垂直上下居中：高度不变，上下按比例变化
        if (cv === VertConsType.Center) {
          position.pHeight = height;
          position.top = g.transform.translate[1];
        }

        // 垂直跟随缩放：高度、上下都按比例变化
        if (ch === HoriConsType.Scale) {
          position.pHeight = height;
          position.top = g.transform.translate[1];
          position.height = g.height;
        }

        positions[g.id] = position;
      }
    });
    return positions;
  };

  /**
   * 父容器改变尺寸开始时，子元素根据约束改变位置大小
   * @param graph
   * @param positions
   */
  const calcGraphPosition = (graph: Graph, positions: Records<RectPosition>) => {
    const { width, height } = graph;
    if (graph.children) {
      graph.children.forEach((g) => {
        if (g.constraints) {
          const ch = g.constraints.horizontal;
          const cv = g.constraints.vertical;

          // 靠右固定，左侧距离变化
          if (ch === HoriConsType.Right) {
            g.transform.translate[0] = width - g.width - positions[g.id].right;
          }
          // 左右固定，宽度变化
          if (ch === HoriConsType.Stretch) {
            g.width = width - positions[g.id].left - positions[g.id].right;
          }
          // 水平居中，宽度不变，左右按比例变化
          if (ch === HoriConsType.Center) {
            // 比例计算：（父旧宽度-子宽度）/ （父新宽度-子宽度）；
            const ratio = (positions[g.id].pWidth - g.width) / (width - g.width);
            g.transform.translate[0] = Math.round(positions[g.id].left / ratio);
          }
          // 水平跟随缩放：宽度、左右都按比例变化
          if (ch === HoriConsType.Scale) {
            const ratio = positions[g.id].pWidth / width;
            g.transform.translate[0] = Math.round(positions[g.id].left / ratio);
            g.width = Math.round(positions[g.id].width / ratio);
          }

          // ------------------------------------------------------------------

          // 靠下固定，上侧距离变化
          if (cv === VertConsType.Bottom) {
            g.transform.translate[1] = height - g.height - positions[g.id].bottom;
          }
          // 上下固定，高度变化
          if (cv === VertConsType.Stretch) {
            g.height = height - positions[g.id].top - positions[g.id].bottom;
          }
          // 垂直居中，高度不变，上下按比例变化
          if (cv === VertConsType.Center) {
            // 比例计算：（父旧高度-子高度）/ （父新高度-子高度）；
            const ratio = (positions[g.id].pHeight - g.height) / (height - g.height);
            g.transform.translate[1] = Math.round(positions[g.id].top / ratio);
          }
          // 垂直跟随缩放：高度、上下都按比例变化
          if (cv === VertConsType.Scale) {
            const ratio = positions[g.id].pHeight / height;
            g.transform.translate[1] = Math.round(positions[g.id].top / ratio);
            g.height = Math.round(positions[g.id].height / ratio);
          }
        }
      });
    }
  };

  /**
   * frame尺寸改变后，重新计算子元素位置及大小
   * @param frame 容器
   * @param oldFrameWidth 不传是不计算约束
   * @param oldFrameHeight
   */
  const setChildrenPosition = (frame: Frame, oldFrameWidth?: number, oldFrameHeight?: number) => {
    const isChange =
      oldFrameWidth && oldFrameHeight && (frame.width !== oldFrameWidth || frame.height !== oldFrameHeight);
    // isChange && console.log('width:', oldFrameWidth, frame.width, 'height:', oldFrameHeight, frame.height);
    // 忽略了自动布局不需要计算
    if (!frame.ignoreAutoLayout) {
      // 自由布局时：子元素约束不是top、left需要重新计算
      if (frame.autoLayout.direction === DirectionType.Freeform) {
        if (isChange) {
          const conList = frame.children.filter((g) => !isDefaultConstraints(g));
          if (conList.length) {
            const rectPositions = getRectPosition({ ...frame, ...{ width: oldFrameWidth, height: oldFrameHeight } });
            calcGraphPosition(frame, rectPositions);
          }
        }
      } else {
        // 自动布局时，分两种情况：
        // 1. 子元素是flexItem时,根据dom重新计算位置和大小
        // 2. 子元素是忽略自动布局(约束不是top、left)时,需要根据frame的位置重新计算子元素的位置

        // 内部是否包含需要计算的约束
        let isConstraints = false;
        frame.children.forEach((g) => {
          if (g.ignoreAutoLayout) {
            if (!isDefaultConstraints(g)) {
              isConstraints = true;
            }
          } else {
            const gDom = document.querySelector(`[id="${g.id}"]`) as HTMLElement;
            if (gDom) {
              g.width = Math.round(gDom.clientWidth);
              g.height = Math.round(gDom.clientHeight);
              g.transform.translate = [Math.round(gDom.offsetLeft), Math.round(gDom.offsetTop)];
            }
          }
        });

        // 子元素配置了忽略自动布局，重新计算位置
        if (isConstraints && isChange) {
          const rectPositions = getRectPosition({ ...frame, ...{ width: oldFrameWidth, height: oldFrameHeight } });
          calcGraphPosition(frame, rectPositions);
        }
      }
    }
  };

  return {
    guideLineStyles,

    isDefaultConstraints,

    getRectPosition,
    calcGraphPosition,

    setChildrenPosition
  };
};

export type RectPosition = {
  // 子元素离父容器的距离
  left: number;
  right: number;
  top: number;
  bottom: number;
  // 子元素宽高
  width: number;
  height: number;
  // 父元素宽高
  pWidth: number;
  pHeight: number;
};
