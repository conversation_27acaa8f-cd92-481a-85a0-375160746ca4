<template>
  <div class="vis-config-style-option">
    <!-- 选项卡组件属性 -->
    <vis-config-tab-option v-if="activeBlock && activeBlock.type === 'tab'" :options="activeBlock.options" />
    <!-- 段落组件属性 -->
    <vis-config-paragraph-option
      v-if="activeBlock && activeBlock.type === 'paragraph'"
      :options="activeBlock.options"
    />
    <!-- 输入文本组件属性 -->
    <vis-config-input-option v-if="activeBlock && activeBlock.type === 'input'" :options="activeBlock.options" />
  </div>
</template>
<script lang="ts" src="./option.ts"></script>
