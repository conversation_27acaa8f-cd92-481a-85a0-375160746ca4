<template>
  <q-page class="ht-app-page row" :class="[dense && 'ht-app-page--dense']">
    <div v-if="(menus && menus.length) || $slots.menu" class="ht-app-page__sider column">
      <label class="text-h5 text-bold text-center q-mb-md">{{ title || $route.meta.title }}</label>
      <slot name="menu">
        <q-scroll-area class="col">
          <div
            v-for="menu in menus"
            :key="menu.id"
            class="ht-app-page__menu"
            :class="{ 'is-active': menu.menuCode === activeMenu }"
            @click="selectMenu(menu)"
          >
            <ht-icon v-if="menu.menuIcon?.startsWith('hticon-')" :name="menu.menuIcon.replace('hticon-', '')" />
            <q-icon v-else :name="menu.menuIcon" />
            <span class="q-ml-sm">{{ menu.menuName }}</span>
          </div>
        </q-scroll-area>
      </slot>
    </div>

    <slot name="main" v-if="$slots.main"></slot>

    <template v-else>
      <div class="ht-app-page__main col column relative q-gutter-y-md">
        <div class="ht-app-page__main-header row items-center">
          <label class="text-h5 text-bold" v-if="!((menus && menus.length) || $slots.menu) && title">
            {{ title }}
          </label>

          <slot name="header"></slot>
        </div>
        <q-scroll-area class="col">
          <div class="ht-app-page__main-body col column q-gutter-y-sm">
            <slot v-if="isEmptyBody" name="bodyEmpty">
              <ht-empty description="~ 空空如也 ~" />
            </slot>
            <slot v-else></slot>
          </div>
        </q-scroll-area>
        <div v-if="$slots.footer" class="ht-app-page__main-footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </template>

    <q-inner-loading :showing="loading" color="primary" />
  </q-page>
</template>
<style lang="scss" src="./page.scss"></style>

<script setup lang="ts">
import { type Menu } from '@hetu/theme';

defineOptions({ name: 'ht-app-page' });

defineProps<{
  title?: string;
  menus?: Menu[];
  activeMenu?: Menu['menuCode'];
  isEmptyBody?: boolean;
  loading?: boolean;
  dense?: boolean;
}>();

const emit = defineEmits(['select-menu']);
const selectMenu = (menu: Menu) => {
  emit('select-menu', menu);
};
</script>
