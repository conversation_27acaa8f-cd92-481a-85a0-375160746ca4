import { type Menu, NavService } from '@hetu/theme';
import { AppService } from './app.service';
import { MenuService } from './menu.service';
import type { RouteLocationNormalized, RouteLocationPathRaw } from 'vue-router';

export class RouteMenuService {
  static initialized = false;

  static menus: Menu[] = [];

  private static readonly menuIdKey = 'menuId';

  static clear() {
    this.initialized = false;
    NavService.setMenus([]);
  }

  /**
   * 加载菜单
   * @param spacePath 空间路由路径
   */
  static async loadMenus(spacePath?: string) {
    try {
      const menus = await MenuService.getNavs(AppService.appKey);
      if (spacePath) {
        menus.forEach((menu) => {
          if (menu.menuLink) {
            menu.menuLink = `${spacePath}${menu.menuLink === '/' ? '' : menu.menuLink}`;
          }
        });
      }
      this.menus = menus;
    } catch {
      this.menus = [];
    }
    NavService.setMenus(this.menus);
    this.initialized = true;

    return this.menus;
  }

  static can(route: RouteLocationNormalized): RouteLocationPathRaw | boolean {
    if (route.meta.guard === false || !this.initialized) {
      return true;
    }

    const { path, matched } = route;
    let menu = NavService.getMenu(path);
    if (!menu) {
      try {
        matched
          .slice(0)
          .reverse()
          .forEach((m) => {
            let matchPath = m.path;
            if (route.params) {
              Object.keys(route.params).forEach((key) => {
                matchPath = matchPath.replace(`:${key}`, route.params[key] as string);
              });
            }
            menu = NavService.getMenu(matchPath);
            if (menu) {
              throw new Error('break');
            }
          });
      } catch (error) {
        if (!(error instanceof Error) || error.message !== 'break') {
          throw new Error(error as any);
        }
      }
    }

    if (!menu && this.menuIdKey) {
      // maybe iframe...
      const menuIdFromPathParam = route.params[this.menuIdKey];

      if (menuIdFromPathParam) {
        menu = NavService.getMenu(menuIdFromPathParam as string);
      }
    }

    return (
      !!menu || {
        path: '/403',
        query: {
          redirect: route.fullPath
        }
      }
    );
  }
}
