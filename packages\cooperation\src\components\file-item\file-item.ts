import { defineComponent, type PropType } from 'vue';
import type { ACLResource } from '@hetu/acl';
import type { CooperationFile } from '../../models';
import { useFilePageStore } from '@hetu/platform-app';
import { SettingsService } from '@hetu/theme';

/**
 * 文件操作中的协作相关操作
 */
export default defineComponent({
  name: 'ht-co-file-item',
  props: {
    /** 文件 */
    file: {
      type: Object as PropType<CooperationFile>,
      required: true
    },
    acl: {
      type: Object as PropType<ACLResource<any>>,
      required: true
    }
  },
  setup(props) {
    const store = useFilePageStore();

    /** 是否允许退出协作操作: 非继承权限*/
    const isCanLeaveCo = () => {
      const currentPrivileges = props.file?.privileges?.filter((p) => !p.authMode);
      return currentPrivileges && !!currentPrivileges.find((p) => p.objectId === SettingsService.user.id);
    };

    const openCoPanel = () => store.setAction('openCoPanel', [props.file]);

    const leaveCo = () => store.setAction('leaveCo', [props.file]);

    return {
      isCanLeaveCo,
      openCoPanel,
      leaveCo
    };
  }
});
