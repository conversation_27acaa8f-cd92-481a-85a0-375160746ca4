import { createSingleClass } from '@hetu/core';
import {
  HttpApiService,
  type HttpApi,
  responseReject,
  responseData,
  type ResponseResult,
  responseResult
} from '@hetu/http';

class SpaceMoveApi implements HttpApi {
  API_PREFIX = '/api';
  DATAFRAME_API_PREFIX = '/dataframe';
}

/**
 * 文件跨空间移动服务类
 */
export class SpaceMoveServiceCtor extends HttpApiService<SpaceMoveApi> {
  httpApi = new SpaceMoveApi();
  httpModuleKey = 'space-move';

  /** 获取路径 */
  getPath = (type: string) => {
    const pathMap: any = {
      env: this.api.API_PREFIX,
      collection: this.api.API_PREFIX,
      dataset: this.api.DATAFRAME_API_PREFIX,
      datasource: this.api.DATAFRAME_API_PREFIX
    };
    return pathMap[type] || this.api.API_PREFIX;
  };

  /**
   * 团队/个人空间之间文件移动
   * @param id 文件id
   * @param targetDomain 目标团队标识
   * @param targetCatalogId 目标文件夹id
   * @param createCopy 是否创建副本并移动
   * @returns
   */
  spaceMove(
    id: string,
    targetDomain: string,
    targetCatalogId: string,
    createCopy: number,
    type: string
  ): Promise<ResponseResult | null> {
    return this.http
      .post(`${this.getPath(type)}/${type}/space/move`, { id, targetDomain, targetCatalogId, createCopy })
      .then(responseResult, responseReject);
  }
}

export const SpaceMoveService = createSingleClass(SpaceMoveServiceCtor);
