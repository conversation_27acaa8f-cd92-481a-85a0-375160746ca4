import { useAuthConfig, type AuthConfig } from '../config';
import { type TokenStore, type TokenModel, createStore } from '../store';

/**
 * `Token` 服务类
 * @static
 * <AUTHOR>
 */
export class TokenService {
  static get loginUrl() {
    return this.config.loginUrl;
  }

  static get config(): AuthConfig {
    return useAuthConfig();
  }

  private static _store?: TokenStore;

  private static get store(): TokenStore {
    if (!this._store) {
      this._store = createStore(this.config.store!);
    }

    return this._store;
  }

  static get(): TokenModel {
    return this.store.get(this.config.storeKey!);
  }

  static set(value: TokenModel): boolean {
    return this.store.set(this.config.storeKey!, value, this.config.cookieOptions);
  }

  static clear() {
    this.store.remove(this.config.storeKey!, this.config.cookieOptions);
  }
}
