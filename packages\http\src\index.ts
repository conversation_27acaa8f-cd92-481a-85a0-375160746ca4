import { createHttpClient, HttpClient } from './client';
import type { HttpConfig } from './config';

export { createHttpApiProxy } from './api';
export type { RootHttpApi, HttpApi } from './api';
export * from './api.service';
export * from './config';
export * from './base-model';
export * from './interceptor';
export * from './paging';
export * from './response';

export { HttpClient, createHttpClient };

export const HttpModule = {
  install(app: unknown, config?: HttpConfig) {
    createHttpClient(config);
  }
};
