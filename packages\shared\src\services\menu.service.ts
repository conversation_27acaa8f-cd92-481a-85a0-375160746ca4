import { responseData, HttpApiService, responseReject, type HttpApi } from '@hetu/http';
import { API_PREFIX } from '../models';
import { createSingleClass } from '@hetu/core';
import type { Menu } from '@hetu/theme';

class MenuApi implements HttpApi {
  navs = `${API_PREFIX}/system/menus`;
}

class MenuServiceCtor extends HttpApiService<MenuApi> {
  httpApi = new MenuApi();

  httpModuleKey = 'menu';

  /**
   * 获取权限过滤后的导航菜单
   * - 可按上级编码查询下级菜单
   * @param parentCode 上级编码
   */
  getNavs(parentCode: string): Promise<Menu[]> {
    return this.http.get(this.api.navs + `/${parentCode}`).then(responseData, responseReject);
  }
}

/**
 * 菜单服务类
 */
export const MenuService = createSingleClass(MenuServiceCtor);
