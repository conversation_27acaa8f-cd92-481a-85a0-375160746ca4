import type { Records } from '@hetu/util';
import {
  Effects,
  EffectsType,
  FillType,
  StrokeAlign,
  Text,
  TextAlign,
  TextEffects,
  VerticalAlign,
  type Stroke
} from '../models';
import { useFill } from './fill';
import { nextTick } from 'vue';
/**
 * ui组件样式解析
 * <AUTHOR>
 */
export const useUiStyle = () => {
  const { getFillStyle } = useFill();

  /**
   * 计算文本样式
   * @param font
   * @param isVertical 文本是否为垂直显示
   * @returns
   */
  const getTextStyle = (font: Text, isVertical?: boolean) => {
    const fontStyle: Records<string> = {};

    // 字号、字间距、首行缩进
    ['fontSize', 'letterSpacing', 'textIndent'].forEach((key) => {
      if (font[key as keyof Text]) {
        fontStyle[key] = `${font[key as keyof Text]}px`;
      }
    });
    // 行高
    fontStyle.lineHeight = font.lineHeight ? `${font.lineHeight}px` : '1.5';

    // 字体
    if (font.fontFamily) {
      fontStyle.fontFamily = font.fontFamily;
    }
    // 字重
    if (font.fontWeight) {
      fontStyle.fontWeight = `${font.fontWeight}`;
    }
    // 斜体
    if (font.italic) {
      fontStyle.fontStyle = 'italic';
    }
    // 下划线
    if (font.underlined) {
      fontStyle.textDecoration = 'underline';
    }
    // 删除线
    if (font.through) {
      fontStyle.textDecoration = 'line-through';
    }
    // 字体颜色
    if (font.color) {
      const fontColor = getFillStyle(font.color, true);
      if (JSON.stringify(fontColor) !== '{}') {
        if (fontColor.color) {
          fontStyle.color = fontColor.color;
        } else {
          fontStyle.color = 'transparent';
          Object.assign(fontStyle, fontColor);
          fontStyle.backgroundClip = 'text';
          fontStyle.webkitBackgroundClip = 'text';
        }
      }
    }

    fontStyle.display = 'flex';
    // 字体对齐
    if (font.alignHorizontal) {
      fontStyle.textAlign = font.alignHorizontal;
      fontStyle[isVertical ? 'alignItems' : 'justifyContent'] =
        font.alignHorizontal === TextAlign.Left
          ? 'flex-start'
          : font.alignHorizontal === TextAlign.Right
          ? 'flex-end'
          : 'center';
    }
    // 字体垂直对齐
    if (font.alignVertical) {
      fontStyle[isVertical ? 'justifyContent' : 'alignItems'] =
        font.alignVertical === VerticalAlign.Top
          ? 'flex-start'
          : font.alignVertical === VerticalAlign.Bottom
          ? 'flex-end'
          : 'center';
    }

    return fontStyle;
  };

  /**
   * 计算描边样式
   * @param stroke
   * @returns
   */
  const getStrokeStyle = (stroke?: Stroke) => {
    const strokeStyle: Records<string> = {
      borderWidth: 'initial',
      borderStyle: 'initial',
      borderColor: 'initial',
      borderImage: 'initial',
      borderImageSlice: 'initial',
      borderImageOutset: 'initial'
    };
    if (!stroke) {
      return strokeStyle;
    }
    const { position, align, paints, style } = stroke;

    const strokeFill = getFillStyle(paints);
    if (JSON.stringify(strokeFill) !== '{}') {
      strokeStyle.borderWidth = position.map((p) => `${p}px`).join(' ');
      strokeStyle.borderStyle = style;
      strokeStyle.borderImage = strokeFill.backgroundImage;
      // 纯色使用渐变背景，使outset值生效
      if (paints.type === FillType.Solid) {
        if (align === StrokeAlign.Inside) {
          strokeStyle.borderColor = strokeFill.backgroundColor;
          strokeStyle.borderImage = 'initial';
        } else {
          strokeStyle.borderImage = `linear-gradient(${strokeFill.backgroundColor} 0%, ${strokeFill.backgroundColor} 100%)`;
        }
      }
      strokeStyle.borderImageSlice = '1';
      strokeStyle.borderImageOutset = align === StrokeAlign.Center ? '0.5' : align === StrokeAlign.Outside ? '1' : '0';
    }

    return strokeStyle;
  };

  /**
   * 计算特效样式
   * @param effects
   * @returns
   */
  const getEffectStyle = (effects?: Effects) => {
    const effectStyle: Records<string> = {
      boxShadow: 'initial',
      filter: 'initial',
      backdropFilter: 'initial'
    };
    if (!effects?.visible) {
      return effectStyle;
    }
    const { type, color, offset, blur, spread } = effects;

    switch (type) {
      case EffectsType.Inset:
        effectStyle.boxShadow = `inset ${offset.x}px ${offset.y}px ${blur}px ${spread}px rgba(${color.r}, ${color.g}, ${color.b}, ${color.a})`;
        break;
      case EffectsType.Outset:
        effectStyle.boxShadow = `${offset.x}px ${offset.y}px ${blur}px ${spread}px rgba(${color.r}, ${color.g}, ${color.b}, ${color.a})`;
        break;
      case EffectsType.Blur:
        effectStyle.filter = `blur(${blur}px)`;
        break;
      case EffectsType.BgBlur:
        effectStyle.backdropFilter = `blur(${blur}px)`;
        break;
      default:
        break;
    }

    return effectStyle;
  };

  /**
   * 计算文本特效样式
   *
   */
  const getTextEffectStyle = (effects?: TextEffects, status?: string) => {
    if (!effects) return { textShadow: 'initial' };
    if (status) {
      return {
        textShadow: `${effects.offset.x}px ${effects.offset.y}px ${effects.blur}px  rgba(${effects.color.r}, ${effects.color.g}, ${effects.color.b}, ${effects.color.a})`
      };
    } else {
      // 普通状态
      return {
        textShadow: effects.visible
          ? `${effects.offset.x}px ${effects.offset.y}px ${effects.blur}px  rgba(${effects.color.r}, ${effects.color.g}, ${effects.color.b}, ${effects.color.a})`
          : 'initial'
      };
    }
  };

  /**
   * 将标准的样式转换为css变量
   * @param style
   * @param status 悬浮或激活状态值
   * @returns
   */
  const createStyleVar = (style: Records<string>, status?: string) => {
    const ret: Records<string> = {};

    // 处理border位置
    if (style.borderWidth) {
      const [bt, br, bb, bl] =
        style.borderWidth !== 'initial' ? `${style.borderWidth}`.split(' ').map((p) => parseInt(p)) : [0, 0, 0, 0];
      const multiple = parseFloat(`${style.borderImageOutset}`) || 0;
      style.borderOffsetLeft = `-${bl * multiple}px`;
      style.borderOffsetTop = `-${bt * multiple}px`;
      style.borderOffsetWidth = `calc(100% + ${(bl + br) * multiple}px)`;
      style.borderOffsetHeight = `calc(100% + ${(bt + bb) * multiple}px)`;
      style.borderImageOutset = '0';
    }
    Object.keys(style).forEach((key) => {
      const k = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      ret[`--${status ? status + '-' + k : k}`] = style[key] || '';
    });

    return ret;
  };

  /**
   * 设置文本溢出效果
   * @param overflow
   * @returns
   */
  const getTextOverflow = (overflow: number) => {
    switch (overflow) {
      case 0:
        return {
          // overflow: 'hidden',
          whiteSpace: 'nowrap'
        };
      case 1:
        return {
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          overflow: 'hidden'
        };
      case 2:
        return {
          whiteSpace: 'normal',
          wordBreak: 'break-all'
        };
      case 3:
        return {
          whiteSpace: 'nowrap'
        };
      default:
        break;
    }
  };

  return {
    getTextStyle,
    getStrokeStyle,
    getEffectStyle,
    getTextEffectStyle,
    createStyleVar,
    getTextOverflow
  };
};
