import {
  useFilePage,
  useFilePageStore,
  type FilePageOptions,
  CatalogService,
  CatalogFileService,
  HtAppMove,
  HtAppMoveSpace
} from '@hetu/platform-app';
import { computed, onUnmounted, watch, watchEffect, type WatchStopHandle } from 'vue';
import { type CooperationFile, SubFileACLResource, type CooperationFileData } from '../models';
import { CooperationACLService, CooperationService } from '../services';
import { useQuasar } from 'quasar';
import { PAGE_MENUS } from '../models';
import { HtCoPanelDialog } from '../components';
import { type Menu } from '@hetu/theme';
import { CooperationFileTree } from '../utils';
import { ACLService, type ACLResource } from '@hetu/acl';
import type { ResponseResult } from '@hetu/http';
import { SpaceService } from '@hetu/platform-shared';

export interface CoFilePageOptions<T extends CooperationFile, D = CooperationFileData<T>>
  extends FilePageOptions<T, D> {
  subAcl?: SubFileACLResource;
}

export const useCoFilePage = <T extends CooperationFile = CooperationFile>(options: CoFilePageOptions<T>) => {
  const $q = useQuasar();
  const filePage = useFilePage<T>(options);
  const store = useFilePageStore<T, CooperationFile, CooperationFileTree<T>>();

  //#region 权限
  const { subAcl } = options;
  const subFolderAcl = new Proxy(
    {},
    {
      get(target, key: string) {
        if (subAcl && subAcl[key]) {
          return subAcl[key];
        }

        return filePage.acl[key];
      }
    }
  ) as ACLResource<any>;

  /** 是否是子文件 */
  const isSubFile = (file: CooperationFile) => {
    return file.privileges?.find((item) => item.privilegeId === file.privilegeId && item.authMode === 1);
  };

  const folderAcl = (file?: CooperationFile) => {
    if (file && file.dataType && isSubFile(file)) {
      return subFolderAcl;
    }

    return filePage.acl;
  };

  const openFolder = computed(() => store.open.value || ({} as CooperationFile));
  const openFolderAcl = computed(() => folderAcl(store.open.value));
  //#endregion 权限

  //#region 菜单
  const menus = PAGE_MENUS;
  if (!filePage.query.value.mode) {
    filePage.query.value.mode = menus[0].menuCode;
  }

  const selectMenu = (menu: Menu) =>
    filePage.selectMenu(menu, () => {
      store.tree?.setMode(filePage.query.value.mode as number);
      filePage.setPreActiveNode();
    });
  //#endregion 菜单

  //#region 节点
  /** 复制文件 */
  const copy = <T extends CooperationFile = CooperationFile>(
    file: T,
    save: (name: string) => Promise<ResponseResult<{ id: string }> | null>,
    newFileAttr?: Partial<T>
  ) => {
    $q.dialog({
      title: `复制 - ${[file.title || '']}`,
      prompt: {
        model: `${[file.title || '']}(复制)`,
        type: 'text',
        outlined: true,
        dense: true,
        maxlength: 50
      },
      ok: '确定',
      cancel: '取消'
    }).onOk(async (name) => {
      const result = await save(name);
      if (result?.status === 'success') {
        const detail = await CatalogFileService.detail<CooperationFile>(options.category, result.data.id);
        // 添加节点
        const node = detail && store.tree.addNode({ ...detail, title: name, ...newFileAttr });

        store.setOpen(store.tree?.getNode(detail.parentId));
        filePage.search();

        $q.notify({ position: 'top', type: 'positive', message: result.message });
      }
    });
  };

  /**过滤文件夹是否有新建权限，当没有新建权限，设置节点不允许选中*/
  const filterMethod = (node: CooperationFile, filter: string) => {
    return CooperationACLService.allow({ ...options.acl.create, group: node.privilegeId });
  };

  /** 是否有自身空间下的移动 */
  const canMove = (node: CooperationFile) => {
    return CooperationACLService.allow({ ...options.acl.move, group: node.privilegeId });
  };
  // 当前空间的域名标识

  /**
   * 移动文件(夹)
   * @param file
   * @param isCrossSpace //是否跨空间移动
   * @returns
   */
  const move = async (file: CooperationFile, isCrossSpace: boolean = false) => {
    const domain = SpaceService.getDomainCode();

    if (!file) {
      return;
    }

    const sources = [file];
    const targets = store.tree.getFile()?.directorys.filter((item) => item.id !== 'DEMO') || [];

    $q.dialog({
      component: isCrossSpace ? HtAppMoveSpace : HtAppMove,
      componentProps: {
        title: file.title,
        parentId: store.open.value?.id,
        category: options.category,
        isCrossSpace,
        move: canMove(file),
        isOtherSpace: file.dataType ? false : domain ? ACLService.can(openFolderAcl.value.spaceMove) : true,
        sources,
        targets,
        filterMethod
      }
    }).onOk(
      async ({
        targetId,
        successIds,
        createCopy
      }: {
        targetId: string;
        successIds: string[];
        createCopy: number | undefined;
      }) => {
        // 勾选创建并移动副本，树不需要任何操作
        if (createCopy) return;
        if (!(successIds && successIds.length)) {
          return;
        }

        const detail = await (file.dataType
          ? CatalogService.detail<CooperationFile>(options.category, file.id)
          : CatalogFileService.detail<CooperationFile>(options.category, file.id));

        // 非款空间移动
        if (createCopy === undefined) {
          if (detail) {
            // 删除节点
            filePage.removeNode(file.id);
            // 添加节点
            store.tree.addNode(
              Object.assign(file, {
                parentId: targetId,
                privilegeId: detail.privilegeId,
                privileges: detail.privileges
              })
            );
          }
        } else {
          // 跨空间到其他团队，直接从树种移除当前节点
          if (detail) {
            // 删除节点
            filePage.removeNode(file.id);
          }
        }
      }
    );
  };
  //#endregion 节点

  //#region 协作
  /** 打开协作面板 */
  const openCoPanel = (file: CooperationFile) => {
    const isParentPrivilege = file.parentId && store.tree?.getFile(file.parentId);
    HtCoPanelDialog(file, filePage.category, options.acl, options.subAcl, !!isParentPrivilege);
  };

  /** 退出协作 */
  const leaveCo = (file: CooperationFile) => {
    $q.dialog({
      title: '提示',
      message: '确认退出协作吗？',
      ok: '确定',
      cancel: '取消'
    }).onOk(() => {
      const id = store.open.value?.id;
      return new Promise(() => {
        CooperationService.leaveCollaborator(file.id, file.title).then(async (res) => {
          if (res && res.status === 'success') {
            $q.notify({
              message: '退出成功',
              position: 'top',
              type: 'positive'
            });

            await init();
            filePage.setNode(id);
            // options.loadData();
          }
        });
      });
    });
  };
  //#endregion 协作

  const handleAction = (action: string, args: any[]) => {
    if (action === 'openCoPanel') {
      return openCoPanel(...(args as [CooperationFile]));
    } else if (action === 'leaveCo') {
      return leaveCo(...(args as [CooperationFile]));
    }

    filePage.handleAction(action, args);
  };

  let actionWatchStopHandle: WatchStopHandle;
  const createActionHandle = () => {
    actionWatchStopHandle = watchEffect(() => {
      if (!store.action.value) return;
      handleAction(store.action.value.name, store.action.value.args);
      store.action.value = undefined;
    });
  };

  let isFirstInit = true;
  let domainWatchStopHandle: WatchStopHandle;
  const init = async () => {
    if (isFirstInit) {
      createActionHandle();
      domainWatchStopHandle = watch(filePage.domain, () => init());
    }

    filePage.loading.value = true;

    const data = await options.loadData();
    if (data) {
      store.tree = new CooperationFileTree<T>(data, options.extraTreeKey);
    }

    const activeMenu = menus.find((m) => m.menuCode === filePage.query.value.mode);
    selectMenu(activeMenu ?? menus[0]);

    filePage.loading.value = false;

    if (isFirstInit) {
      isFirstInit = false;
    }
  };

  onUnmounted(() => {
    actionWatchStopHandle && actionWatchStopHandle();
    domainWatchStopHandle && domainWatchStopHandle();
    isFirstInit = true;
  });

  return {
    ...filePage,

    subAcl,
    folderAcl,
    openFolder,
    openFolderAcl,

    menus,
    selectMenu,

    copy,
    move,

    openCoPanel,
    leaveCo,

    handleAction,
    init
  };
};
