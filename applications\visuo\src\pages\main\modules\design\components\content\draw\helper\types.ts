import { ArrayChild, SingleChild } from './groups';

export type TargetGroupWithId = { groupId: string; children: TargetGroupsObject };
export type TargetRef = { current: HTMLElement | SVGElement | null };
export type TargetGroupsObject = Array<HTMLElement | SVGElement | TargetRef | TargetGroupsObject | TargetGroupWithId>;
export type TargetGroupsType = Array<HTMLElement | SVGElement | TargetGroupsType>;
export type GroupChild = SingleChild | ArrayChild;

export interface TargetList {
  raw(): GroupChild[];
  flatten(): Array<HTMLElement | SVGElement>;
  targets(): TargetGroupsType;
}

export type FlattedElement<T> = T extends any[] ? never : T;

/**
 * @function
 * @memberof Utils
 */
export function deepFlat<T extends any[]>(arr: T): Array<FlattedElement<T[0]>> {
  return arr.reduce((prev, cur) => {
    if (Array.isArray(cur)) {
      prev.push(...deepFlat(cur));
    } else {
      prev.push(cur);
    }
    return prev;
  }, [] as any[]);
}
