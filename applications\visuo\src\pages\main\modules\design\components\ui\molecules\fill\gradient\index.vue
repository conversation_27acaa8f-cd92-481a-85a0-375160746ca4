<template>
  <div class="vis-fill-gradient">
    <div class="row items-center q-mb-sm">
      <div class="gradient-box flex-1 ma-1.5">
        <div class="gradient-slider" :style="sliderBg"></div>
        <div class="gradient-points">
          <span
            v-for="(item, index) in stops"
            :key="index"
            class="gradient-point"
            :class="{ active: activeIndex === index }"
            :style="{ background: 'transparent', left: item.position * 100 + '%' }"
            @dblclick.stop="deleteStop"
          ></span>
        </div>
      </div>
      <q-btn flat dense class="!mx-0.5" @click="reverse">
        <ht-icon class="vis-icon" name="hticon-vis-switch" />
        <q-tooltip :offset="[0, 4]">反转</q-tooltip>
      </q-btn>
      <div v-if="rotationShow" class="w-16 column items-center">
        <vis-number
          v-model="rotation"
          @change="onChangeRotate"
          dense
          class="no-outline transparent"
          icon="hticon-vis-rotate"
          :min="-180"
          :max="180"
          suffix="°"
        ></vis-number>
      </div>
    </div>

    <vis-color-picker v-model="currentColor" @update:modelValue="updateColor" />

    <vis-fill-color-input v-if="stops[activeIndex]" v-model="stops[activeIndex].color" />

    <template v-if="palette.length">
      <q-separator class="q-mt-sm q-mb-md opacity-30" />
      <div class="row vis-color-picker">
        <div
          v-for="(item, index) in palette"
          :key="index"
          class="q-color-picker__cube"
          :style="calcBackground(item)"
          @click="setGradient(item)"
        ></div>
      </div>
    </template>

    <q-separator class="q-my-sm opacity-30" />
    <div class="text-xs mb-2">系统</div>
    <q-color
      class="vis-color-picker"
      v-model="currentColor"
      default-view="palette"
      :palette="SystemPalette"
      format-model="rgba"
      no-header
      no-footer
      square
      flat
      @update:modelValue="updateColor"
    />
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" scoped src="./index.scss"></style>
