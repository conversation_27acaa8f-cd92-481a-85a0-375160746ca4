import { computed, defineComponent, nextTick, ref } from 'vue';
import { LinkParam } from '@vis/document-core';
/**
 * 链接参数
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-link-param',
  props: {
    modelValue: {
      type: Array<LinkParam>,
      required: true
    }
  },
  setup(props, { emit }) {
    const computedModel = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    /**
     * 添加参数
     */
    const onAddParam = () => {
      const param: LinkParam = new LinkParam();
      if (!Array.isArray(computedModel.value)) {
        computedModel.value = [];
      }
      computedModel.value.push(param);
      showMenuType.value.push(false);
    };

    /**
     * 删除参数
     */
    const onDelParam = (index: number) => {
      computedModel.value.splice(index, 1);
      showMenuType.value.splice(index, 1);
    };

    const calcTypeShow = () => {
      const ret: boolean[] = [];
      computedModel.value.forEach((item, index) => {
        ret[index] = false;
      });
      return ret;
    };
    // 初始化menu显示数组
    const showMenuType = ref<boolean[]>(calcTypeShow());
    const changeType = (val: 'fix' | 'param', index: number) => {
      computedModel.value[index].type = val;
      computedModel.value[index].value = '';

      showMenuType.value[index] = false;
    };

    // 全局参数列表
    const paramOptions = ref([]);

    return {
      computedModel,
      onAddParam,
      onDelParam,

      showMenuType,
      changeType,

      paramOptions
    };
  }
});
