@import '../../../../index.scss';

.#{$vis-prefix}-link-param {
  .add-param {

    .add-btn {
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      border: 1px dotted $select-active-color;

      .vis-icon {
        font-size: 12px;
        color: $select-active-color !important;
      }

      &:hover {
        background-color: $input-bg !important;
      }
    }
  }


  .q-table__container {
    width: 100%;
    background-color: $input-bg;
  }

  .q-table--dense {
    .q-table__bottom {
      min-height: $component-height;
      padding: 0;
      border-top: none;
      font-size: $label-font-size;
    }

    .q-table {

      th,
      td {
        padding: 0 $secondary-margin;
        font-weight: 400;
        font-size: $label-font-size;
        color: $label-color;

        &:last-of-type {
          padding: 0;
        }
      }

      thead,
      tbody {

        tr,
        td {
          height: $component-height;
        }
      }

    }
  }
}