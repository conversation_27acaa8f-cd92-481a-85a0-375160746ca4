export const useColorTransfer = () => {
  /**
   * 将 RGB 转换为 HSB（HSV）
   * @param {number} r - 红色值 [0, 255]
   * @param {number} g - 绿色值 [0, 255]
   * @param {number} b - 蓝色值 [0, 255]
   * @returns {Object} { h: 色相 [0, 360), s: 饱和度 [0, 100], v: 亮度 [0, 100] }
   */
  const rgbToHsb = (r: number, g: number, b: number) => {
    // 归一化到 [0, 1]
    const rNorm = r / 255;
    const gNorm = g / 255;
    const bNorm = b / 255;

    // 计算最大值、最小值和差值
    const max = Math.max(rNorm, gNorm, bNorm);
    const min = Math.min(rNorm, gNorm, bNorm);
    const delta = max - min;

    // 计算色相 (H)
    let h = 0;
    if (delta !== 0) {
      if (max === rNorm) {
        h = 60 * (((gNorm - bNorm) / delta) % 6);
      } else if (max === gNorm) {
        h = 60 * ((bNorm - rNorm) / delta + 2);
      } else if (max === bNorm) {
        h = 60 * ((rNorm - gNorm) / delta + 4);
      }
    }
    if (h < 0) h += 360; // 确保 h ∈ [0, 360)

    h = parseInt(`${h}`);

    // 计算饱和度 (S)
    const s = max === 0 ? 0 : parseInt(`${(delta / max) * 100}`);

    // 计算亮度 (V)
    const v = parseInt(`${max * 100}`);

    return { h, s, v };
  };

  /**
   * 将 HSB（HSV）转换为 RGB
   * @param {number} h - 色相 [0, 360)
   * @param {number} s - 饱和度 [0, 100]
   * @param {number} v - 亮度 [0, 100]
   * @returns {Object} { r: 红色 [0, 255], g: 绿色 [0, 255], b: 蓝色 [0, 255] }
   */
  const hsbToRgb = (h: number, s: number, v: number) => {
    s /= 100;
    v /= 100;

    // 计算色相区间和中间值
    const k = Math.floor(h / 60) % 6;
    const f = h / 60 - k;

    // 计算 p, q, t
    const p = v * (1 - s);
    const q = v * (1 - f * s);
    const t = v * (1 - (1 - f) * s);

    let r = 0,
      g = 0,
      b = 0;
    switch (k) {
      case 0:
        [r, g, b] = [v, t, p];
        break;
      case 1:
        [r, g, b] = [q, v, p];
        break;
      case 2:
        [r, g, b] = [p, v, t];
        break;
      case 3:
        [r, g, b] = [p, q, v];
        break;
      case 4:
        [r, g, b] = [t, p, v];
        break;
      case 5:
        [r, g, b] = [v, p, q];
        break;
    }

    // 扩展到 [0, 255] 并四舍五入
    r = Math.round(r * 255);
    g = Math.round(g * 255);
    b = Math.round(b * 255);

    return { r, g, b };
  };

  /**
   * 将 RGB 转换为 HSL
   * @param {number} r - 红色值 [0, 255]
   * @param {number} g - 绿色值 [0, 255]
   * @param {number} b - 蓝色值 [0, 255]
   * @returns {Object} { h: 色相 [0, 360), s: 饱和度 [0, 100], l: 亮度 [0, 100] }
   */
  function rgbToHsl(r: number, g: number, b: number) {
    // 归一化到 [0, 1]
    r /= 255;
    g /= 255;
    b /= 255;

    // 计算最大值、最小值和差值
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const delta = max - min;

    // 计算亮度 (L)
    let h, s;
    const l = parseInt(`${((max + min) / 2) * 100}`);

    // 如果 delta = 0，说明是灰度色（无饱和度）
    if (delta === 0) {
      h = 0;
      s = 0;
    } else {
      // 计算饱和度 (S)
      s = parseInt(`${(l > 0.5 ? delta / (2 - max - min) : delta / (max + min)) * 100}`);

      // 计算色相 (H)
      if (max === r) {
        h = ((g - b) / delta) % 6;
      } else if (max === g) {
        h = (b - r) / delta + 2;
      } else {
        h = (r - g) / delta + 4;
      }
      h *= 60; // 转换为角度 [0, 360)
      if (h < 0) h += 360; // 确保 h ∈ [0, 360)
    }
    h = parseInt(`${h}`);

    return { h, s, l };
  }

  /**
   * 将 HSL 转换为 RGB
   * @param {number} h - 色相 [0, 360)
   * @param {number} s - 饱和度 [0, 100]
   * @param {number} l - 亮度 [0, 100]
   * @returns {Object} { r: 红色 [0, 255], g: 绿色 [0, 255], b: 蓝色 [0, 255] }
   */
  const hslToRgb = (h: number, s: number, l: number) => {
    s /= 100;
    l /= 100;
    // 如果饱和度为 0，直接返回灰色
    if (s === 0) {
      const gray = Math.round(l * 255);
      return { r: gray, g: gray, b: gray };
    }

    // 计算中间变量
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;

    // 将色相转换为 [0, 1] 区间
    const hueToRgb = (t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    // 计算 RGB 分量
    const r = hueToRgb(h / 360 + 1 / 3);
    const g = hueToRgb(h / 360);
    const b = hueToRgb(h / 360 - 1 / 3);

    // 扩展到 [0, 255] 并四舍五入
    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  /**
   * RGB 转 HEX
   * @param {number} r - 红色 [0, 255]
   * @param {number} g - 绿色 [0, 255]
   * @param {number} b - 蓝色 [0, 255]
   * @returns {string} HEX 颜色字符串
   */
  const rgbToHex = (r: number, g: number, b: number) => {
    // 确保数值在 [0, 255] 范围内
    r = Math.max(0, Math.min(255, r));
    g = Math.max(0, Math.min(255, g));
    b = Math.max(0, Math.min(255, b));

    // 转换为 2 位十六进制，不足补零
    const toHex = (c: number) => c.toString(16).padStart(2, '0');

    return `${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
  };

  /**
   * HEX 转 RGB
   * @param {string} hex - HEX 颜色字符串，如 "#ff00ff" 或 "#f0f"
   * @returns {Object} { r: 红色 [0, 255], g: 绿色 [0, 255], b: 蓝色 [0, 255] }
   */
  const hexToRgb = (hex: string) => {
    // 移除 # 并处理缩写形式（如 #abc → #aabbcc）
    let formattedHex = hex.replace(/^#/, '');
    if (formattedHex.length === 3) {
      formattedHex = formattedHex
        .split('')
        .map((c) => c + c)
        .join('');
    }

    // 解析为 R, G, B
    const num = parseInt(formattedHex, 16);
    const r = (num >> 16) & 255;
    const g = (num >> 8) & 255;
    const b = num & 255;

    return { r, g, b };
  };

  /**
   * RGBA 字符串转 RGB 对象
   * @param {string} rgbaStr - 如 "rgba(255, 0, 0, 0.5)"
   * @returns {Object} { r: [0, 255], g: [0, 255], b: [0, 255], a: [0, 1] }
   */
  const rgbaStrToRgb = (rgbaStr: string) => {
    // 提取数值部分
    const match = rgbaStr.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
    if (!match) throw new Error('Invalid RGBA string');

    // 解析 R, G, B, A（A 默认为 1）
    const r = Math.max(0, Math.min(255, parseInt(match[1], 10)));
    const g = Math.max(0, Math.min(255, parseInt(match[2], 10)));
    const b = Math.max(0, Math.min(255, parseInt(match[3], 10)));
    const a = match[4] ? Math.max(0, Math.min(1, parseFloat(match[4]))) : 1;

    return { r, g, b, a };
  };

  return {
    rgbToHsb,
    hsbToRgb,
    rgbToHsl,
    hslToRgb,
    rgbToHex,
    hexToRgb,
    rgbaStrToRgb
  };
};
