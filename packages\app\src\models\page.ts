import { type QueryOrder } from '@hetu/http';
import type { Menu } from '@hetu/theme';
import type { FileInfo } from './file';

export interface PageQuery<T> {
  /** 搜索 */
  search: string;

  order: QueryOrder<T>;

  /** 页面导航 */
  mode?: Menu['menuCode'];

  /** 文件分类 */
  type?: string;
}

export interface PageBreadcrumbsProps {
  title?: string;
  outHeader?: boolean;
  search: { text: string; count: number };
  list: FileInfo[];
}
