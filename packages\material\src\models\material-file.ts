import { MaterialType } from './material';
import { type FileInfo } from '@hetu/platform-app';
export interface MaterialFile extends FileInfo {
  /**
   * 文件大小
   */
  fileSize: number;

  /**
   * 文件类型
   */
  extension: string;

  /**
   * 缩略图
   */
  scaleId: string;

  /**
   * 大图id
   */
  attachmentId: string;

  /**
   * 类型
   */
  materialType: MaterialType;

  /**
   * 数据
   */
  materialData?: any;

  /**
   * 是否是默认
   */
  isDefault: boolean;

  /**
   * 上传进度
   */
  percent: number;

  /**
   * 是否被选中
   */
  active?: boolean;

  uid?: string;
}
