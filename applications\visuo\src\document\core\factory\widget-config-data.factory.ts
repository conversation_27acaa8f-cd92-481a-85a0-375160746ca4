import { UUID } from '@hetu/platform-shared';
import { AxisField, WidgetType } from '../models';

/**
 * 组件配置数据
 * <AUTHOR>
 */
export class WidgetConfigDataFactory {
  /**
   * 段落
   */
  get [WidgetType.Paragraph]() {
    return {
      datasetType: 'static',
      static: [
        {
          content: ''
        }
      ],
      matchs: {
        content: {
          name: '内容',
          field: [new AxisField('content', 'content', '', 'dimension', undefined, 'string')]
        }
      }
    };
  }

  /**
   * 标题
   */
  get [WidgetType.Title]() {
    return {
      datasetType: 'static',
      static: [
        {
          title: '标题'
        }
      ],
      matchs: {}
    };
  }

  /**
   * 选项卡
   */
  get [WidgetType.Tab]() {
    return {
      datasetType: 'static',
      static: [
        {
          id: UUID(),
          text: 'Tab A',
          value: 'a',
          disabled: false
        },
        {
          id: UUID(),
          text: 'Tab B',
          value: 'b',
          disabled: false
        },
        { id: UUID(), text: 'Tab C', value: 'c', disabled: false },
        {
          id: UUID(),
          text: 'Tab D',
          value: 'd',
          disabled: false
        }
      ],
      matchs: {
        text: {
          name: '标签',
          field: [new AxisField('text', 'text', '', 'dimension', undefined, 'string')]
        },
        value: {
          name: '值',
          field: [new AxisField('value', 'value', '', 'dimension', undefined, 'string')]
        },
        disabled: {
          name: '禁用',
          field: [new AxisField('disabled', 'disabled', '', 'dimension', undefined, 'string')]
        }
      }
    };
  }

  /**
   * 文本输入
   */
  get [WidgetType.Input]() {
    return {
      datasetType: 'static',
      static: [
        {
          title: '文本输入'
        }
      ],
      matchs: {}
    };
  }

  /**
   * 获取组件配置
   * @param widgetType 组件类型
   * @returns 组件配置
   */
  get(widgetType: WidgetType) {
    return this[widgetType];
  }
}
