<template>
  <div class="vis-config-body">
    <div class="vis-config-body-control">
      <q-tabs
        class="vis-config-body-tabs vis-tabs"
        v-if="isNode"
        v-model="tab"
        indicator-color="transparent"
        narrow-indicator
      >
        <q-tab name="style" label="设计" />
        <q-tab name="interactive" label="交互" />
      </q-tabs>

      <q-btn :label="zoomFormat" :ripple="false" flat class="!px-1">
        <q-icon right size="12px" name="expand_more" />
        <q-menu dense style="width: 120px" class="vis-menu">
          <q-input
            v-model="scale"
            @change="onChangeScale"
            class="vis-field--mini vis-config-body-menu__input p-3"
            dense
            outlined
            borderless
          >
            <template v-slot:append>
              <span class="vis-config-body-menu__percentage">%</span>
            </template>
          </q-input>
          <q-separator />
          <q-list dense>
            <q-item clickable @click="onScale(1)">
              <q-item-section>放大</q-item-section>
            </q-item>
            <q-item clickable @click="onScale(-1)">
              <q-item-section>缩小</q-item-section>
            </q-item>
            <q-item v-for="value in options" :key="value" clickable @click="setZoom(value / 100)">
              <q-item-section>缩放至{{ value }}%</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <vis-config-style v-if="tab === 'style'" />
    <vis-config-interactive v-if="tab === 'interactive'" />
    <vis-config-node v-if="!isNode" />
  </div>
</template>

<script lang="ts" src="./body.ts"></script>
<style lang="scss" src="./body.scss" scoped></style>
