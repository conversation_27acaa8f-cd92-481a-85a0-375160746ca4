import { computed, ref, defineComponent, type PropType } from 'vue';
import { useQuasar, useDialogPluginComponent } from 'quasar';
import type { FileInfo } from '../../models';
import { type FileTreeNode } from '../../utils';
import { CatalogFileService, CatalogService } from '../../services';

export default defineComponent({
  name: 'ht-app-move',
  emits: [...useDialogPluginComponent.emits],
  props: {
    title: String,
    // 当前上级
    parentId: {
      type: String,
      default: ''
    },
    // 类别
    category: {
      type: String,
      required: true
    },
    // 待移动
    sources: {
      type: Array<FileInfo>,
      required: true
    },
    // 组件内不请求移除，直接返回选择文件夹id，移除动作通过组件外部实现
    getSelectId: {
      type: Boolean,
      default: false
    },
    /** 树节点不展示文件 */
    filterFile: {
      type: Boolean,
      default: false
    },
    // 可选范围
    targets: {
      type: Array<FileInfo>,
      default: []
    },
    // 过滤移动目录规则
    filterMethod: {
      type: Function as PropType<(node: FileTreeNode<FileInfo>, filter: string) => boolean>
    }
  },
  setup(props) {
    const $q = useQuasar();
    const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

    const loading = ref(false);

    const targetId = ref(props.parentId);
    const isChange = computed(() => targetId.value !== props.parentId || !targetId.value);

    const group = computed(() => {
      const folders: string[] = [];
      const files: string[] = [];
      props.sources?.forEach((item) => {
        (item.dataType ? folders : files).push(item.id);
      });

      return { folders, files };
    });

    // 当待移动中包含文件夹时开启过滤
    const filter = ref(' ');
    const filterFn = (node: FileTreeNode<FileInfo> & { selectable: boolean }, filterText: string) => {
      // 排除待移动目录
      let result = !group.value.folders.includes(node.id);
      if (props.filterMethod) {
        const isCreate = props.filterMethod(node, filterText);
        if (!isCreate) {
          result = isCreate;
          if (!filter.value.trim()) {
            filter.value = node.id;
          }
        }
      }

      if (result && node?.getParentIds) {
        // 排除待移动目录下级
        result = !node.getParentIds().find((pid) => group.value.folders.includes(pid));
      }

      if (props.filterFile && node.dataType == 0) {
        result = node.dataType != 0;
      }
      return result;
    };

    /** 当前空间下移动 */
    const move = async () => {
      if (!isChange.value) {
        return onDialogCancel();
      }

      if (!props.getSelectId) {
        loading.value = true;

        const { folders, files } = group.value;
        const folderLen = folders.length;
        const fileLen = files.length;

        // 发送请求
        const all = [];
        folderLen && all.push(CatalogService.move(props.category as string, folders, targetId.value));
        fileLen && all.push(CatalogFileService.move(props.category as string, files, targetId.value));
        const result = await Promise.all(all);

        const isFolderSuccess = folderLen ? result[0]?.status === 'success' : true;
        const isFileSuccess = fileLen ? result[folderLen ? 1 : 0]?.status === 'success' : true;

        let successIds;
        if (isFolderSuccess && isFileSuccess) {
          successIds = group.value.folders.concat(group.value.files);
          $q.notify({ position: 'top', type: 'positive', message: '移动成功' });
        } else if (isFolderSuccess && folderLen) {
          successIds = group.value.folders;
          $q.notify({ position: 'top', type: 'warning', message: '文件夹移动成功, 文件移动失败!!!' });
        } else if (isFileSuccess && fileLen) {
          successIds = group.value.files;
          $q.notify({ position: 'top', type: 'warning', message: '文件移动成功, 文件夹移动失败!!!' });
        }

        loading.value = false;
        onDialogOK({
          targetId: targetId.value,
          successIds
        });
      } else {
        onDialogOK({
          targetId: targetId.value
        });
      }
    };

    /**过滤后可点击的节点 */
    const selected = (node: FileTreeNode<FileInfo>) => {
      targetId.value = targetId.value === node.id ? '' : node.id;
    };
    /**根据文件夹节点，判断当前节点文件夹是否有创建的权限， filterMethod返回false，则无创建能力*/
    const checkDisable = (node: FileTreeNode<FileInfo>) => {
      return props.filterMethod ? !props.filterMethod(node, '') : false;
    };

    return {
      dialogRef,
      loading,
      targetId,
      isChange,
      onDialogCancel,
      onDialogHide,

      filter,
      filterFn,

      move,
      selected,
      checkDisable
    };
  }
});
