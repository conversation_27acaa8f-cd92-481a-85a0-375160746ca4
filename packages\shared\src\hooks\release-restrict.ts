import { onBeforeRouteLeave } from 'vue-router';
import type { ReleaseResponseData, ReleaseRestrictParams } from '../models';
import { SettingsService } from '@hetu/theme';
import { ref, watch } from 'vue';
import { useQuasar } from 'quasar';
import { HtSpacePassword } from '../components';
import { SpaceService } from '../services';
import { useRouterWithout } from '@hetu/core';

export const useReleaseRestrict = <T>(
  dataKey: string,
  getData: (params: ReleaseRestrictParams) => Promise<ReleaseResponseData<T> | null>
) => {
  const route = useRouterWithout().currentRoute;
  const $q = useQuasar();

  const releaseData = ref<T>();

  const params = ref<ReleaseRestrictParams>({
    releaseCode: route.value.params.releaseCode as string,
    dataId: route.value.params.dataId as string,
    sourceDataId: route.value.params.sourceDataId as string,
    accessParams: window.location.hash.split('?')[1]
  });

  const load = async () => {
    const { isFetching } = SettingsService;
    isFetching.value = true;

    const response = await getData(params.value!);
    if (!response) {
      isFetching.value = false;
      return;
    }

    SpaceService.set(response.domain, response.tenantId);
    if (response[dataKey]) {
      releaseData.value = response[dataKey];
    } else if (response.validate.validatePassword) {
      // 验证密码
      $q.dialog({
        component: HtSpacePassword,
        componentProps: {
          title: response.dataName,
          params: params.value,
          validateCaptcha: response.validate.validateCaptcha
        }
      }).onOk(() => load());
    }

    isFetching.value = false;
  };

  load();

  const watchStopHandle = watch(
    () => route.value.params.releaseCode || route.value.params.dataId,
    () => {
      params.value = {
        releaseCode: route.value.params.releaseCode as string,
        dataId: route.value.params.dataId as string,
        sourceDataId: route.value.params.sourceDataId as string,
        accessParams: window.location.hash.split('?')[1]
      };
      load();
    }
  );

  onBeforeRouteLeave(() => {
    watchStopHandle();
    return true;
  });

  return { releaseData };
};
