import {
  computed,
  getCurrentInstance,
  onMounted,
  onUnmounted,
  ref,
  watch,
  nextTick,
  type ComponentPublicInstance,
  onBeforeMount,
  onUpdated
} from 'vue';
import type { WidgetBlock, Block } from '../models';
import { useDocumentStore } from '../stores';

/**
 * 基础组件
 */
export const useBase = () => {
  const docStore = useDocumentStore();

  /**
   * 组件实例
   */
  const instance = getCurrentInstance();

  /**
   * 获取组件实例代理
   */
  const childComponent = instance?.proxy as ComponentPublicInstance & {
    $props: { widget: WidgetBlock; block: Block };
    loadWidgetData: () => void;
    customLoadWidgetData?: () => void;
    updateWidgetData?: (data?: any) => void;
    updateWidgetDataLayer?: (data: any, layerId: string) => void;
  };

  const widget = childComponent.$props.widget || null;
  const block = childComponent.$props.block || null;
  const isDesign = ref<boolean>(false);

  // 响应式数据
  const dataTimeout = ref<number | null>(null);
  const allDataPromise = ref<Promise<any>[]>([]);
  const lastPromiseIndex = ref(0);
  const unWatchVars = ref<(() => void) | null>(null);

  /**
   * 初始化方法
   */
  const beforeInit = () => {
    if (!widget) return;

    if (!isDesign.value) {
      // 组件挂载前
    }
    console.log(widget, 'widget组件挂载前');
  };

  /**
   * 组件挂载后
   */
  const afterMounted = () => {
    if (!widget) return;

    // 等待 DOM 更新完成后再重新计算
    nextTick(() => {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          console.log(widget, 'widget组件挂载后');
        });
      });
    });
  };

  /**
   * 初始化组件数据和监听
   */
  const init = () => {
    if (!widget) return;

    if (isDesign.value) {
      customLoadWidgetData ? customLoadWidgetData() : loadWidgetData();
    } else {
      // 启用数据响应监听
      watchVars();

      // 变量池中默认值添加到请求参数中
      handleInteractFilters();

      if (widget.refreshType === 'control') {
        loadRequest();
      } else if (widget.refreshType === 'auto') {
        autoRefreshData();
      } else {
        loadRequest();
      }
    }

    // 设计器中启用监听
    if (isDesign.value && widget.options) {
      console.log('设计器中处理监听逻辑');
    }
  };

  /**
   * 发起数据请求
   */
  const loadRequest = async (callback?: () => Promise<any>) => {
    if (!widget) return;

    // 执行 --> 当发送数据接口请求前 事件
    const payload = createBeforeRequestEventPayload();
    beforeRequest(widget.id, payload);

    const handleAfterRequest = (data: any) => {
      // 执行 --> 当数据接口请求完成后 事件
      afterRequest(widget.id, createAfterRequestEventPayload(data));

      // 自动刷新数据
      autoRefreshData();

      // 模板页面组件数据加载完成后(动态数据)调用该方法设置组件状态
      widgetDataFinished();
    };

    try {
      let data;
      if (callback) {
        data = await callback();
      } else {
        data = await loadWidgetData();
      }
      handleAfterRequest(data);
    } catch (error) {
      console.error('数据请求失败:', error);
    }
  };

  /**
   * 动态数据自动刷新
   */
  const autoRefreshData = () => {
    if (!widget || isDesign.value) return;

    if (widget.refreshType === 'auto') {
      dataTimeout.value = window.setTimeout(() => {
        loadRequest();
      }, widget.refreshSecond * 1000);
    }
  };

  /**
   * 返回组件匹配字段的数据,并处理多个请求先发出，后返回的问题
   */
  const getWidgetFieldData = (dataPromise: Promise<any>): Promise<any> => {
    return new Promise((resolve) => {
      if (dataTimeout.value) {
        window.clearTimeout(dataTimeout.value);
        dataTimeout.value = null;
      }

      allDataPromise.value.push(dataPromise);

      dataPromise.then((data: any) => {
        const len = allDataPromise.value.length;
        const index = allDataPromise.value.findIndex((promise) => promise === dataPromise);

        if (!dataTimeout.value && len && index >= lastPromiseIndex.value) {
          // 最后一个promise时清空allDataPromise并还原lastPromiseIndex
          if (len === index + 1) {
            allDataPromise.value = [];
            lastPromiseIndex.value = 0;
          } else {
            lastPromiseIndex.value = index;
          }
          resolve(data);
        }
      });
    });
  };

  /**
   * 根据已改变的受监听变量处理数据响应参数
   */
  const handleInteractFilters = (): boolean => {
    // 这里需要根据实际的变量系统来实现
    // 暂时返回false
    return false;
  };

  /**
   * 监听变量数据变化
   */
  const watchVars = () => {
    // 这里需要根据实际的变量系统来实现监听逻辑
    console.log('watchVars 监听变量变化');
  };

  /**
   * 重写
   * @param name 方法名
   * @param defaultImpl 默认实现
   * @returns 重写后的方法
   */
  const override =
    (name: string, defaultImpl?: (...args: any[]) => any) =>
    (...args: any[]) => {
      const method = childComponent?.[name as keyof typeof childComponent];
      if (typeof method === 'function') {
        return method.apply(childComponent, args);
      } else if (defaultImpl) {
        return defaultImpl(...args);
      }
    };

  /**
   * 加载组件数据
   */
  const loadWidgetData = override('loadWidgetData', () => {
    console.log('useBase loadWidgetData, base组件的获取数据执行了', widget.id);
    // 可以添加默认的数据加载逻辑
    return Promise.resolve(null);
  });

  const customLoadWidgetData = override('customLoadWidgetData', () => {
    console.log('useBase customLoadWidgetData, base组件的获取数据执行了', widget.id);
    // 可以添加默认的自定义数据加载逻辑
    return Promise.resolve(null);
  });

  /**
   * 更新组件数据
   */
  const updateWidgetData = override('updateWidgetData', (data?: any) => {
    console.log('useBase updateWidgetData, base组件的更新数据执行了', data);
    // 可以添加默认的数据更新逻辑
    if (widget && data) {
      // 例如：更新widget的配置或选项
      (widget as any).data = data;
    }
  });

  /**
   * 更新组件数据层
   */
  const updateWidgetDataLayer = override('updateWidgetDataLayer', (data: any, layerId: string) => {
    console.log('useBase updateWidgetDataLayer, base组件的更新数据层执行了', data, layerId);
    // 可以添加默认的数据层更新逻辑
  });

  /**
   * 隐藏动作
   */
  const hide = (param: any) => {
    if (!widget) return;

    const el = document.getElementById('#' + block.id);
    if (!el) return;

    if (param.option.type !== 'none') {
      const animation = () => {
        console.log('隐藏动画执行结束: ', widget.name);
        block.visible = !!param.result;
        el.removeEventListener('animationend', animation);
      };
      el.addEventListener('animationend', animation);
    } else {
      block.opacity = 0;
      setTimeout(() => {
        block.visible = !!param.result;
        block.opacity = block.opacity || 1;
        console.log('隐藏动作延迟200毫秒执行结束: ', widget.name);
      }, 200);
    }
  };

  /**
   * 显示动作
   */
  const show = (param: any) => {
    if (!widget) return;

    const el = document.getElementById('#' + block.id);
    if (!el) return;

    block.visible = !!param.result;

    const animation = () => {
      console.log('显示动画执行结束: ', widget.name);
      el.removeEventListener('animationend', animation);
    };

    el.addEventListener('animationend', animation);
  };

  /**
   * 组件数据加载完成
   */
  const widgetDataFinished = (item?: any) => {
    const targetWidget = item || widget;
    if (!targetWidget) return;

    // 这里需要根据实际的模板和状态管理系统来实现
    console.log('组件数据加载完成:', targetWidget.id);
  };

  /**
   * 设置组件状态及数据
   */
  const setWidgetStore = (id: string, key: string, value: any) => {
    console.log('setWidgetStore:', id, key, value);
  };

  /**
   * 拼装请求参数
   */
  const createBeforeRequestEventPayload = () => {
    // 这里需要根据实际的参数系统来实现
    return {
      filter: {},
      query: {}
    };
  };

  /**
   * 数据请求后处理
   */
  const createAfterRequestEventPayload = (resultData?: any) => {
    if (!widget) return resultData;
    // 修改resultData
    console.log(resultData, '拼装参数');
    return resultData;
  };

  /**
   * 数据请求前
   */
  const beforeRequest = (nodeId: string, data: any) => {
    console.log('数据请求前beforeRequest:', nodeId, data);
  };

  /**
   * 数据请求后
   */
  const afterRequest = (nodeId: string, data: any) => {
    console.log('数据请求后afterRequest:', nodeId, data);
  };

  // 组件挂载前
  onBeforeMount(() => {
    // beforeInit();
  });

  // 组件挂载后
  onMounted(() => {
    // init();
    // afterMounted();
  });

  onUnmounted(() => {
    // 销毁组件时清除定时器
    if (dataTimeout.value) {
      window.clearTimeout(dataTimeout.value);
    }
    if (unWatchVars.value) {
      unWatchVars.value();
    }
  });

  return {
    // 数据
    dataTimeout,
    allDataPromise,
    lastPromiseIndex,

    // 方法
    loadWidgetData,
    customLoadWidgetData,
    updateWidgetData,
    updateWidgetDataLayer,
    loadRequest,
    autoRefreshData,
    getWidgetFieldData,
    handleInteractFilters,
    watchVars,
    hide,
    show,
    widgetDataFinished,
    setWidgetStore,
    createBeforeRequestEventPayload,
    createAfterRequestEventPayload,
    beforeRequest,
    afterRequest,
    init,
    override
  };
};
