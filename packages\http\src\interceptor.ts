import { type HttpRequestConfig } from './config';
import { type HttpResponse } from './response';

/** 拦截器 */
export interface HttpInterceptor<V> {
  /** The function to handle `then` for a `Promise` */
  intercept(value: V): V | Promise<V>;
}

/** 错误拦截 */
export interface HttpErrorInterceptor {
  /** The function to handle `reject` for a `Promise` */
  error(error: any): any;
}

/** 请求拦截器 */
export type HttpRequestInterceptor = HttpInterceptor<HttpRequestConfig>;
/** 请求拦截器: 包含错误拦截 */
export interface HttpRequestFullInterceptor extends HttpRequestInterceptor, HttpErrorInterceptor {}
export type HttpRequestInterceptorType =
  | HttpRequestInterceptor
  | (new () => HttpRequestInterceptor)
  | HttpErrorInterceptor
  | (new () => HttpErrorInterceptor)
  | HttpRequestFullInterceptor
  | (new () => HttpRequestFullInterceptor);

/** 响应拦截器 */
export type HttpResponseInterceptor = HttpInterceptor<HttpResponse>;
/** 响应拦截器: 包含错误拦截 */
export interface HttpResponseFullInterceptor extends HttpResponseInterceptor, HttpErrorInterceptor {}
export type HttpResponseInterceptorType =
  | HttpResponseInterceptor
  | (new () => HttpResponseInterceptor)
  | HttpErrorInterceptor
  | (new () => HttpErrorInterceptor)
  | HttpResponseFullInterceptor
  | (new () => HttpResponseFullInterceptor);

/**
 * HTTP 拦截器 KEY
 *
 * 用来执行 `eject` 操作: 删除已添加的拦截器
 */
export interface HttpInterceptorKeys {
  request: Map<HttpRequestInterceptorType, number>;
  response: Map<HttpResponseInterceptorType, number>;
}
