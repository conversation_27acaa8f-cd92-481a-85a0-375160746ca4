import { Dialog, Loading, Notify, Quasar } from 'quasar';
import quasarLang from 'quasar/lang/zh-CN';
import type { App } from 'vue';

export function setupQuasar(app: App) {
  app.use(Quasar, {
    plugins: {
      Notify,
      Dialog,
      Loading
    }, // import Quasar plugins and add here
    lang: quasarLang
    /*
    config: {
      brand: {
        // primary: '#e46262',
        // ... or all other brand colors
      },
      notify: {...}, // default set of options for Notify Quasar plugin
      loading: {...}, // default set of options for Loading Quasar plugin
      loadingBar: { ... }, // settings for LoadingBar Quasar plugin
      // ..and many more (check Installation card on each Quasar component/directive/plugin)
    }
    */
  });
}
