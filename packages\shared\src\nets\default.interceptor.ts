import { ACLService } from '@hetu/acl';
import { AppService, PageService, PassportService } from '../services';
import type { HttpRequestConfig, HttpRequestInterceptor, HttpResponse, HttpResponseFullInterceptor } from '@hetu/http';
import { Dialog, Notify, type QNotifyCreateOptions } from 'quasar';
import { useRouterWithout } from '@hetu/core';
import { TokenService } from '@hetu/auth';
import { ROUTE_DOMAIN_KEY } from '../models';

/**
 * HTTP 请求拦截器
 */
export class AppHttpRequestInterceptor implements HttpRequestInterceptor {
  intercept(config: HttpRequestConfig): HttpRequestConfig {
    // 防止请求缓存
    config.params = config.params ?? {};
    config.params['_'] = Date.now();

    return config;
  }
}

/**
 * HTTP 响应拦截器
 */
export class AppHttpResponseInterceptor implements HttpResponseFullInterceptor {
  intercept(response: HttpResponse<any>): HttpResponse<any> | Promise<HttpResponse<any>> {
    return response.status === 200 ? this.resolveCommon(response) : response;
  }

  error(error: any): Promise<never> {
    let response = error.response;
    if (!response) {
      throw new Error(`[Response] App Interceptor Error: ${error}`);
    }
    if (error.code === 'ECONNABORTED' && error.message.includes('timeout')) {
      response = { status: 504 };
    }
    this.resolveException(response, error.config);
    return Promise.reject(response);
  }

  private resolveCommon(response: HttpResponse<any>): HttpResponse<any> | Promise<HttpResponse<any>> {
    const data = response.data;
    if (data) {
      // 以下是统一输出格式（指不管成功与否都有相应的数据格式）情况下进行处理
      // 例如响应内容：
      // 错误内容：{ status: 'error', message: '非法参数', code: '' }
      // 正确内容：{ status: 'success', data: ( {} || [{}] ) }
      if (data.status === 'error') {
        const code = Number.parseInt(data.code, 10) || 0;
        if (
          this.resolveException(
            { status: code, statusText: data.message, data: data.data, from: 'body' },
            response.config
          )
        ) {
          return Promise.reject(response);
        }
      }
    }
    return response;
  }

  private resolveException(
    response: { status: number; statusText: string; from: string; data?: any },
    config: HttpRequestConfig
  ): boolean {
    let breakOff = true;
    // 1020: 不提示
    let tip = response.status !== 1020;
    const message = response?.data?.message || response.statusText || '';
    const bigMessage = message.includes('\n');
    const option: QNotifyCreateOptions = {
      type: 'negative',
      position: 'top',
      message,
      multiLine: false,
      actions: [{ icon: 'close', size: 'sm', color: 'white', round: true }]
    };
    const router = useRouterWithout();
    const createQuery = () => {
      const query: Record<string, string> = {
        redirect: encodeURIComponent(router.currentRoute.value.fullPath)
      };
      if (response.data?.domain) {
        query[ROUTE_DOMAIN_KEY] = response.data.domain;
      }
      if (response.data?.dataName) {
        query.dataName = response.data.dataName;
      }

      return query;
    };
    switch (response.status) {
      // 401: 未登录
      // 未登录则跳转登录页面, 并携带当前页面的路径
      // 清除 token
      // 在登录成功后返回当前页面, 这一步需要在登录页面操作
      case 401: {
        // 应保持登录&注册页面中不使用需要登录的接口, 为避免产生死循环在此单独处理一下
        if (
          AppService.appKey === import.meta.env.VITE_HETU_ACCOUNT_KEY ||
          TokenService.loginUrl === router.currentRoute.value.path
        ) {
          option.message = '当前页面中请求了需要登录的接口, 请联系管理员!';
        } else {
          PassportService.clearUserInfo();
          if (TokenService.loginUrl) {
            // 跳转路由
            router.replace({ path: TokenService.loginUrl, query: createQuery() });
          } else {
            PageService.toLogin(location.href);
          }
          tip = false;
        }
        break;
      }
      // 403: 无权限
      case 403: {
        if (router) {
          if (router.currentRoute.value.path !== ACLService.guardUrl) {
            router.replace({ path: ACLService.guardUrl, query: createQuery() });
          }
          tip = false;
        } else {
          option.message = '无权限';
        }
        break;
      }
      // 404: 请求不存在
      case 404: {
        option.message = '网络请求不存在';
        break;
      }
      case 400:
      case 500: {
        if (message.includes('JSON parse error')) {
          option.message = '请求参数格式错误';
        } else if (message === 'Internal Server Error' || bigMessage) {
          option.message = '服务器内部错误';
        }
        break;
      }
      case 504: {
        option.message = '网络请求超时';
        break;
      }
      // 其他错误, 直接抛出错误提示
      default:
        if (response.from === 'body') {
          breakOff = false;
        } else if (bigMessage) {
          option.message = '未知错误';
        } else {
          console.warn('未知错误', response);
        }
    }

    // 全局错误提示
    if (tip) {
      if (bigMessage) {
        option.actions = [
          {
            label: '查看详情',
            color: 'primary',
            handler: () => {
              Dialog.create({
                title: '错误详情',
                message: `<pre class="inline-block no-margin q-pa-xs rounded-borders">${message
                  .replace('<', '&lt;')
                  .replace('>', '&gt;')}</pre>`,
                html: true,
                class: 'ht-error-message-box column no-wrap',
                maximized: true,
                ok: false,
                cancel: '关闭'
              });
            }
          },
          ...option.actions!
        ];
      }

      Notify.create(option);
    }
    return breakOff;
  }
}
