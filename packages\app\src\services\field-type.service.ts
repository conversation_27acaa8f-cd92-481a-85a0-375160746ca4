import { HttpApiService, type ResponseResult } from '@hetu/http';
import { createSingleClass } from '@hetu/core';
import { FieldTypeApi, type DatasetFieldType } from '../models';
import { CacheService, CacheStoreType } from '@hetu/util';

export class FieldTypeServiceCtor<T extends FieldTypeApi = FieldTypeApi> extends HttpApiService<T> {
  httpApi = new FieldTypeApi() as T;

  httpModuleKey = 'dataset';

  /**
   * 获取字段类型配置项
   */
  getFieldTypes(): Promise<ResponseResult<DatasetFieldType[]> | null> {
    return CacheService.get(this.api.fieldTypes, { mode: 'promise', type: CacheStoreType.Memory });
  }
}
/**
 * 字段类型服务类
 */
export const FieldTypeService = createSingleClass(FieldTypeServiceCtor);
