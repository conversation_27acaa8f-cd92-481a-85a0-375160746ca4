$ht-actionbar-prefix-cls: '#{$ht-prefix}-actionbar';

.#{$ht-actionbar-prefix-cls} {
  @apply flex items-center justify-end;

  height: $ht-layout-header-height;

  &.is-mini {
    @apply h-10;
  }

  .q-btn,
  .q-avatar {
    @apply text-font-secondary duration-300 hover:bg-primary-lighter;
  }

  .q-btn {
    .q-badge {
      top: 0;
      right: -1px;
      height: 18px;
      transform: scale(0.9);
      font-size: 12px;
      line-height: 18px;
    }
  }

  .q-avatar {
    @apply bg-grey-light;
  }

  > * + * {
    @apply ml-4;
  }
}

.#{$ht-prefix}-apps-popup {
  @apply pa-3;

  .#{$ht-prefix}-app-btn {
    @apply flex flex-col items-center justify-center w-24 h-24 pa-2 rounded
    cursor-pointer text-font-regular duration-300 hover:bg-grey-light hover:text-primary;

    .q-img {
      @apply h-10 text-2xl;

      width: 60px;

      .q-img__content {
        @apply flex items-center justify-center;
      }
    }
  }
}

.#{$ht-prefix}-messages-popup {
  @apply w-87.5;

  .q-scrollarea,
  .#{$ht-prefix}-empty {
    @apply h-95.5;
  }

  .#{$ht-prefix}-message {
    @apply flex position-relative px-3 py-2 text-font-regular text-3.25 cursor-pointer 
      duration-300 hover:bg-grey-light;
    @include transition-all;

    line-height: 1.4;

    &.is-readed {
      @apply text-font-placeholder;

      .q-avatar {
        @apply opacity-65;
      }

      .#{$ht-prefix}-message__info {
        @apply text-font-placeholder;
      }
    }

    &__info {
      @apply text-xs text-font-secondary;
    }

    &__toolbar {
      @apply absolute top-1 right-2 opacity-0;
      @include transition-all;
    }

    &:hover .#{$ht-prefix}-message__toolbar {
      @apply opacity-100;
    }
  }
}

.#{$ht-prefix}-user-popup {
  .#{$ht-prefix}-user-cover {
    @apply w-56 h-28;

    background-image: $ht-background-color-gradual;

    .q-avatar {
      @apply text-font-secondary bg-grey-light;
    }
  }

  .q-item {
    @apply h-9 pa-0 cursor-pointer text-font-secondary text-center duration-300 
      hover:text-font-primary hover:bg-primary-lighter;
  }
}
