<template>
  <div class="ht-link" @click="deleteAllFavorite">
    <ht-icon :name="favoriteing ? '' : 'favorite'" size="16px">
      <q-spinner-ios v-if="favoriteing" />
    </ht-icon>
    <span class="q-pl-xs">取消星标</span>
  </div>
</template>

<script setup lang="ts">
import type { FileInfo } from '../../models';
import { useQuasar } from 'quasar';
import { inject, ref } from 'vue';
import { FILE_PAGE_STORE_KEY, useFilePageStore } from '../../stores';
import { FavoriteService } from '../../services';
import type { ResponseResult } from '@hetu/http';
import type { FileTreeNode } from '@hetu/platform-app';
import type { CooperationFile } from '@hetu/platform-cooperation';

defineOptions({ name: 'ht-app-delete-all-favorite' });

const props = defineProps<{
  /**分类 【分析、看板、驾驶舱...】 */
  category: string;
}>();

const $q = useQuasar();
/**loading状态 */
const favoriteing = ref(false);
const store = inject(FILE_PAGE_STORE_KEY) as ReturnType<typeof useFilePageStore<FileInfo>>;

/**获取所有文件文件夹 */
const filesFolders = () => {
  const files: Array<FileInfo> = [];
  const folders: Array<FileInfo> = [];
  // 获取所有文件文件夹
  const getAllFilesFolders = (tree: FileTreeNode<CooperationFile, FileInfo> | any) => {
    tree?.files.forEach((el: FileInfo) => {
      files.push(el);
    });
    tree?.directorys.forEach((el: FileInfo) => {
      folders.push(el);
      getAllFilesFolders(el);
    });
  };
  getAllFilesFolders(store.tree?.getNode());
  return { files, folders };
};

/**重置星标数据状态 */
const resetFavorite = (files: Array<FileInfo>, folders: Array<FileInfo>) => {
  files.forEach((d: FileInfo) => {
    d.favorite = 0;
  });
  folders.forEach((d: FileInfo) => {
    d.favorite = 0;
  });
  store.setOpen();
};

/** 删除所有星标 */
const deleteAllFavorite = () => {
  $q.dialog({
    title: '提示',
    message: `确认取消所有星标?`,
    ok: '确定',
    cancel: '取消'
  }).onOk(async () => {
    favoriteing.value = true;

    try {
      const deleteAllReq: Promise<ResponseResult<any> | null>[] = [];
      const { files, folders } = filesFolders();
      const fileIds = files.filter((d: FileInfo) => d.favorite).map((d: FileInfo) => d.id);
      const folderIds = folders.filter((d: FileInfo) => d.favorite).map((d: FileInfo) => d.id);
      if (fileIds && fileIds.length) {
        deleteAllReq.push(FavoriteService.catalogDelete(fileIds, props.category));
      }
      if (folderIds && folderIds.length) {
        deleteAllReq.push(FavoriteService.fileDelete(folderIds, props.category));
      }
      if (deleteAllReq.length) {
        const res = await Promise.all(deleteAllReq);
        const success = res && res.every((e) => e && e.status === 'success');
        if (success) {
          $q.notify({
            position: 'top',
            type: 'positive',
            message: '取消成功'
          });
          resetFavorite(files, folders);
        }
      }
      // eslint-disable-next-line no-empty
    } catch (e) {}

    favoriteing.value = false;
  });
};
</script>
