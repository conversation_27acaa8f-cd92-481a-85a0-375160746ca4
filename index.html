<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="expires" content="0" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <base href="<%= baseURI %>" />
    <link rel="icon" href="./favicon.ico" />
    <title><%= VITE_APP_TITLE %></title>
    <style>
      .ht-preloader {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: <%= dark ? '#0d0d0d' : 'linear-gradient(-135deg, #ff6b45, #308dec)' %>;
        -webkit-transition: all 0.65s;
        transition: all 0.65s;
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1;
      }

      .ht-preloader-hidden-add {
        -webkit-transform: scale(2);
        transform: scale(2);
        opacity: 0;
      }

      .ht-preloader-hidden {
        display: none !important;
      }

      .ht-preloader .ht-csloader {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100px;
        height: 100px;
        margin: -50px 0 0 -50px;
        background: url(./static/img/logo-loading.gif) no-repeat center;
        background-size: 100%;
      }

      .ht-preloader.<%= dark ? 'light' : 'dark' %> {
        background: <%= dark ? 'linear-gradient(-135deg, #ff6b45, #308dec)' : '#0d0d0d' %>;
      }

      .ht-preloader.hide {
        -webkit-transition: -webkit-transform 0.65s;
        transition: transform 0.65s;
        opacity: 0;
      }
    </style>
    <script>
      console.log('<%= VITE_APP_BUILD_INFO %>');
    </script>
  </head>
  <body>
    <noscript>
      <strong style="z-index: 10000; position: fixed">
        <span>We're sorry but <%=VITE_APP_TITLE%> doesn't work properly without JavaScript enabled.</span>
        <span>Please enable it to continue.</span>
      </strong>
    </noscript>
    <div id="q-app"></div>
    <div class="ht-preloader">
      <div class="ht-csloader"></div>
    </div>
    <script>
      (function () {
        const parsedUrl = new URL(window.location.href);
        const theme = parsedUrl.searchParams.get('preloader');
        theme && document.querySelector('.ht-preloader').classList.add(theme);
      })();
    </script>
  </body>
</html>
