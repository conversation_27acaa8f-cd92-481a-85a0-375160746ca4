<template>
  <q-dialog ref="dialogRef" @hide="onDialogHide" v-close-popup>
    <q-card class="q-dialog-plugin">
      <q-card-section class="q-dialog__title ellipsis"
        >移动{{ title ? ` - ${title}` : `(${sources.length})` }}</q-card-section
      >
      <q-scroll-area style="height: 200px">
        <q-card-section class="q-py-none">
          <q-tree
            :nodes="targets"
            node-key="id"
            label-key="title"
            children-key="directorys"
            :filter="filter"
            :filter-method="filterFn"
            default-expand-all
          >
            <template v-slot:default-header="{ node }">
              <div v-if="checkDisable(node)" class="col row items-center q-tree__node--disabled">
                {{ node.title }}
              </div>
              <div
                v-else
                class="col row items-center cursor-pointer"
                :class="node.id === targetId && 'text-primary'"
                @click="selected(node)"
              >
                {{ node.title }}
              </div>
            </template></q-tree
          >
        </q-card-section>
      </q-scroll-area>
      <q-card-actions align="right">
        <q-btn label="取消" flat @click="onDialogCancel" />
        <q-btn label="确定" color="primary" :loading="loading" :disable="!isChange" flat @click="move" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" src="./move"></script>
