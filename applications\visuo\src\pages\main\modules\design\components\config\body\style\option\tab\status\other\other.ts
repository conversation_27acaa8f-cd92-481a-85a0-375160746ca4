import { EffectsType, StrokeType, type TabItemStyle } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';

/**
 * 其他状态组件 填充、描边、特效
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-status-other',
  props: {
    /**
     * 其他配置
     */
    statusOption: {
      type: Object as PropType<TabItemStyle>,
      required: true
    }
  },
  setup(props) {
    const showLine = ref(false);

    const lineOptions = [
      { label: '实线', value: StrokeType.Solid, style: 'solid' },
      { label: '虚线', value: StrokeType.Dashed, style: 'dashed' },
      { label: '点线', value: StrokeType.Dotted, style: 'dotted' }
    ];

    const effectOptions = [
      { label: '外阴影', value: EffectsType.Outset, icon: 'shadow-out' },
      { label: '内阴影', value: EffectsType.Inset, icon: 'shadow-in' },
      { label: '高斯模糊', value: EffectsType.Blur, icon: 'blur' },
      { label: '背景模糊', value: EffectsType.BgBlur, icon: 'blur-bg' }
    ];
    const computedStyle = computed(() => props.statusOption);

    /**
     * 修改线宽
     * @param value
     */
    const handlePositionChange = (value: number) => {
      computedStyle.value.border.position = [value, value, value, value];
    };
    return {
      showLine,
      computedStyle,
      lineOptions,
      effectOptions,
      handlePositionChange
    };
  }
});
