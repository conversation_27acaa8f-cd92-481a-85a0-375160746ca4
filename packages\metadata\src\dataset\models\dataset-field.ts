import { BaseModel } from '@hetu/http';
import { isNumber } from 'lodash-es';

/**
 * 数据资源字段
 * <AUTHOR>
 */
export class DatasetField extends BaseModel {
  /**
   * 数据资源唯一标识
   */
  datasetId?: string;
  /**
   * 字段名
   */
  fieldName?: string;
  /**
   * 字段名
   */
  fieldOriginal?: string;
  /**
   * 字段中文名
   */
  fieldAlias?: string;
  /**
   * 数据类型
   */
  fieldDatatype = 'string';
  /**
   * 维度/量度
   */
  fieldType?: string;
  /**
   * 格式化
   */
  fieldFormat?: string;
  /**
   * 所属表名
   */
  tableName?: string;
  /**
   * 描述
   */
  remark?: string;
  /**
   * 是否隐藏(0：隐藏；1：显示)
   */
  show = 0;
  /**
   * 排序值
   */
  orderNo = 0;

  /**
   * 是否是创建计算字段
   * 0表示不是，1表示是
   */
  calculatedField?: number;

  constructor(
    fieldName?: string,
    fieldAlias?: string,
    fieldDatatype?: string,
    fieldType?: string,
    calculatedField?: number
  ) {
    super();
    if (fieldName) {
      this.fieldName = fieldName;
    }
    if (fieldAlias) {
      this.fieldAlias = fieldAlias;
    }
    if (fieldDatatype) {
      this.fieldDatatype = fieldDatatype;
    }
    if (fieldType) {
      this.fieldType = fieldType;
    }

    isNumber(calculatedField) && (this.calculatedField = calculatedField);
  }
}

/**
 * 数据资源字段对象数组
 * @type
 */
export type DatasetFields = DatasetField[];
