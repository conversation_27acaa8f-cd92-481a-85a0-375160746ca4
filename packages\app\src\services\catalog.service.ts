import { SYSTEM_API_PREFIX } from '@hetu/platform-shared';
import {
  HttpApiService,
  type HttpApi,
  type ResponseResult,
  responseResult,
  responseReject,
  responseData
} from '@hetu/http';
import { Catalog, type FileInfo } from '../models';
import { createSingleClass } from '@hetu/core';

const API_HOST = `${SYSTEM_API_PREFIX}/catalog`;

class CatalogApi implements HttpApi {
  save = `${API_HOST}/save`;
  delete = `${API_HOST}/delete`;
  recycle = `${API_HOST}/recycle/delete`;
  update = `${API_HOST}/update`;
  detail = `${API_HOST}/detail`;
  move = `${API_HOST}/move`;
  moveList = `${API_HOST}/move/list`;
  categoryList = `${API_HOST}/list`;
}

/**
 * 目录服务类
 * <AUTHOR>
 */
export class CatalogServiceCtor extends HttpApiService<CatalogApi> {
  httpApi = new CatalogApi();

  httpModuleKey = 'catalog';

  /**
   * 保存
   * @param catalog 目录信息
   */
  save(catalog: Catalog): Promise<ResponseResult | null> {
    return this.http.post(this.api.save, catalog).then(responseResult, responseReject);
  }

  /**
   * 删除
   * @param category 类别
   * @param ids 目录 ID
   */
  delete(category: string, ids: string[]): Promise<ResponseResult | null> {
    return this.http.delete(`${this.api.delete}/${category}`, { data: { ids } }).then(responseResult, responseReject);
  }

  /**
   * 删除到回收站
   * @param category 类别
   * @param ids 目录 ID
   */
  recycle(category: string, ids: string[]): Promise<ResponseResult | null> {
    return this.http.delete(`${this.api.recycle}/${category}`, { data: { ids } }).then(responseResult, responseReject);
  }

  /**
   * 更新目录名称
   * @param id 目录 ID
   * @param catalogName 目录名称
   */
  update(id: string, catalogName: string) {
    return this.http.post(this.api.update, { id, catalogName }).then(responseResult, responseReject);
  }

  /**
   * 查询目录详情
   * @param category 类别
   * @param id 目录 ID
   */
  detail<T extends FileInfo = FileInfo>(category: string, id: string): Promise<T> {
    return this.http.get(`${this.api.detail}/${category}`, { params: { id } }).then(responseData, responseReject);
  }

  /**
   * 移目录
   * @param category 类别
   * @param id 目录 ID
   * @param targetId 目标目录 ID
   */
  move(category: string, ids: string[], targetId: string): Promise<ResponseResult | null> {
    return this.http.post(`${this.api.move}/${category}`, { ids, targetId }).then(responseResult, responseReject);
  }

  /**
   * 查询当前用户在指定空间有权限的文件夹列表
   * - 可用于跨空间协作文件(夹)移动
   * @param domain 空间标识
   * @param category 类别
   */
  moveList(domain: string, category: string): Promise<FileInfo[]> {
    return this.http.get(`${this.api.moveList}/${category}`, { params: { domain } }).then(responseData, responseReject);
  }

  /**
   * 查询当前用户有权限的文件夹列表(文件夹信息及权限信息)
   * - 可用于当前空间内协作文件(夹)移动
   * @param category 类别
   */
  categoryList(category: string): Promise<FileInfo[]> {
    return this.http.get(`${this.api.categoryList}/${category}`).then(responseData, responseReject);
  }
}

export const CatalogService = createSingleClass(CatalogServiceCtor);
