import { Dialog } from 'quasar';
import { type CooperationFile, CooperationACLResource, SubFileACLResource } from '../../models';
import HtCoPanel from './panel.vue';

/**
 * 协作面板
 * @param file   文件信息
 * @param category          分类
 * @param acl               权限
 * @param subAcl
 * @param isParentPrivilege 显示上级文件夹协作者：parentId && tree.getFile(parentId) === true
 */
const HtCoPanelDialog = (
  file: CooperationFile,
  category: string,
  acl: CooperationACLResource,
  subAcl?: SubFileACLResource,
  isParentPrivilege: boolean = true
) =>
  Dialog.create({
    component: HtCoPanel,
    componentProps: {
      file,
      category,
      acl,
      subAcl,
      isParentPrivilege
    }
  });

export { HtCoPanelDialog, HtCoPanel };
