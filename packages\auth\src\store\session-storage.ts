import type { TokenStore, TokenModel } from '.';

/**
 * `sessionStorage` storage
 */
export class SessionStorageStore implements TokenStore {
  get(key: string) {
    return JSON.parse(sessionStorage.getItem(key) || '{}') || {};
  }

  set(key: string, value: TokenModel): boolean {
    sessionStorage.setItem(key, JSON.stringify(value));
    return true;
  }

  remove(key: string) {
    sessionStorage.removeItem(key);
  }
}
