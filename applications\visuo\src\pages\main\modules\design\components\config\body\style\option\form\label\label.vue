<template>
  <div class="vis-form-label relative">
    <!-- 颜色 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <vis-fill v-model="computedOptions.font.color"></vis-fill>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">内容</div>
          <div class="vis-form-field__content">
            <q-input
              v-model="computedOptions.text"
              placeholder="请输入标签内容"
              flat
              borderless
              class="vis-input px-2 border-radius rounded-borders w-full"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 位置+对齐 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label">显示位置</div>
          <div class="vis-form-field__content">
            <vis-select
              v-model="computedOptions.position"
              @update:modelValue="handlePosition"
              :options="positionOptions"
            ></vis-select>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__label">对齐方式</div>
          <div class="vis-form-field__content">
            <vis-button-group v-model="computedOptions.align" :options="alignOptions" />
          </div>
        </div>
      </div>
    </div>

    <q-btn flat dense @click="showPopup" class="btn-field absolute bottom-0 right-0" :class="{ active: popupShow }">
      <ht-icon class="vis-icon" name="vis-control" />
    </q-btn>

    <!-- 弹窗 -->
    <vis-popup title="标签设置" ref="popupRef" :target="false" @hide="popupShow = false">
      <div class="vis-form-inline">
        <!-- 宽度 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">宽度</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOptions.width" icon="vis-resizing-w" :min="0" :disabled="adaptSingle" />
          </div>
        </div>

        <!-- 字体 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">字体</div>
          <div class="vis-form-field__content">
            <vis-select
              v-model="computedOptions.font.fontFamily"
              class="w-full"
              :options="fontFamilyOptions"
              :popHeight="240"
            >
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: computedOptions.font.fontWeight, fontFamily: opt }">{{ opt }}</span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>

        <!-- 粗细 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">粗细</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedOptions.font.fontWeight" :options="fontWeightOptions" class="w-full">
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: opt.value, fontFamily: computedOptions.font.fontFamily }">
                      {{ opt.label }}
                    </span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>

        <!-- 字号 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">字号</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedOptions.font.fontSize" :options="fontSizeOptions" editable />
          </div>
        </div>

        <!-- 字间距 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">字间距</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOptions.font.letterSpacing" icon="vis-resizing-w" :min="0" />
          </div>
        </div>

        <!-- 行高 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">行高</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOptions.font.lineHeight" icon="vis-resizing-h" :min="0" />
          </div>
        </div>

        <!-- 展示模式 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">展示模式</div>
          <div class="vis-form-field__content">
            <vis-button-group v-model="computedOptions.font.adapt" :options="adaptOptions"></vis-button-group>
          </div>
        </div>

        <!-- 内边距 -->
        <div class="vis-form-field">
          <div class="vis-form-field__label">内边距</div>
          <div class="vis-form-field__content">
            <vis-mix-input
              v-model="padding"
              @update:model-value="updatePadding"
              icon="hticon-vis-padding-round"
              :min="0"
              class="pr-0"
              :input-class="{ hidden: isSingle }"
              :readonly="isSingle"
            >
              <template #default>
                <span class="text-font-regular">{{ isSingle ? '单独设置' : '' }}</span>
              </template>
              <template #append>
                <q-btn>
                  <q-icon name="keyboard_arrow_down" class="!text-xs" />
                  <q-menu v-model="showMenuPadding" style="width: 110px" class="vis-menu" dense>
                    <q-list dense>
                      <q-item :active="!isSingle" @click="handlePadding(false)" clickable>
                        <q-item-section>统一设置</q-item-section>
                      </q-item>
                      <q-item :active="isSingle" @click="handlePadding(true)" clickable>
                        <q-item-section>单独设置</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </template>
            </vis-mix-input>
          </div>
        </div>
        <template v-if="isSingle">
          <div class="vis-form-field">
            <div class="vis-form-field__label"></div>
            <div class="vis-form-field__content">
              <vis-number v-model="computedOptions.padding[0]" icon="hticon-vis-padding-top" :min="0" />
              <vis-number v-model="computedOptions.padding[1]" icon="hticon-vis-padding-right" :min="0" />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label"></div>
            <div class="vis-form-field__content">
              <vis-number v-model="computedOptions.padding[3]" icon="hticon-vis-padding-left" :min="0" />
              <vis-number v-model="computedOptions.padding[2]" icon="hticon-vis-padding-bottom" :min="0" />
            </div>
          </div>
        </template>
      </div>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./label.ts"></script>
