$ht-layout-prefix-cls: '#{$ht-prefix}-app-layout';

.#{$ht-layout-prefix-cls} {
  min-width: 992px;
  min-height: 400px;

  > .q-drawer-container .q-drawer {
    &__content {
      @apply py-10;
    }

    &--left {
      .q-btn {
        min-width: 40px;
        min-height: 40px;
      }

      .q-layout__shadow::after {
        box-shadow: 2px 0 8px 0 rgb(29 35 41 / 5%);
      }
    }

    &--right .q-drawer__content {
      @apply pr-8;
    }
  }

  &__logo {
    height: 40px;
    cursor: pointer;
  }

  &__navbar {
    .q-item {
      width: 64px;
      height: 64px;
      border-radius: 4px;
      font-size: 12px;

      & + .q-item {
        @apply mt-4;
      }

      &:hover,
      &.q-item--active {
        @apply bg-primary-lighter;
      }

      &:not(.q-item--active) {
        @apply text-font-primary;
      }
    }
  }

  &__file-info {
    margin-right: -8px;

    .q-scrollarea__content {
      padding-right: 8px;
    }
  }

  &__main {
    // padding: 0;
    // overflow-x: hidden;
  }
}
