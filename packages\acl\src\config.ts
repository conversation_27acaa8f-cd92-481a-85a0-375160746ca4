import type { ACLCanType, ACLType } from './acl';

export interface ACLConfig {
  /** 无权限路由页面地址, 默认: `/403`, 通过 `ACLService.guardUrl` 获取 */
  guardUrl: string;

  /** 预验证: 在权限验证 `ACLService.can` 执行前回调 */
  preCan: (roleOrResource?: ACLCanType | null) => ACLType | null;
}

const aclConfig = {
  guardUrl: '/403'
} as ACLConfig;

export const setupACLConfig = (config: Partial<ACLConfig>) => Object.assign(aclConfig, config);

export const useACLConfig = () => aclConfig;
