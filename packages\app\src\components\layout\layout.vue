<template>
  <q-layout class="ht-app-layout" view="lhr lpr lfr">
    <q-drawer
      class="ht-app-layout-sider column"
      show-if-above
      side="left"
      mini
      :mini-width="80"
      :breakpoint="0"
      elevated
    >
      <div class="ht-app-layout-sider__header column items-center q-mb-md q-gutter-y-md" inset>
        <img class="ht-app-layout__logo" :src="'./static/img/logo.png'" alt="logo" @click="onBackMain" />
        <slot name="create-btn" />
      </div>

      <q-scroll-area class="ht-app-layout-sider__content col">
        <q-list class="ht-app-layout__navbar column q-px-sm">
          <template v-for="(nav, index) in navs" :key="index">
            <q-item
              v-if="!nav.isSeparator"
              class="no-helper"
              :active="active === nav.id"
              clickable
              @click="select(nav)"
            >
              <q-item-section class="items-center" avatar>
                <ht-icon v-if="nav.menuIcon?.includes('hticon')" class="q-icon" :name="nav.menuIcon" />
                <q-icon v-else :name="nav.menuIcon" />
                <span class="q-mt-sm col">{{ nav.menuName }}</span>
              </q-item-section>
            </q-item>
            <q-separator v-else class="q-my-sm !bg-grey-extra-light" />
          </template>
        </q-list>
      </q-scroll-area>

      <div class="ht-app-layout-sider__footer column items-center q-mt-md">
        <ht-help />
      </div>
    </q-drawer>

    <q-page-container>
      <slot>
        <router-view v-slot="{ Component }">
          <transition name="ht-transition--fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </slot>
    </q-page-container>

    <q-drawer
      class="ht-app-layout-sider column no-wrap"
      show-if-above
      side="right"
      :width="256"
      :mini="mini"
      :mini-width="120"
      :breakpoint="0"
    >
      <div class="ht-app-layout-sider__header q-mb-md q-gutter-y-md">
        <ht-actionbar mini />
        <ht-resourcebar :mini="mini" />
      </div>

      <q-scroll-area class="ht-app-layout-sider__content ht-app-layout__file-info col">
        <div class="gt-sm">
          <slot name="file-info">
            <router-view name="fileInfo" v-slot="{ Component }">
              <component :is="Component" />
            </router-view>
          </slot>
        </div>
      </q-scroll-area>

      <div class="ht-app-layout-sider__footer q-mt-md">
        <ht-space :mini="mini" />
      </div>
    </q-drawer>
  </q-layout>
</template>

<style lang="scss" src="./layout.scss"></style>
<script lang="ts" src="./layout.ts"></script>
