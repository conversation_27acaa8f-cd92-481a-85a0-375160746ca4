import { defineComponent, ref, type PropType, computed } from 'vue';
import type { ReleaseRestrictParams, ValidatorCode } from '../../models';
import { createQFormRules } from '@hetu/util';
import { PassportService } from '../../services';
import { QInput, useDialogPluginComponent } from 'quasar';

export default defineComponent({
  name: 'ht-space-password',
  props: {
    title: String,
    params: {
      type: Object as PropType<ReleaseRestrictParams>,
      required: true
    },
    validateCaptcha: {
      type: Number as PropType<0 | 1>,
      default: 0
    }
  },
  setup(props) {
    const { dialogRef, onDialogOK } = useDialogPluginComponent();
    const params = computed(() => props.params);
    // 给初始值用于激活表单验证
    params.value.accessPassword = params.value.accessPassword ?? '';

    const code = ref<ValidatorCode>({});
    const codeInputRef = ref<QInput>();
    const fetchCodeImg = async () => {
      const data = await PassportService.validatorCode();
      code.value = data ?? {};
    };
    if (props.validateCaptcha) {
      fetchCodeImg();
      params.value.captchaCode = params.value.captchaCode ?? '';
    }

    const formRef = ref();
    const codeValidator = async () => {
      const result = await PassportService.loginCodeCheck(params.value.captchaCode!, code.value.codeId!);
      if (result && result.status === 'error') {
        fetchCodeImg();
        return '验证码错误，请重新输入!';
      }
      return true;
    };
    const rules = createQFormRules<ReleaseRestrictParams>({
      accessPassword: [{ required: true, message: '请输入密码' }],
      captchaCode: [{ required: true, message: '请输入验证码' }, { validator: codeValidator }]
    });

    const submit = () => {
      formRef.value.validate().then((success: boolean) => {
        if (success) {
          params.value.captchaCodeId = code.value.codeId;
          onDialogOK(params.value);
        }
      });
    };

    return {
      dialogRef,

      code,
      codeInputRef,
      fetchCodeImg,

      formRef,
      rules,

      submit
    };
  }
});
