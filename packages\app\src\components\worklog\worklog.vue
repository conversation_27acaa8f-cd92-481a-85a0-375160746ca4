<template>
  <ht-app-page
    :menus="allMenu"
    :active-menu="actMenu.menuCode"
    :loading="loading"
    :isEmptyBody="empty"
    @selectMenu="selectMenu"
    class="ht-worklog"
  >
    <template #header>
      <div class="col text-h5 text-font-primary">{{ actMenu.menuName }}</div>
      <div class="col row flex-center gt-sm">
        <q-input
          v-model="search"
          class="ht-field ht-field--medium ht-field--light"
          placeholder="搜索"
          dense
          rounded
          standout
          @keyup.enter="handleSearch"
          @blur="handleSearch"
        >
          <template v-slot:prepend>
            <q-icon name="search" size="20px" />
          </template>
        </q-input>
      </div>
      <div class="col row justify-end">
        <ht-app-sort :order="order" :columns="sortColumns" @sort="handleSort" />
      </div>
    </template>
    <q-timeline>
      <q-timeline-entry v-for="(dates, idx) in operates" :key="idx" :subtitle="idx">
        <div v-for="op in dates" :key="op.id" class="row q-mb-sm q-pa-sm hover:bg-grey-light">
          <div class="col-6 row items-center">
            <slot name="operateIcon" :operate="op"></slot>
            <span>{{ op.objectName }}</span>
          </div>

          <div class="col-3"><slot name="operateName" :operate="op"></slot></div>

          <div class="col-3 text-right">{{ op.time }} {{ op.creatorName }} {{ op.operateDesc }}</div>
        </div>
      </q-timeline-entry>
    </q-timeline>
  </ht-app-page>
</template>

<script lang="ts" src="./worklog.ts"></script>
