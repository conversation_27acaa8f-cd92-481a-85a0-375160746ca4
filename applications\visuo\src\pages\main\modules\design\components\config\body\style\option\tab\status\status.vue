<template>
  <div class="vis-config-tab-status">
    <div class="vis-form-inline">
      <div :class="`vis-form-inline__content--minus-60`">
        <q-btn :class="{ active: popupShow }" @click.stop="showPopup">
          <q-icon name="settings" size="12px"></q-icon>
        </q-btn>
        <vis-popup :title="title" ref="popupRef" :target="false" @hide="popupShow = false">
          <q-tabs class="vis-tabs" v-model="tab" indicator-color="transparent" narrow-indicator>
            <q-tab name="text" label="文本" />
            <q-tab name="icon" label="图标" />
            <q-tab name="other" label="其他" />
          </q-tabs>

          <div class="q-py-sm">
            <component :is="`vis-config-status-${tab}`" :statusOption="getData(tab)"></component>
          </div>
        </vis-popup>
        <vis-fill v-model="computedStyle.background" :showEyes="false" :minusWidth="0" />
      </div>
      <!-- 显隐按钮 -->
      <q-btn class="btn-field" @click="computedStyle.visible = !computedStyle.visible">
        <ht-icon class="vis-icon" :name="`hticon-vis-eye-${computedStyle.visible ? 'o' : 'c'}`" />
      </q-btn>
    </div>
  </div>
</template>
<script lang="ts" src="./status.ts"></script>
