import { createSingleClass } from '@hetu/core';
import { HttpApiService, responseData, responseReject, type HttpApi } from '@hetu/http';
import { API_PREFIX } from '@hetu/platform-shared';

const API_HOST = `${API_PREFIX}`;

class ReleaseApi implements HttpApi {
  url = `${API_HOST}/release/url`;
  token = `${API_HOST}/release/token`;
}

/**
 * 发布服务类
 * <AUTHOR>
 */
class ReleaseServiceCtor extends HttpApiService<ReleaseApi> {
  httpApi = new ReleaseApi();

  httpModuleKey = 'release';

  /**
   * 获取发布地址信息
   */
  getUrl(): Promise<{ releaseUrl: string; releaseCode: string } | null> {
    return this.http.get(this.api.url).then(responseData, responseReject);
  }

  /**
   * 获取 Token
   */
  getToken(): Promise<{ accessToken: string } | null> {
    return this.http.get(this.api.token).then(responseData, responseReject);
  }
}

export const ReleaseService = createSingleClass(ReleaseServiceCtor);
