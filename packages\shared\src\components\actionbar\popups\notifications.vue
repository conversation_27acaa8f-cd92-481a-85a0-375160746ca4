<template>
  <q-btn class="no-helper" icon="o_notifications" size="12px" flat round>
    <q-badge v-if="unreadCount" color="negative" floating rounded>{{ unreadCount }}</q-badge>
    <q-popup-proxy
      class="ht-messages-popup"
      transition-show="flip-right"
      transition-hide="flip-left"
      breakpoint="0"
      @before-show="beforeShow"
      @before-hide="beforeHide"
    >
      <div class="flex justify-between">
        <q-tabs v-model="active" active-color="primary" @update:model-value="loadLatestMessages">
          <q-tab label="未读" name="unread" />
          <q-tab label="全部" name="all" />
        </q-tabs>
        <div class="row q-px-md">
          <label :class="['ht-link', !unreadCount && 'is-disabled']" @click="readAll">
            <q-icon name="check_circle_outline" />
            <span class="q-ml-xs">全部已读</span>
          </label>
          <label
            v-if="active === 'all'"
            :class="['ht-link is-negative', !messages.length && 'is-disabled']"
            @click="removeAll"
          >
            <q-icon name="delete_outline" />
            <span class="q-ml-xs">清空</span>
          </label>
        </div>
      </div>
      <q-separator />
      <q-scroll-area ref="scrollArea">
        <q-infinite-scroll @load="loadMessages" :offset="100" ref="infiniteScroll" scroll-target="$refs.scrollArea">
          <template v-if="messages.length">
            <div
              v-for="message in messages"
              :key="message.id"
              :class="['ht-message', message.status && 'is-readed']"
              @click="read(message)"
            >
              <q-avatar class="bg-font-placeholder" size="36px" text-color="white">
                <img v-if="avatar(message.avatar)" :src="avatar(message.avatar)" />
                <span v-else>{{ message.senderName.toUpperCase().charAt(0) }}</span>
                <q-tooltip anchor="center left" self="center right">
                  {{ message.senderName }}
                </q-tooltip>
              </q-avatar>
              <div class="col q-ml-sm">
                <div class="ellipsis">{{ message.title }}</div>
                <div class="q-mt-xs">{{ message.content }}</div>
                <div class="ht-message__info q-mt-xs">{{ message.sendTime }}</div>
              </div>
              <div class="ht-message__toolbar">
                <q-icon name="close" color="negative" @click.stop="remove(message)">
                  <q-tooltip>点击删除</q-tooltip>
                </q-icon>
              </div>
            </div>
          </template>
          <ht-empty v-else-if="!loading" description="~ 空空如也 ~" />
          <template v-slot:loading>
            <div class="row justify-center q-my-md">
              <q-spinner-dots color="primary" size="40px" />
            </div>
          </template>
        </q-infinite-scroll>
      </q-scroll-area>
    </q-popup-proxy>
  </q-btn>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { Notify, QInfiniteScroll } from 'quasar';
import { AttachmentService, MessageManageService, MessageObserveService, MessageSubscription } from '../../../services';
import type { Message } from '../../../models';

// 订阅通知
const subscription = ref<MessageSubscription>();
const unreadCount = ref<number>(0);
onMounted(() => {
  subscription.value = MessageObserveService.subscribe((count) => {
    unreadCount.value = count;
  });
});
onBeforeUnmount(() => {
  MessageObserveService.unsubscribe(subscription.value!);
});

// 切换弹出时
const active = ref<'unread' | 'all'>('unread');
const infiniteScroll = ref();
const beforeShow = (): void => {
  active.value = 'unread';
};
const beforeHide = (): void => {
  infiniteScroll.value.stop();
  pageNum.value = 1;
  messages.value = [];
};

// 切换选项卡时
const loadLatestMessages = (): void => {
  pageNum.value = 1;
  messages.value = [];
  infiniteScroll.value.reset();
  infiniteScroll.value.resume();
  infiniteScroll.value.poll();
};

// 加载消息
const pageNum = ref<number>(1);
const messages = ref<Message[]>([]);
const loading = ref(false);
const loadMessages = (index: number, done: any) => {
  loading.value = true;
  MessageManageService.messages(pageNum.value, active.value === 'all')
    .then((response) => {
      const records = response?.records || [];
      if (index === 1) {
        messages.value.push(...records);
      } else {
        // 加载更多时，可能加载到已有消息
        const ids = messages.value.map((message) => message.id);
        messages.value.push(...records.filter((record) => !ids.includes(record.id)));
      }
      pageNum.value = index + 1;
      done(records.length < 10 || response?.total === messages.value.length);
    })
    .finally(() => (loading.value = false));
};

// 标记为已读
const read = (message: Message): void => {
  // 跳转到相关应用
  if (message.link) window.open(message.link);
  // 状态上报
  if (message.status === 0) {
    MessageManageService.read(message.id).then((response) => {
      if (response?.status === 'success') {
        // 全部消息列表
        if (active.value === 'all') {
          message.status = 1;
        }
        // 未读消息列表
        else {
          // 从列表中移除当前消息
          messages.value.splice(messages.value.indexOf(message), 1);
          // 检查滚动位置，必要时加载更多内容
          infiniteScroll.value.poll();
          // 当前列表为空时，加载最新数据
          if (messages.value.length === 0) loadLatestMessages();
        }
        MessageObserveService.next();
      }
    });
  }
};

// 全部标记为已读
const readAll = (): void => {
  if (unreadCount.value) {
    MessageManageService.readAll().then((response) => {
      if (response?.status === 'success') {
        loadLatestMessages();
        MessageObserveService.next();
        Notify.create({ message: response.message, type: 'positive', position: 'top' });
      } else {
        Notify.create({ message: response?.message, type: 'negative', position: 'top' });
      }
    });
  }
};

// 移除
const remove = (message: Message) => {
  MessageManageService.delete([message.id]).then((response) => {
    if (response?.status === 'success') {
      // 从列表中移除当前消息
      messages.value.splice(messages.value.indexOf(message), 1);
      // 检查滚动位置，必要时加载更多内容
      infiniteScroll.value.poll();
      // 当前列表为空时，加载最新数据
      if (messages.value.length === 0) loadLatestMessages();
      MessageObserveService.next();
      Notify.create({ message: response.message, type: 'positive', position: 'top' });
    } else {
      Notify.create({ message: response?.message, type: 'negative', position: 'top' });
    }
  });
};

// 清空
const removeAll = () => {
  if (messages.value.length) {
    MessageManageService.deleteAll().then((response) => {
      if (response?.status === 'success') {
        loadLatestMessages();
        MessageObserveService.next();
        Notify.create({ message: response.message, type: 'positive', position: 'top' });
      } else {
        Notify.create({ message: response?.message, type: 'negative', position: 'top' });
      }
    });
  }
};

// 获取用户头像
const avatar = (id: string): string => {
  return id ? AttachmentService.downloadFileUrl(id) : '';
};
</script>
