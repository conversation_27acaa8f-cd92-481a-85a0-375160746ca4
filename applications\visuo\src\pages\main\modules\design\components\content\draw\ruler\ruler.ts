import { computed, defineComponent, ref, watch } from 'vue';
import Guides from 'vue3-guides';
import { useDesignStore } from '../../../../stores';
import { useQuasar } from 'quasar';
import { GraphType, type GuidesInterface } from '@vis/document-core';
import { useGraph } from '../../../../hooks';

/**
 * 画布标尺
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-design-ruler',
  components: {
    'vue-guides': Guides
  },
  props: {},
  setup(props) {
    const designStore = useDesignStore();
    const rulerState = designStore.rulerState;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;

    //#region 标尺样式
    const $q = useQuasar();
    const isDark = computed(() => $q.dark.isActive);

    const ruleTheme = {
      light: {
        rule: { bg: '#f8f8fc', color: '#615e5e', selectedbg: '#bbdefbd9', selectedColor: '#1976d2' }
      },
      dark: {
        rule: { bg: '', color: '', selectedbg: '', selectedColor: '' }
      }
    };

    const theme = computed(() => {
      return isDark.value ? ruleTheme.dark : ruleTheme.light;
    });

    //#endregion

    const horizontalGuidesRef = ref<GuidesInterface>();
    const verticalGuidesRef = ref<GuidesInterface>();

    // 标尺选中的范围
    const horizontalGuidesSelectedRanges = computed(() => rulerState.value.selectedRangesH);
    const verticalGuidesSelectedRanges = computed(() => rulerState.value.selectedRangesV);

    const zoom = computed(() => rulerState.value.zoom);

    const unit = ref(50);

    // 初始参考线基准位置
    const defaultGuidesPos = computed(() => rulerState.value.defaultGuidesPos);
    // 初始滚动位置
    const defaultScrollPos = computed(() => rulerState.value.defaultScrollPos);

    // 根据缩放比例计算刻度间隔
    watch(
      () => zoom.value,
      () => {
        const base = 60 / zoom.value;
        // 计算最近的2的幂次方倍数
        const power = Math.round(Math.log2(base / 50));
        unit.value = Math.max(25, 50 * Math.pow(2, power));
      },
      { immediate: true }
    );

    const onResize = () => {
      horizontalGuidesRef.value?.resize();
      verticalGuidesRef.value?.resize();
    };

    /**
     * 添加、删除、移动是存储辅助线
     */
    const onChange = () => {
      const h = horizontalGuidesRef.value?.getGuides() || [];
      const v = verticalGuidesRef.value?.getGuides() || [];
      designStore.active.value.page.guides.horizontal = h;
      designStore.active.value.page.guides.vertical = v;
    };

    const onRestore = () => {
      horizontalGuidesRef.value?.scroll(0);
      horizontalGuidesRef.value?.scrollGuides(0);
      verticalGuidesRef.value?.scroll(0);
      verticalGuidesRef.value?.scrollGuides(0);
      infiniteCanvasRef.value?.scrollTo(0, 0);
    };

    const { findGraphParents } = useGraph();

    // 监听选中容器，容器改变后标尺0刻度的位置
    watch(
      () => designStore.active.value.frame,
      () => {
        const frame = designStore.active.value.frame;
        if (frame) {
          let x = 0,
            y = 0;

          const top = Math.round(infiniteCanvasRef.value?.getScrollTop() || 0);
          const left = Math.round(infiniteCanvasRef.value?.getScrollLeft() || 0);

          if (frame) {
            x = -frame.transform.translate[0] + left;
            y = -frame.transform.translate[1] + top;
            if (frame.parent) {
              const parents = findGraphParents(frame.id);
              parents.forEach((p) => {
                x -= p.transform.translate[0];
                y -= p.transform.translate[1];
              });
            }
          }

          horizontalGuidesRef.value?.scroll(x);
          verticalGuidesRef.value?.scroll(y);
        } else {
          horizontalGuidesRef.value?.scroll(0);
          verticalGuidesRef.value?.scroll(0);
        }
      },
      {
        deep: true
      }
    );

    // 监听选中元素，元素改变后标尺选中范围进行调整
    watch(
      () => designStore.active.value.graphIds,
      () => {
        const rangesH: number[][] = [];
        const rangesV: number[][] = [];
        const graphs = designStore.active.value.graphs;
        if (graphs.length === 1 && !graphs[0].parent && graphs[0].type === GraphType.Frame) {
          const w = graphs[0].width;
          const h = graphs[0].height;
          rangesH.push([0, w]);
          rangesV.push([0, h]);
        } else {
          designStore.active.value.graphs.forEach((graph) => {
            const w = graph.width;
            const h = graph.height;
            const x = graph.transform.translate[0];
            const y = graph.transform.translate[1];
            rangesH.push([x, x + w]);
            rangesV.push([y, y + h]);
          });
        }
        rulerState.value.selectedRangesH = rangesH;
        rulerState.value.selectedRangesV = rangesV;
      }
    );

    // 切换page时加载默认辅助线
    watch(
      () => designStore.active.value.page.id,
      () => {
        const guides = designStore.active.value.page.guides;
        horizontalGuidesRef.value?.loadGuides(guides.horizontal);
        verticalGuidesRef.value?.loadGuides(guides.vertical);
      },
      {
        immediate: true
      }
    );

    return {
      theme,
      horizontalGuidesRef,
      verticalGuidesRef,

      zoom,
      unit,

      defaultGuidesPos,
      defaultScrollPos,

      horizontalGuidesSelectedRanges,
      verticalGuidesSelectedRanges,

      onResize,
      onChange,
      onRestore
    };
  },
  mounted() {
    this.onResize();
    window.addEventListener('resize', this.onResize);

    const designStore = useDesignStore();
    designStore.horizontalGuidesRef.value = this.horizontalGuidesRef;
    designStore.verticalGuidesRef.value = this.verticalGuidesRef;
  },
  methods: {}
});
