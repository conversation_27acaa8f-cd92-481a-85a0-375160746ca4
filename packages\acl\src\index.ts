import type { App } from 'vue';
import { setupACLConfig, type ACLConfig } from './config';
import { ACLDirective } from './acl.directive';

export * from './acl';
export * from './acl.guard';
export * from './acl.service';
export * from './config';

export const ACLModule = {
  install(app: App, config?: ACLConfig) {
    config && setupACLConfig(config);

    // 注册指令
    app.directive(ACLDirective.name, ACLDirective);
  }
};
