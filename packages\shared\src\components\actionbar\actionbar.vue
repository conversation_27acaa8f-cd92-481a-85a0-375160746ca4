<template>
  <div class="ht-actionbar" :class="mini && 'is-mini'">
    <slot />
    <ht-apps-popup v-if="apps" />
    <ht-notifications-popup />
    <ht-account-popup />
  </div>
</template>
<style lang="scss" src="./actionbar.scss"></style>

<script setup lang="ts">
import HtAccountPopup from './popups/account.vue';
import HtAppsPopup from './popups/apps.vue';
import HtNotificationsPopup from './popups/notifications.vue';

defineOptions({ name: 'ht-actionbar' });

defineProps({
  mini: {
    type: Boolean,
    default: false
  },
  apps: {
    type: Boolean,
    default: true
  }
});
</script>
