import { ACLResource, type ACLType } from '@hetu/acl';
import { DirectoryACLResource, DirectoryACLResourceCode } from '@hetu/platform-app';

export interface CooperationACLDataType {
  id: string;

  privilegeGroup: string;

  privilegeName: string;

  resources: Record<string, string | string[]>;
}

export type CooperationACLType = ACLType & {
  /** 协作权限组 */
  group: string;
  /** 非协作资源 */
  regularResource?: Record<string, string | string[]>;
};

export enum CooperationACLResourceCode {
  /** 添加协作者 */
  AddCooperation = 'addCooperation',
  /** 修改协作 */
  EditCooperation = 'editCooperation',
  /** 移除协作者 */
  RemoveCooperation = 'removeCooperation',
  /** 查看协作 */
  LookCooperation = 'lookCooperation',
  /** 退出协作 */
  LeaveCooperation = 'leaveCooperation'
}

/**
 * 子文件(夹)
 */
export enum SubFileACLResourceCode {
  /** 添加协作者 */
  AddCooperation = 'addCooperation',
  /** 修改协作 */
  EditCooperation = 'editCooperation',
  /** 移除协作者 */
  RemoveCooperation = 'removeCooperation',
  /** 编辑文件夹 */
  EditDirectory = 'editDirectory',
  /** 删除文件夹 */
  DeleteDirectory = 'deleteDirectory'
}

export abstract class CoDirectoryACLResource<T> extends DirectoryACLResource<typeof CooperationACLResourceCode & T> {
  get toolbarDir() {
    const code = this.resourceCode as typeof DirectoryACLResourceCode & typeof CooperationACLResourceCode;

    return {
      resource: {
        [this.group as string]: [
          code.EditDirectory,
          code.MoveDirectory,
          code.LookCooperation,
          code.LeaveCooperation,
          code.DeleteDirectory
        ]
      },
      regularResource: {
        [this.group as string]: [code.SaveFavoriteDir, code.DeleteFavoriteDir]
      }
    } as CooperationACLType;
  }
}

export abstract class CooperationACLResource extends ACLResource<typeof CooperationACLResourceCode> {}

export abstract class SubFileACLResource extends ACLResource<typeof SubFileACLResourceCode> {
  private parentGroup: string;

  get toolbarDir() {
    const code = this.resourceCode as typeof SubFileACLResourceCode;
    return {
      resource: {
        [this.parentGroup]: [
          DirectoryACLResourceCode.MoveDirectory,
          CooperationACLResourceCode.LookCooperation,
          CooperationACLResourceCode.LeaveCooperation
        ],
        // 子文件(夹)操作
        [this.group]: [code.EditDirectory, code.DeleteDirectory]
      },
      regularResource: {
        [this.parentGroup]: [DirectoryACLResourceCode.SaveFavoriteDir, DirectoryACLResourceCode.DeleteFavoriteDir]
      }
    } as CooperationACLType;
  }

  constructor(parentGroup: string) {
    super();
    this.parentGroup = parentGroup;
  }
}
