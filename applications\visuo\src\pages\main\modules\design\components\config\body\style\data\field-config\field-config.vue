<template>
  <q-menu class="vis-menu w-[85px]" :offset="[0, 8]" @before-show="handleBeforeShow" @before-hide="handleBeforeHide">
    <q-scroll-area :style="{ height: '104px', maxHeight: '300px' }">
      <q-list>
        <q-item clickable v-close-popup @click="handleFilter">
          <q-item-section side class="!pr-1">
            <ht-icon name="vis-vertical_b" class="vis-icon"></ht-icon>
          </q-item-section>
          <q-item-section>筛选</q-item-section>
        </q-item>
        <q-item clickable v-close-popup @click="handleSort('asc')">
          <q-item-section side class="!pr-1">
            <ht-icon name="vis-asc" class="vis-icon"></ht-icon>
          </q-item-section>
          <q-item-section>升序</q-item-section>
        </q-item>
        <q-item clickable v-close-popup @click="handleSort('desc')">
          <q-item-section side class="!pr-1">
            <ht-icon name="vis-desc" class="vis-icon"></ht-icon>
          </q-item-section>
          <q-item-section>降序</q-item-section>
        </q-item>

        <!--   <q-item clickable>
          <q-item-section avatar>
            <div class="hticon-bi-fx"></div>
          </q-item-section>
          <q-item-section>数据格式</q-item-section>
        </q-item> -->
        <!-- 选择聚合函数 
        <q-expansion-item clickable>
          <template #header>
            <q-item-section avatar>
              <div class="hticon-bi-fx"></div>
            </q-item-section>
            <q-item-section>度量计算</q-item-section>
          </template>
          <q-list class="!p-0 !pt-1">
            <q-item
              clickable
              v-close-popup="!!fun.support"
              v-for="fun in aggregatorFunctions"
              :key="fun.function"
              :disable="!fun.support"
            >
              <q-item-section avatar class="text-right">
                <div :class="`text-xs w-6 hticon-bi-${fun.function.toLocaleLowerCase()}`"></div>
              </q-item-section>
              <q-item-section>{{ fun.name }}</q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>-->
      </q-list>
    </q-scroll-area>
  </q-menu>
</template>

<script lang="ts" src="./field-config.ts"></script>
